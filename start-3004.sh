#!/bin/bash

# 清理3004端口上的所有进程
echo "正在检查3004端口占用情况..."
PIDS=$(ss -tlnp | grep :3004 | grep -o 'pid=[0-9]*' | cut -d'=' -f2)

if [ ! -z "$PIDS" ]; then
    echo "发现占用3004端口的进程: $PIDS"
    for pid in $PIDS; do
        echo "终止进程 $pid"
        kill -9 $pid 2>/dev/null
    done
    sleep 2
else
    echo "3004端口未被占用"
fi

# 再次检查确保端口已释放
REMAINING=$(ss -tlnp | grep :3004)
if [ ! -z "$REMAINING" ]; then
    echo "警告: 仍有进程占用3004端口:"
    echo "$REMAINING"
    exit 1
fi

echo "3004端口已清理完毕，启动服务器..."

# 使用开发模式启动网站
echo "正在使用开发模式启动网站..."
npm run dev -- -p 3004

# 如果需要后台运行，请使用以下命令：
# nohup npm run dev -- -p 3004 > nohup.out 2>&1 &