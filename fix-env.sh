#!/bin/bash

echo "🚨 发现问题：Turbopack可能导致环境变量无法正确加载"
echo "============================================"

echo ""
echo "🔧 解决步骤："
echo "1. 停止当前开发服务器 (Ctrl+C)"
echo "2. 使用标准模式重新启动："
echo "   npm run dev"
echo ""
echo "3. 如果问题依然存在，手动重新创建 .env.local："

echo ""
echo "📝 创建新的 .env.local 文件："
cat > .env.local << 'EOL'
GEMINI_API_KEY=AIzaSyAql-N9-wjfH0cOqNd862L5lTebC6wQQ5o
KIMI_API_KEY=sk-ICzrvaJKR8kkIWIgGhBymFnAEgLQGqgsyxO4qTQWBc6Pmx3H
DEEPSEEK_API_KEY=***********************************
NODE_ENV=development
EOL

echo "✅ .env.local 文件已更新"

echo ""
echo "🧪 测试步骤："
echo "1. 重启服务器: npm run dev"
echo "2. 访问: http://localhost:3000/api/env-check"
echo "3. 如果环境变量正确加载，测试AI生成"

echo ""
echo "🎯 如果还是不行，使用硬编码测试："
echo "curl -X POST http://localhost:3000/api/hardcode-test \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"apiKey\":\"sk-ICzrvaJKR8kkIWIgGhBymFnAEgLQGqgsyxO4qTQWBc6Pmx3H\"}'"