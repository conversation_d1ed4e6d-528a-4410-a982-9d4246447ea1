{"name": "xujie-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev-turbo": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^9.88.0", "@react-three/fiber": "^8.15.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.53.0", "critters": "^0.0.23", "dotenv": "^17.2.1", "express": "^5.1.0", "framer-motion": "^12.23.12", "html2canvas": "^1.4.1", "lucide-react": "^0.536.0", "next": "^14.0.0", "nodemailer": "^6.9.13", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "sharp": "^0.34.3", "three": "^0.179.1", "tsx": "^4.20.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.2.5", "postcss": "^8.5.6", "tailwindcss": "^4", "typescript": "^5.9.2"}}