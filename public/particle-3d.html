<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆线宇宙 - 真3D粒子系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%);
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
        }
        
        #canvas {
            cursor: crosshair;
        }
        
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 100;
        }

        .glow {
            text-shadow: 0 0 10px currentColor;
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
    </div>
    <div id="loading">
        <h2 style="background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;" class="glow">
            🌌 启动真3D逆线宇宙
        </h2>
        <p>请稍候...</p>
    </div>

    <script>
        console.log('🚀 启动真3D粒子系统');

        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Canvas设置
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 3D数学工具
        class Vector3D {
            constructor(x = 0, y = 0, z = 0) {
                this.x = x;
                this.y = y;
                this.z = z;
            }

            add(v) {
                return new Vector3D(this.x + v.x, this.y + v.y, this.z + v.z);
            }

            subtract(v) {
                return new Vector3D(this.x - v.x, this.y - v.y, this.z - v.z);
            }

            multiply(scalar) {
                return new Vector3D(this.x * scalar, this.y * scalar, this.z * scalar);
            }

            length() {
                return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
            }

            normalize() {
                const len = this.length();
                return len > 0 ? new Vector3D(this.x / len, this.y / len, this.z / len) : new Vector3D();
            }
        }

        // 3D相机
        class Camera3D {
            constructor() {
                this.position = new Vector3D(0, 0, 500);
                this.rotation = { x: 0, y: 0 };
                this.fov = 60;
                this.velocity = new Vector3D();
                this.speed = 8;
                this.mouseSpeed = 0.003;
            }

            project(point3d) {
                // 相对于相机的位置
                const relative = point3d.subtract(this.position);
                
                // 应用旋转 (简化版)
                const cosY = Math.cos(this.rotation.y);
                const sinY = Math.sin(this.rotation.y);
                const cosX = Math.cos(this.rotation.x);
                const sinX = Math.sin(this.rotation.x);
                
                // Y轴旋转
                let x = relative.x * cosY - relative.z * sinY;
                let z = relative.x * sinY + relative.z * cosY;
                let y = relative.y;
                
                // X轴旋转
                const newY = y * cosX - z * sinX;
                z = y * sinX + z * cosX;
                y = newY;

                if (z <= 0) return null; // 在相机后面

                // 3D到2D投影
                const distance = (canvas.height / 2) / Math.tan((this.fov * Math.PI / 180) / 2);
                const scale = distance / z;
                
                return {
                    x: canvas.width / 2 + x * scale,
                    y: canvas.height / 2 - y * scale,
                    z: z,
                    scale: Math.max(0.1, Math.min(scale / 100, 3))
                };
            }

            update() {
                // 应用速度
                const forward = new Vector3D(-Math.sin(this.rotation.y), 0, -Math.cos(this.rotation.y));
                const right = new Vector3D(Math.cos(this.rotation.y), 0, -Math.sin(this.rotation.y));
                const up = new Vector3D(0, 1, 0);

                this.position = this.position.add(forward.multiply(this.velocity.z));
                this.position = this.position.add(right.multiply(this.velocity.x));
                this.position = this.position.add(up.multiply(this.velocity.y));

                // 减速
                this.velocity = this.velocity.multiply(0.88);
            }
        }

        // 粒子类
        class Particle3D {
            constructor() {
                this.position = new Vector3D(
                    (Math.random() - 0.5) * 4000,
                    (Math.random() - 0.5) * 4000,
                    (Math.random() - 0.5) * 4000
                );
                
                // 使用噪声函数确定颜色
                const noise = this.noise3D(this.position.x * 0.0008, this.position.y * 0.0008, this.position.z * 0.0008);
                
                if (noise > 0.3) {
                    this.color = '#FFD64F'; // 柠檬黄
                    this.baseSize = Math.random() * 6 + 4;
                } else if (noise > 0.0) {
                    this.color = '#C2A8F2'; // 紫晶色
                    this.baseSize = Math.random() * 4 + 3;
                } else {
                    this.color = '#666BCE'; // 紫色
                    this.baseSize = Math.random() * 3 + 2;
                }
                
                this.velocity = new Vector3D(
                    (Math.random() - 0.5) * 0.5,
                    (Math.random() - 0.5) * 0.5,
                    (Math.random() - 0.5) * 0.5
                );

                this.life = Math.random();
                this.lifeSpeed = Math.random() * 0.02 + 0.005;
            }

            noise3D(x, y, z) {
                return (Math.sin(x) * Math.cos(y) + Math.cos(y) * Math.sin(z) + Math.sin(z) * Math.cos(x)) / 3;
            }

            update() {
                this.position = this.position.add(this.velocity);
                this.life += this.lifeSpeed;
                
                // 循环生命
                if (this.life > 1) this.life = 0;
                
                // 边界检查，环绕
                if (Math.abs(this.position.x) > 2000) this.position.x *= -1;
                if (Math.abs(this.position.y) > 2000) this.position.y *= -1;
                if (Math.abs(this.position.z) > 2000) this.position.z *= -1;
            }

            render(camera) {
                const projected = camera.project(this.position);
                if (!projected) return;

                const alpha = Math.sin(this.life * Math.PI) * 0.8 + 0.2;
                const size = this.baseSize * projected.scale;
                
                // 发光效果
                ctx.shadowColor = this.color;
                ctx.shadowBlur = size * 2;
                ctx.globalAlpha = alpha;
                
                // 绘制粒子
                ctx.beginPath();
                ctx.fillStyle = this.color;
                ctx.arc(projected.x, projected.y, size, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制光晕
                ctx.globalAlpha = alpha * 0.3;
                ctx.beginPath();
                ctx.arc(projected.x, projected.y, size * 3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.shadowBlur = 0;
            }
        }

        // 初始化
        const camera = new Camera3D();
        const particles = [];
        const numParticles = 15000;

        for (let i = 0; i < numParticles; i++) {
            particles.push(new Particle3D());
        }

        // 控制
        const keys = {};
        let mouseX = 0, mouseY = 0;
        let isPointerLocked = false;

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            keys[e.code] = true;
        });

        document.addEventListener('keyup', (e) => {
            keys[e.code] = false;
        });

        // 鼠标事件
        canvas.addEventListener('click', () => {
            canvas.requestPointerLock();
        });

        document.addEventListener('pointerlockchange', () => {
            isPointerLocked = !!document.pointerLockElement;
        });

        document.addEventListener('mousemove', (e) => {
            if (isPointerLocked) {
                camera.rotation.y -= e.movementX * camera.mouseSpeed;
                camera.rotation.x -= e.movementY * camera.mouseSpeed;
                camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, camera.rotation.x));
            }
        });

        // 主渲染循环
        function animate() {
            requestAnimationFrame(animate);

            // 控制处理
            if (keys['KeyW'] || keys['ArrowUp']) camera.velocity.z -= camera.speed;
            if (keys['KeyS'] || keys['ArrowDown']) camera.velocity.z += camera.speed;
            if (keys['KeyA'] || keys['ArrowLeft']) camera.velocity.x -= camera.speed;
            if (keys['KeyD'] || keys['ArrowRight']) camera.velocity.x += camera.speed;
            if (keys['Space']) camera.velocity.y += camera.speed;
            if (keys['ShiftLeft']) camera.velocity.y -= camera.speed;

            camera.update();

            // 清屏
            ctx.fillStyle = 'rgba(10, 10, 26, 0.95)'; // 带透明度的拖尾效果
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 更新和渲染粒子
            particles.forEach(particle => {
                particle.update();
                particle.render(camera);
            });

            ctx.globalAlpha = 1;
        }

        // 隐藏加载提示并启动
        setTimeout(() => {
            document.getElementById('loading').style.display = 'none';
            console.log('✨ 真3D粒子系统启动完成');
            animate();
        }, 1000);

        // 性能信息
        let frameCount = 0;
        setInterval(() => {
            console.log(`FPS: ~${frameCount}, 粒子数: ${numParticles}, 相机位置: (${camera.position.x.toFixed(0)}, ${camera.position.y.toFixed(0)}, ${camera.position.z.toFixed(0)})`);
            frameCount = 0;
        }, 1000);

        function countFrame() {
            frameCount++;
            requestAnimationFrame(countFrame);
        }
        countFrame();
    </script>
</body>
</html>