<svg width="240" height="240" viewBox="0 0 240 240" xmlns="http://www.w3.org/2000/svg">
  <!-- 莫比乌斯环主体（强化交叉处颜色交融） -->
  <path d="M70,120 C70,80 110,60 150,80 C190,100 200,140 170,170 C140,200 80,190 70,150 C70,130 60,110 80,90 C100,70 130,70 150,90 C170,110 160,140 130,150 C100,160 80,140 80,120 C80,100 90,80 120,80 C150,80 160,110 160,130 C160,150 130,170 100,170 C70,170 50,140 50,110 C70,80 90,70 130,90" 
        fill="none" 
        stroke="url(#blendGradient)" 
        stroke-width="8" 
        stroke-linecap="round" 
        stroke-linejoin="round" />
  
  <!-- 交叉处高光（突出颜色交融） -->
  <path d="M130,90 C140,100 140,120 130,130" 
        fill="none" 
        stroke="rgba(255,255,255,0.5)" 
        stroke-width="4" 
        stroke-linecap="round" />
  
  <!-- 渐变定义（减少黄色占比，强化交叉融合） -->
  <defs>
    <linearGradient id="blendGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#666BCE" /> <!-- 幻海紫（占比增加） -->
      <stop offset="20%" stop-color="#666BCE" /> <!-- 延长紫色覆盖范围 -->
      <stop offset="60%" stop-color="#C2A8F2" /> <!-- 紫黄混合过渡色（交叉核心） -->
      <stop offset="100%" stop-color="#FFD64F" /> <!-- 柠檬黄（占比减少） -->
    </linearGradient>
  </defs>
</svg> 