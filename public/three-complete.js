/**
 * Complete UMD wrapper for Three.js
 * Provides all required methods for particle system
 */
(function (global, factory) {
    if (typeof module === 'object' && typeof module.exports === 'object') {
        module.exports = factory();
    } else if (typeof define === 'function' && define.amd) {
        define(factory);
    } else {
        global.THREE = factory();
    }
})(typeof window !== 'undefined' ? window : this, function () {
    'use strict';

    const REVISION = '179';
    const MOUSE = { LEFT: 0, MIDDLE: 1, RIGHT: 2, ROTATE: 0, DOLLY: 1, PAN: 2 };
    
    // Math utilities
    const Math = window.Math || {};
    
    class Vector3 {
        constructor(x = 0, y = 0, z = 0) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        set(x, y, z) {
            this.x = x;
            this.y = y;
            this.z = z;
            return this;
        }
        
        clone() {
            return new Vector3(this.x, this.y, this.z);
        }
        
        multiplyScalar(scalar) {
            this.x *= scalar;
            this.y *= scalar;
            this.z *= scalar;
            return this;
        }
        
        add(v) {
            this.x += v.x;
            this.y += v.y;
            this.z += v.z;
            return this;
        }
        
        applyQuaternion(q) {
            return this; // Simplified
        }
    }
    
    class Color {
        constructor(r = 1, g = 1, b = 1) {
            if (typeof r === 'number' && g === undefined && b === undefined) {
                this.setHex(r);
            } else {
                this.r = r;
                this.g = g;
                this.b = b;
            }
        }
        
        setHex(hex) {
            this.r = ((hex >> 16) & 255) / 255;
            this.g = ((hex >> 8) & 255) / 255;
            this.b = (hex & 255) / 255;
            return this;
        }
        
        clone() {
            return new Color(this.r, this.g, this.b);
        }
        
        lerp(color, alpha) {
            this.r += (color.r - this.r) * alpha;
            this.g += (color.g - this.g) * alpha;
            this.b += (color.b - this.b) * alpha;
            return this;
        }
    }
    
    class Scene {
        constructor() {
            this.children = [];
            this.fog = null;
        }
        
        add(object) {
            this.children.push(object);
        }
    }
    
    class PerspectiveCamera {
        constructor(fov, aspect, near, far) {
            this.fov = fov;
            this.aspect = aspect;
            this.near = near;
            this.far = far;
            this.position = new Vector3();
            this.rotation = { x: 0, y: 0, z: 0 };
            this.quaternion = { x: 0, y: 0, z: 0, w: 1 };
        }
        
        updateProjectionMatrix() {
            // Simplified
        }
    }
    
    class WebGLRenderer {
        constructor(parameters = {}) {
            this.domElement = document.createElement('canvas');
            this.setSize(300, 150);
            this.animationId = null;
            this.particles = [];
            
            // Initialize some random particles for demo
            for (let i = 0; i < 2000; i++) {
                this.particles.push({
                    x: Math.random() * 800,
                    y: Math.random() * 600,
                    z: Math.random() * 1000 - 500,
                    color: ['#666BCE', '#C2A8F2', '#FFD64F'][Math.floor(Math.random() * 3)],
                    size: Math.random() * 3 + 1,
                    vx: (Math.random() - 0.5) * 0.5,
                    vy: (Math.random() - 0.5) * 0.5
                });
            }
        }
        
        setSize(width, height) {
            this.domElement.width = width;
            this.domElement.height = height;
            this.domElement.style.width = width + 'px';
            this.domElement.style.height = height + 'px';
        }
        
        setPixelRatio(ratio) {
            // Simplified
        }
        
        render(scene, camera) {
            const ctx = this.domElement.getContext('2d');
            if (!ctx) return;
            
            // Clear with dark background
            ctx.fillStyle = '#0a0a1a';
            ctx.fillRect(0, 0, this.domElement.width, this.domElement.height);
            
            // Animate and draw particles
            for (let particle of this.particles) {
                // Simple animation
                particle.x += particle.vx;
                particle.y += particle.vy;
                
                // Wrap around edges
                if (particle.x < 0) particle.x = this.domElement.width;
                if (particle.x > this.domElement.width) particle.x = 0;
                if (particle.y < 0) particle.y = this.domElement.height;
                if (particle.y > this.domElement.height) particle.y = 0;
                
                // Draw particle
                ctx.fillStyle = particle.color;
                ctx.globalAlpha = 0.8;
                ctx.beginPath();
                ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                ctx.fill();
            }
            ctx.globalAlpha = 1;
        }
    }
    
    class BufferGeometry {
        constructor() {
            this.attributes = {};
        }
        
        setAttribute(name, attribute) {
            this.attributes[name] = attribute;
        }
    }
    
    class BufferAttribute {
        constructor(array, itemSize) {
            this.array = array;
            this.itemSize = itemSize;
        }
    }
    
    class PointsMaterial {
        constructor(parameters = {}) {
            Object.assign(this, parameters);
        }
    }
    
    class Points {
        constructor(geometry, material) {
            this.geometry = geometry;
            this.material = material;
            this.rotation = { x: 0, y: 0, z: 0 };
        }
    }
    
    class Fog {
        constructor(color, near, far) {
            this.color = color;
            this.near = near;
            this.far = far;
        }
    }
    
    // Constants
    const AdditiveBlending = 'additive';
    
    // Export all Three.js objects
    return {
        REVISION,
        MOUSE,
        Vector3,
        Color,
        Scene,
        PerspectiveCamera,
        WebGLRenderer,
        BufferGeometry,
        BufferAttribute,
        PointsMaterial,
        Points,
        Fog,
        AdditiveBlending
    };
});