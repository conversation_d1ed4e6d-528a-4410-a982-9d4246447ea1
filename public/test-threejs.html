<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Three.js 测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #0a0a1a;
            color: white;
        }
        #container {
            width: 100%;
            height: 80vh;
            border: 1px solid #666BCE;
            margin-top: 20px;
        }
        #info {
            background: rgba(102, 107, 206, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        button {
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 5px;
        }
        button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h1>Three.js 测试页面</h1>
    <p>这个页面用于测试基本的Three.js功能是否正常工作。</p>
    
    <button onclick="initScene()">初始化场景</button>
    <button onclick="addParticles()">添加粒子</button>
    <button onclick="animateScene()">开始动画</button>
    
    <div id="info">
        <h3>测试信息:</h3>
        <div id="webgl-support">WebGL支持检测中...</div>
        <div id="renderer-info">渲染器信息加载中...</div>
        <div id="performance-info">性能信息加载中...</div>
    </div>
    
    <div id="container"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer, particles;
        let animationId = null;
        
        // 检测WebGL支持
        function detectWebGLSupport() {
            try {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                const result = !!context;
                document.getElementById('webgl-support').innerHTML = 
                    result ? '✅ WebGL 支持正常' : '❌ WebGL 不支持';
                return result;
            } catch (e) {
                document.getElementById('webgl-support').innerHTML = '❌ WebGL 检测失败: ' + e.message;
                return false;
            }
        }
        
        // 初始化场景
        function initScene() {
            const container = document.getElementById('container');
            
            // 清空容器
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0a0a1a);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
            camera.position.z = 5;
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            container.appendChild(renderer.domElement);
            
            // 显示渲染器信息
            const info = renderer.info;
            document.getElementById('renderer-info').innerHTML = 
                `渲染器: ${renderer.context.getParameter(renderer.context.VERSION)}<br>
                 GPU: ${renderer.context.getParameter(renderer.context.RENDERER)}<br>
                 供应商: ${renderer.context.getParameter(renderer.context.VENDOR)}`;
            
            console.log('场景初始化完成');
        }
        
        // 添加粒子
        function addParticles() {
            if (!scene) {
                alert('请先初始化场景');
                return;
            }
            
            // 移除旧粒子
            if (particles) {
                scene.remove(particles);
            }
            
            // 创建粒子系统
            const particleCount = 5000;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            
            // 生成粒子位置和颜色
            for (let i = 0; i < particleCount; i++) {
                positions[i * 3] = (Math.random() - 0.5) * 10;
                positions[i * 3 + 1] = (Math.random() - 0.5) * 10;
                positions[i * 3 + 2] = (Math.random() - 0.5) * 10;
                
                colors[i * 3] = Math.random() * 0.5 + 0.5; // R
                colors[i * 3 + 1] = Math.random() * 0.5 + 0.5; // G
                colors[i * 3 + 2] = Math.random() * 0.8 + 0.2; // B
            }
            
            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
            
            const material = new THREE.PointsMaterial({
                size: 0.1,
                vertexColors: true,
                transparent: true,
                opacity: 0.9
            });
            
            particles = new THREE.Points(geometry, material);
            scene.add(particles);
            
            console.log(`添加了 ${particleCount} 个粒子`);
        }
        
        // 动画循环
        function animateScene() {
            if (!scene || !camera || !renderer) {
                alert('请先初始化场景');
                return;
            }
            
            // 停止之前的动画
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            const startTime = performance.now();
            let frameCount = 0;
            
            function animate() {
                animationId = requestAnimationFrame(animate);
                
                // 旋转粒子系统
                if (particles) {
                    particles.rotation.x += 0.001;
                    particles.rotation.y += 0.002;
                }
                
                renderer.render(scene, camera);
                
                // 性能监控
                frameCount++;
                if (frameCount % 60 === 0) {
                    const elapsed = performance.now() - startTime;
                    const fps = Math.round((frameCount / elapsed) * 1000);
                    document.getElementById('performance-info').innerHTML = 
                        `帧率: ${fps} FPS<br>渲染调用: ${renderer.info.render.calls}<br>三角形数量: ${renderer.info.render.triangles}`;
                }
            }
            
            animate();
        }
        
        // 页面加载完成后检测WebGL支持
        window.addEventListener('load', function() {
            detectWebGLSupport();
        });
        
        // 窗口大小调整
        window.addEventListener('resize', function() {
            if (camera && renderer) {
                const container = document.getElementById('container');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });
    </script>
</body>
</html>