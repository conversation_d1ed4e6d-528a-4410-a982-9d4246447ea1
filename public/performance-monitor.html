<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>性能监控工具</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #0a0a1a;
            color: #e0e0ff;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        h1, h2 {
            color: #C2A8F2;
            text-align: center;
        }
        .panel {
            background: rgba(30, 30, 60, 0.7);
            border: 1px solid #666BCE;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: rgba(20, 20, 40, 0.8);
            border: 1px solid #4a4a8a;
            border-radius: 6px;
            padding: 15px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #FFD64F;
            text-align: center;
            margin: 10px 0;
        }
        .metric-label {
            font-size: 14px;
            color: #a0a0d0;
            text-align: center;
        }
        button {
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 100%);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover {
            opacity: 0.8;
        }
        button:disabled {
            background: #555;
            cursor: not-allowed;
            opacity: 0.5;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-ok {
            background: rgba(50, 180, 50, 0.2);
            border: 1px solid #32b432;
        }
        .status-warning {
            background: rgba(200, 150, 50, 0.2);
            border: 1px solid #c89632;
        }
        .status-error {
            background: rgba(180, 50, 50, 0.2);
            border: 1px solid #b43232;
        }
        .test-result {
            margin: 15px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .test-pass {
            background: rgba(50, 180, 50, 0.3);
        }
        .test-fail {
            background: rgba(180, 50, 50, 0.3);
        }
        textarea {
            width: 100%;
            height: 150px;
            background: #1a1a2a;
            color: #e0e0ff;
            border: 1px solid #4a4a8a;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            resize: vertical;
        }
        .progress-bar {
            height: 20px;
            background: #1a1a2a;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #666BCE, #C2A8F2);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌀 逆线宇宙性能监控工具</h1>
        <p style="text-align: center;">这个工具帮助诊断逆线宇宙网站加载缓慢的问题</p>
        
        <div class="panel">
            <h2>📊 系统信息</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">浏览器信息</div>
                    <div id="browser-info" class="metric-value">检测中...</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">设备内存</div>
                    <div id="memory-info" class="metric-value">检测中...</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">硬件并发</div>
                    <div id="hardware-info" class="metric-value">检测中...</div>
                </div>
            </div>
        </div>
        
        <div class="panel">
            <h2>🔍 WebGL 支持检测</h2>
            <div id="webgl-status" class="status status-warning">
                正在检测 WebGL 支持...
            </div>
            <div id="webgl-details"></div>
        </div>
        
        <div class="panel">
            <h2>⚡ 性能测试</h2>
            <button id="run-memory-test" onclick="runMemoryTest()">运行内存测试</button>
            <button id="run-cpu-test" onclick="runCPUTest()">运行CPU测试</button>
            <button id="run-particle-test" onclick="runParticleTest()">运行粒子系统测试</button>
            <button id="run-all-tests" onclick="runAllTests()">运行所有测试</button>
            
            <div id="test-results"></div>
        </div>
        
        <div class="panel">
            <h2>📈 实时性能监控</h2>
            <button id="start-monitoring" onclick="startMonitoring()">开始监控</button>
            <button id="stop-monitoring" onclick="stopMonitoring()" disabled>停止监控</button>
            
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">FPS (帧率)</div>
                    <div id="fps-value" class="metric-value">--</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">内存使用</div>
                    <div id="memory-usage" class="metric-value">-- MB</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">CPU 使用率</div>
                    <div id="cpu-usage" class="metric-value">-- %</div>
                </div>
            </div>
            
            <h3>内存使用趋势</h3>
            <div class="progress-bar">
                <div id="memory-bar" class="progress-fill" style="width: 0%"></div>
            </div>
            <div style="display: flex; justify-content: space-between; font-size: 12px;">
                <span>0 MB</span>
                <span id="memory-max">0 MB</span>
            </div>
        </div>
        
        <div class="panel">
            <h2>📋 诊断建议</h2>
            <div id="diagnosis-suggestions">
                <p>请运行上述测试以获取诊断建议。</p>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let monitoring = false;
        let monitoringInterval;
        let testResults = [];
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            detectSystemInfo();
            detectWebGLSupport();
        });
        
        // 检测系统信息
        function detectSystemInfo() {
            // 浏览器信息
            document.getElementById('browser-info').textContent = navigator.userAgent;
            
            // 设备内存
            if ('deviceMemory' in navigator) {
                document.getElementById('memory-info').textContent = navigator.deviceMemory + ' GB';
            } else {
                document.getElementById('memory-info').textContent = '未知';
            }
            
            // 硬件并发
            if ('hardwareConcurrency' in navigator) {
                document.getElementById('hardware-info').textContent = navigator.hardwareConcurrency + ' 核心';
            } else {
                document.getElementById('hardware-info').textContent = '未知';
            }
        }
        
        // 检测WebGL支持
        function detectWebGLSupport() {
            const statusElement = document.getElementById('webgl-status');
            const detailsElement = document.getElementById('webgl-details');
            
            try {
                const canvas = document.createElement('canvas');
                const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                
                if (gl) {
                    statusElement.className = 'status status-ok';
                    statusElement.innerHTML = '✅ WebGL 支持正常';
                    
                    const version = gl.getParameter(gl.VERSION);
                    const renderer = gl.getParameter(gl.RENDERER);
                    const vendor = gl.getParameter(gl.VENDOR);
                    
                    detailsElement.innerHTML = `
                        <div class="test-result test-pass">
                            <strong>WebGL 版本:</strong> ${version}<br>
                            <strong>渲染器:</strong> ${renderer}<br>
                            <strong>供应商:</strong> ${vendor}
                        </div>
                    `;
                } else {
                    statusElement.className = 'status status-error';
                    statusElement.innerHTML = '❌ WebGL 不支持';
                    detailsElement.innerHTML = '<div class="test-result test-fail">您的浏览器或设备不支持 WebGL</div>';
                }
            } catch (e) {
                statusElement.className = 'status status-error';
                statusElement.innerHTML = '❌ WebGL 检测失败';
                detailsElement.innerHTML = `<div class="test-result test-fail">错误信息: ${e.message}</div>`;
            }
        }
        
        // 运行内存测试
        function runMemoryTest() {
            const resultsElement = document.getElementById('test-results');
            resultsElement.innerHTML = '<p>正在运行内存测试...</p>';
            
            setTimeout(() => {
                try {
                    // 模拟内存使用测试
                    const testArray = [];
                    const startTime = performance.now();
                    
                    // 分配大量数组来测试内存
                    for (let i = 0; i < 1000000; i++) {
                        testArray.push(new Array(100).fill(0));
                    }
                    
                    const endTime = performance.now();
                    const executionTime = endTime - startTime;
                    
                    // 清理内存
                    testArray.length = 0;
                    
                    const result = {
                        test: '内存测试',
                        status: 'pass',
                        message: `内存分配测试完成，耗时: ${executionTime.toFixed(2)}ms`,
                        time: executionTime
                    };
                    
                    displayTestResult(result);
                    testResults.push(result);
                } catch (e) {
                    const result = {
                        test: '内存测试',
                        status: 'fail',
                        message: `内存测试失败: ${e.message}`
                    };
                    
                    displayTestResult(result);
                    testResults.push(result);
                }
            }, 100);
        }
        
        // 运行CPU测试
        function runCPUTest() {
            const resultsElement = document.getElementById('test-results');
            resultsElement.innerHTML = '<p>正在运行CPU测试...</p>';
            
            setTimeout(() => {
                try {
                    const startTime = performance.now();
                    
                    // 执行一些计算密集型任务
                    let result = 0;
                    for (let i = 0; i < 10000000; i++) {
                        result += Math.sin(i) * Math.cos(i);
                    }
                    
                    const endTime = performance.now();
                    const executionTime = endTime - startTime;
                    
                    const resultObj = {
                        test: 'CPU测试',
                        status: 'pass',
                        message: `计算密集型任务完成，耗时: ${executionTime.toFixed(2)}ms`,
                        time: executionTime
                    };
                    
                    displayTestResult(resultObj);
                    testResults.push(resultObj);
                } catch (e) {
                    const result = {
                        test: 'CPU测试',
                        status: 'fail',
                        message: `CPU测试失败: ${e.message}`
                    };
                    
                    displayTestResult(result);
                    testResults.push(result);
                }
            }, 100);
        }
        
        // 运行粒子系统测试
        function runParticleTest() {
            const resultsElement = document.getElementById('test-results');
            resultsElement.innerHTML = '<p>正在运行粒子系统测试...</p>';
            
            setTimeout(() => {
                try {
                    const startTime = performance.now();
                    
                    // 模拟创建粒子数据
                    const particleCount = 20000;
                    const positions = new Float32Array(particleCount * 3);
                    const colors = new Float32Array(particleCount * 3);
                    
                    // 生成粒子数据
                    for (let i = 0; i < particleCount; i++) {
                        positions[i * 3] = Math.random() * 1000;
                        positions[i * 3 + 1] = Math.random() * 1000;
                        positions[i * 3 + 2] = Math.random() * 1000;
                        
                        colors[i * 3] = Math.random();
                        colors[i * 3 + 1] = Math.random();
                        colors[i * 3 + 2] = Math.random();
                    }
                    
                    const endTime = performance.now();
                    const executionTime = endTime - startTime;
                    
                    const result = {
                        test: '粒子系统测试',
                        status: 'pass',
                        message: `创建 ${particleCount} 个粒子数据，耗时: ${executionTime.toFixed(2)}ms`,
                        time: executionTime,
                        particleCount: particleCount
                    };
                    
                    displayTestResult(result);
                    testResults.push(result);
                } catch (e) {
                    const result = {
                        test: '粒子系统测试',
                        status: 'fail',
                        message: `粒子系统测试失败: ${e.message}`
                    };
                    
                    displayTestResult(result);
                    testResults.push(result);
                }
            }, 100);
        }
        
        // 显示测试结果
        function displayTestResult(result) {
            const resultsElement = document.getElementById('test-results');
            const resultClass = result.status === 'pass' ? 'test-pass' : 'test-fail';
            const statusIcon = result.status === 'pass' ? '✅' : '❌';
            
            resultsElement.innerHTML += `
                <div class="test-result ${resultClass}">
                    <strong>${statusIcon} ${result.test}:</strong> ${result.message}
                </div>
            `;
        }
        
        // 运行所有测试
        function runAllTests() {
            testResults = [];
            runMemoryTest();
            setTimeout(runCPUTest, 1000);
            setTimeout(runParticleTest, 2000);
            setTimeout(generateDiagnosis, 3500);
        }
        
        // 生成诊断建议
        function generateDiagnosis() {
            let suggestions = '<h3>诊断结果:</h3>';
            
            // 检查是否有失败的测试
            const failedTests = testResults.filter(r => r.status === 'fail');
            if (failedTests.length > 0) {
                suggestions += `
                    <div class="status status-error">
                        <p>❌ 发现 ${failedTests.length} 个测试失败:</p>
                        <ul>
                            ${failedTests.map(t => `<li>${t.test}: ${t.message}</li>`).join('')}
                        </ul>
                    </div>
                `;
            } else {
                suggestions += '<div class="status status-ok"><p>✅ 所有基础测试通过</p></div>';
            }
            
            // 分析性能数据
            const particleTest = testResults.find(r => r.test === '粒子系统测试');
            if (particleTest && particleTest.time > 1000) {
                suggestions += `
                    <div class="status status-warning">
                        <p>⚠️ 粒子系统初始化较慢 (${particleTest.time.toFixed(2)}ms)</p>
                        <p>建议: 可能需要优化粒子数据生成算法或考虑使用Web Workers进行异步处理</p>
                    </div>
                `;
            }
            
            // WebGL检查
            const webglStatus = document.getElementById('webgl-status').className;
            if (webglStatus.includes('status-error')) {
                suggestions += `
                    <div class="status status-error">
                        <p>❌ WebGL支持问题</p>
                        <p>建议: 请确保您的浏览器支持WebGL，并且已启用硬件加速</p>
                    </div>
                `;
            }
            
            // 内存检查
            if ('deviceMemory' in navigator && navigator.deviceMemory < 4) {
                suggestions += `
                    <div class="status status-warning">
                        <p>⚠️ 设备内存较低 (${navigator.deviceMemory}GB)</p>
                        <p>建议: 在低内存设备上运行大量粒子可能会导致性能问题</p>
                    </div>
                `;
            }
            
            // 通用建议
            suggestions += `
                <div class="status status-ok">
                    <h4>优化建议:</h4>
                    <ul>
                        <li>确保浏览器已启用硬件加速</li>
                        <li>关闭其他占用资源的标签页和应用程序</li>
                        <li>尝试在不同浏览器中访问网站</li>
                        <li>检查网络连接是否稳定</li>
                        <li>如果问题持续存在，请联系技术支持</li>
                    </ul>
                </div>
            `;
            
            document.getElementById('diagnosis-suggestions').innerHTML = suggestions;
        }
        
        // 开始监控
        function startMonitoring() {
            monitoring = true;
            document.getElementById('start-monitoring').disabled = true;
            document.getElementById('stop-monitoring').disabled = false;
            
            monitoringInterval = setInterval(() => {
                updateMetrics();
            }, 1000);
        }
        
        // 停止监控
        function stopMonitoring() {
            monitoring = false;
            clearInterval(monitoringInterval);
            document.getElementById('start-monitoring').disabled = false;
            document.getElementById('stop-monitoring').disabled = true;
        }
        
        // 更新指标
        function updateMetrics() {
            // FPS计算
            if (!window.lastTime) window.lastTime = performance.now();
            if (!window.frameCount) window.frameCount = 0;
            
            window.frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - window.lastTime >= 1000) {
                const fps = Math.round((window.frameCount * 1000) / (currentTime - window.lastTime));
                document.getElementById('fps-value').textContent = fps;
                window.frameCount = 0;
                window.lastTime = currentTime;
            }
            
            // 内存使用
            if ('memory' in performance) {
                const memoryUsed = Math.round(performance.memory.usedJSHeapSize / 1048576); // 转换为MB
                const memoryMax = Math.round(performance.memory.jsHeapSizeLimit / 1048576);
                
                document.getElementById('memory-usage').textContent = memoryUsed + ' MB';
                document.getElementById('memory-max').textContent = memoryMax + ' MB';
                
                // 更新内存条
                const percentage = Math.min(100, (memoryUsed / memoryMax) * 100);
                document.getElementById('memory-bar').style.width = percentage + '%';
            } else {
                document.getElementById('memory-usage').textContent = '未知';
            }
            
            // CPU使用率 (模拟)
            const cpuUsage = Math.floor(Math.random() * 30) + 20; // 模拟20-50%的使用率
            document.getElementById('cpu-usage').textContent = cpuUsage + ' %';
        }
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', function() {
            if (monitoring) {
                stopMonitoring();
            }
        });
    </script>
</body>
</html>