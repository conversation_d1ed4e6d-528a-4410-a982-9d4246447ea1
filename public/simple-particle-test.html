<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化粒子系统测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #0a0a1a;
            color: white;
        }
        #container {
            width: 100%;
            height: 70vh;
            border: 1px solid #666BCE;
            margin-top: 20px;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: rgba(102, 107, 206, 0.2);
            border-radius: 8px;
        }
        button {
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 100%);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
        }
        button:hover {
            opacity: 0.8;
        }
        .info-panel {
            background: rgba(20, 20, 40, 0.8);
            border: 1px solid #4a4a8a;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        .progress-bar {
            height: 20px;
            background: #1a1a2a;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #666BCE, #C2A8F2);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>简化粒子系统测试</h1>
    <p>这个页面用于测试粒子系统的性能，帮助诊断加载缓慢的问题。</p>
    
    <div class="controls">
        <button onclick="initTest(1000)">1,000 粒子</button>
        <button onclick="initTest(5000)">5,000 粒子</button>
        <button onclick="initTest(10000)">10,000 粒子</button>
        <button onclick="initTest(20000)">20,000 粒子</button>
        <button onclick="clearScene()">清空场景</button>
    </div>
    
    <div class="info-panel">
        <h3>测试信息:</h3>
        <div id="test-info">
            请选择粒子数量开始测试
        </div>
        <div>进度:</div>
        <div class="progress-bar">
            <div id="progress-fill" class="progress-fill" style="width: 0%"></div>
        </div>
    </div>
    
    <div id="container"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script>
        let scene, camera, renderer, particles;
        let animationId = null;
        
        // 初始化测试
        function initTest(particleCount) {
            updateInfo(`开始初始化 ${particleCount} 个粒子...`);
            updateProgress(0);
            
            // 延迟执行以允许UI更新
            setTimeout(() => {
                createParticleSystem(particleCount);
            }, 100);
        }
        
        // 创建粒子系统
        function createParticleSystem(particleCount) {
            const startTime = performance.now();
            
            // 清空现有场景
            clearScene();
            
            // 创建容器
            const container = document.getElementById('container');
            
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x0a0a1a);
            
            // 创建相机
            camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 5000);
            camera.position.z = 500;
            
            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: false });
            renderer.setSize(container.clientWidth, container.clientHeight);
            renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
            
            // 清空容器并添加渲染器
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }
            container.appendChild(renderer.domElement);
            
            updateInfo(`正在生成 ${particleCount} 个粒子...`);
            
            // 创建粒子几何体
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);
            const sizes = new Float32Array(particleCount);
            
            // 生成粒子数据（分批处理以更新进度）
            const batchSize = Math.max(1, Math.floor(particleCount / 100));
            let processed = 0;
            
            const processBatch = () => {
                const end = Math.min(processed + batchSize, particleCount);
                
                for (let i = processed; i < end; i++) {
                    // 位置
                    positions[i * 3] = (Math.random() - 0.5) * 2000;
                    positions[i * 3 + 1] = (Math.random() - 0.5) * 2000;
                    positions[i * 3 + 2] = (Math.random() - 0.5) * 2000;
                    
                    // 颜色
                    const r = Math.random() * 0.5 + 0.5;
                    const g = Math.random() * 0.5 + 0.5;
                    const b = Math.random() * 0.8 + 0.2;
                    colors[i * 3] = r;
                    colors[i * 3 + 1] = g;
                    colors[i * 3 + 2] = b;
                    
                    // 大小
                    sizes[i] = Math.random() * 5 + 2;
                }
                
                processed = end;
                updateProgress((processed / particleCount) * 100);
                
                if (processed < particleCount) {
                    // 继续处理下一批
                    setTimeout(processBatch, 1);
                } else {
                    // 完成处理
                    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
                    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
                    
                    const material = new THREE.PointsMaterial({
                        size: 4.6,
                        vertexColors: true,
                        transparent: true,
                        blending: THREE.AdditiveBlending,
                        sizeAttenuation: true,
                        depthWrite: false,
                        opacity: 0.95
                    });
                    
                    particles = new THREE.Points(geometry, material);
                    scene.add(particles);
                    
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    updateInfo(`✅ 成功创建 ${particleCount} 个粒子，耗时: ${duration.toFixed(2)}ms`);
                    
                    // 开始动画
                    animate();
                }
            };
            
            // 开始处理
            processBatch();
        }
        
        // 动画循环
        function animate() {
            if (!scene || !camera || !renderer) return;
            
            // 停止之前的动画
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
            
            function render() {
                animationId = requestAnimationFrame(render);
                
                // 旋转粒子系统
                if (particles) {
                    particles.rotation.x += 0.001;
                    particles.rotation.y += 0.002;
                }
                
                renderer.render(scene, camera);
            }
            
            render();
        }
        
        // 清空场景
        function clearScene() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
            
            if (renderer) {
                renderer.dispose();
                renderer = null;
            }
            
            if (particles) {
                particles.geometry.dispose();
                particles.material.dispose();
                particles = null;
            }
            
            scene = null;
            camera = null;
            
            const container = document.getElementById('container');
            while (container.firstChild) {
                container.removeChild(container.firstChild);
            }
            
            updateInfo('场景已清空');
            updateProgress(0);
        }
        
        // 更新信息
        function updateInfo(message) {
            document.getElementById('test-info').innerHTML = message;
        }
        
        // 更新进度条
        function updateProgress(percentage) {
            document.getElementById('progress-fill').style.width = percentage + '%';
        }
        
        // 窗口大小调整
        window.addEventListener('resize', function() {
            if (camera && renderer) {
                const container = document.getElementById('container');
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });
    </script>
</body>
</html>