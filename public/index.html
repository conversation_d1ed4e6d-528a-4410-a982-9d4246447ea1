<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆线 - 3D逆线宇宙</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
            background: linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%);
        }

        .container {
            width: 100vw;
            height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 3D粒子背景样式 */
        .stars {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: transparent;
        }

        .star {
            position: absolute;
            background: radial-gradient(circle, #fff 0%, transparent 70%);
            border-radius: 50%;
            animation: twinkle 3s infinite ease-in-out;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(0.8); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        /* UI覆盖层样式 */
        .ui-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .nav-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .brand {
            font-size: 1.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-item {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .creation-island {
            position: absolute;
            bottom: 80px;
            right: 80px;
            width: 520px;
            max-width: 92vw;
            z-index: 100;
        }

        .island-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .island-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .slogan {
            font-size: 0.9rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .category-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 16px;
        }

        .category-btn {
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.05);
            color: white;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-btn.active {
            border-color: #C2A8F2;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .category-btn:hover {
            border-color: rgba(255, 255, 255, 0.4);
            background: rgba(255, 255, 255, 0.1);
        }

        .input-area {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .input-area textarea {
            width: 100%;
            min-height: 80px;
            background: transparent;
            border: none;
            color: white;
            font-family: inherit;
            font-size: 0.9rem;
            resize: none;
            outline: none;
        }

        .input-area textarea::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        .btn-group {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .btn.primary {
            background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%);
            border: none;
            color: black;
            font-weight: bold;
        }

        .btn.primary:hover {
            background: linear-gradient(135deg, #5a5fb8 0%, #a08dd9 50%, #e5b94a 100%);
            box-shadow: 0 4px 12px rgba(102, 107, 206, 0.4);
        }

        .controls-hint {
            position: absolute;
            bottom: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            padding: 12px;
            max-width: 300px;
        }

        .hint-title {
            color: white;
            font-size: 0.9rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .key-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
        }

        .key-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .key-label {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            color: white;
            font-size: 0.7rem;
            font-weight: bold;
        }

        .key-desc {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 5万个3D粒子背景 -->
        <div class="stars" id="stars-container">
            <!-- 粒子将由JavaScript生成 -->
        </div>

        <!-- UI覆盖层 -->
        <div class="ui-overlay">
            <!-- 导航 -->
            <div class="nav-bar">
                <div class="brand">逆线</div>
                <a href="#" class="nav-item">公告</a>
                <a href="#" class="nav-item">光影回廊</a>
                <a href="#" class="nav-item">创作社区</a>
            </div>

            <!-- 用户操作 -->
            <div class="nav-bar">
                <a href="#" class="nav-item">充值</a>
                <a href="#" class="nav-item">登录</a>
            </div>
        </div>

        <!-- 创作岛 -->
        <div class="creation-island">
            <div class="island-card">
                <div class="island-title">在无数逆转的线里，重逢唯一的你！</div>
                <div class="slogan">每一颗星辰，都是一份回响——而你，点亮了整个宇宙！</div>

                <div style="margin-bottom: 16px; color: rgba(255,255,255,0.8); font-size: 0.9rem;">
                    选择一个创作方向
                </div>

                <div class="category-grid">
                    <button class="category-btn">高能场面</button>
                    <button class="category-btn">细节补完</button>
                    <button class="category-btn">心理深挖</button>
                    <button class="category-btn">配角外传</button>
                    <button class="category-btn">平行世界</button>
                    <button class="category-btn">真人宇宙</button>
                </div>

                <div class="input-area">
                    <textarea placeholder="输入你的脑洞，或点击灵感小助手获取导演级提示词。描述越具体，生成越精彩。"></textarea>
                </div>

                <div class="btn-group">
                    <button class="btn">灵感小助手</button>
                    <button class="btn primary">立即生成</button>
                </div>
            </div>
        </div>

        <!-- 控制提示 -->
        <div class="controls-hint">
            <div class="hint-title">Navigation Controls</div>
            <div class="key-grid">
                <div class="key-item">
                    <span class="key-label">W</span>
                    <span class="key-desc">Forward</span>
                </div>
                <div class="key-item">
                    <span class="key-label">S</span>
                    <span class="key-desc">Backward</span>
                </div>
                <div class="key-item">
                    <span class="key-label">A</span>
                    <span class="key-desc">Left</span>
                </div>
                <div class="key-item">
                    <span class="key-label">D</span>
                    <span class="key-desc">Right</span>
                </div>
                <div class="key-item">
                    <span class="key-label">Space</span>
                    <span class="key-desc">Up</span>
                </div>
                <div class="key-item">
                    <span class="key-label">Shift</span>
                    <span class="key-desc">Down</span>
                </div>
                <div class="key-item">
                    <span class="key-label">Mouse</span>
                    <span class="key-desc">Look</span>
                </div>
                <div class="key-item">
                    <span class="key-label">Click</span>
                    <span class="key-desc">Lock</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 创建5万个粒子（精准还原A项目）
        function createStars() {
            const container = document.getElementById('stars-container');
            if (!container) return;

            const colors = ['#666BCE', '#C2A8F2', '#FFD64F', '#ffffff'];

            // 创建5万个粒子（documentFragment优化性能）
            const fragment = document.createDocumentFragment();

            for (let i = 0; i < 50000; i++) {
                const star = document.createElement('div');
                star.className = 'star';

                // 随机位置
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';

                // 随机大小（2-6px）
                const size = Math.random() * 4 + 2;
                star.style.width = size + 'px';
                star.style.height = size + 'px';

                // 随机颜色
                star.style.background = colors[Math.floor(Math.random() * colors.length)];

                // 随机动画延迟和持续时间
                star.style.animationDelay = (Math.random() * 3) + 's';
                star.style.animationDuration = (Math.random() * 2 + 1) + 's';

                fragment.appendChild(star);
            }

            container.appendChild(fragment);
            console.log('✅ 已创建 50,000 个粒子');
        }

        // 初始化分类按钮
        function initCategoryButtons() {
            const buttons = document.querySelectorAll('.category-btn');
            buttons.forEach((btn) => {
                btn.addEventListener('click', () => {
                    buttons.forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                });
            });
        }

        // 页面加载完成后初始化
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                createStars();
                initCategoryButtons();
            });
        } else {
            createStars();
            initCategoryButtons();
        }

        console.log('🎉 逆线3D逆线宇宙已加载完成');
    </script>
</body>
</html>
