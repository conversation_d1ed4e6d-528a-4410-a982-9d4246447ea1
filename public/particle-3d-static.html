<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆线宇宙 - 非交互3D粒子</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%);
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
        }
        
        #canvas {
            pointer-events: none; /* 禁用交互 */
        }
    </style>
</head>
<body>
    <div id="container">
        <canvas id="canvas"></canvas>
    </div>

    <script>
        console.log('🌟 启动非交互3D星空背景');

        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Canvas设置
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 3D数学工具
        class Vector3D {
            constructor(x = 0, y = 0, z = 0) {
                this.x = x;
                this.y = y;
                this.z = z;
            }

            add(v) {
                return new Vector3D(this.x + v.x, this.y + v.y, this.z + v.z);
            }

            subtract(v) {
                return new Vector3D(this.x - v.x, this.y - v.y, this.z - v.z);
            }

            multiply(scalar) {
                return new Vector3D(this.x * scalar, this.y * scalar, this.z * scalar);
            }

            length() {
                return Math.sqrt(this.x * this.x + this.y * this.y + this.z * this.z);
            }
        }

        // 自动旋转相机（无用户控制）
        class AutoCamera3D {
            constructor() {
                this.position = new Vector3D(0, 0, 800);
                this.rotation = { x: 0, y: 0 };
                this.fov = 60;
                this.time = 0;
            }

            project(point3d) {
                // 相对于相机的位置
                const relative = point3d.subtract(this.position);
                
                // 应用自动旋转
                const cosY = Math.cos(this.rotation.y);
                const sinY = Math.sin(this.rotation.y);
                const cosX = Math.cos(this.rotation.x);
                const sinX = Math.sin(this.rotation.x);
                
                // Y轴旋转
                let x = relative.x * cosY - relative.z * sinY;
                let z = relative.x * sinY + relative.z * cosY;
                let y = relative.y;
                
                // X轴旋转
                const newY = y * cosX - z * sinX;
                z = y * sinX + z * cosX;
                y = newY;

                if (z <= 0) return null;

                // 3D到2D投影
                const distance = (canvas.height / 2) / Math.tan((this.fov * Math.PI / 180) / 2);
                const scale = distance / z;
                
                return {
                    x: canvas.width / 2 + x * scale,
                    y: canvas.height / 2 - y * scale,
                    z: z,
                    scale: Math.max(0.1, Math.min(scale / 100, 3))
                };
            }

            update() {
                // 自动慢速旋转，营造飘逸感
                this.time += 0.005;
                this.rotation.y += 0.001;
                this.rotation.x = Math.sin(this.time * 0.3) * 0.2;
                
                // 轻微的位置变化，像在宇宙中漂浮
                this.position.x = Math.sin(this.time * 0.2) * 100;
                this.position.y = Math.cos(this.time * 0.15) * 50;
            }
        }

        // 粒子类（与交互版本相同的视觉效果）
        class Particle3D {
            constructor() {
                this.position = new Vector3D(
                    (Math.random() - 0.5) * 4000,
                    (Math.random() - 0.5) * 4000,
                    (Math.random() - 0.5) * 4000
                );
                
                // 使用噪声函数确定颜色（与交互版本相同）
                const noise = this.noise3D(this.position.x * 0.0008, this.position.y * 0.0008, this.position.z * 0.0008);
                
                if (noise > 0.3) {
                    this.color = '#FFD64F'; // 柠檬黄
                    this.baseSize = Math.random() * 6 + 4;
                } else if (noise > 0.0) {
                    this.color = '#C2A8F2'; // 紫晶色
                    this.baseSize = Math.random() * 4 + 3;
                } else {
                    this.color = '#666BCE'; // 紫色
                    this.baseSize = Math.random() * 3 + 2;
                }
                
                // 轻微的自动移动
                this.velocity = new Vector3D(
                    (Math.random() - 0.5) * 0.3,
                    (Math.random() - 0.5) * 0.3,
                    (Math.random() - 0.5) * 0.3
                );

                this.life = Math.random();
                this.lifeSpeed = Math.random() * 0.02 + 0.005;
            }

            noise3D(x, y, z) {
                return (Math.sin(x) * Math.cos(y) + Math.cos(y) * Math.sin(z) + Math.sin(z) * Math.cos(x)) / 3;
            }

            update() {
                this.position = this.position.add(this.velocity);
                this.life += this.lifeSpeed;
                
                if (this.life > 1) this.life = 0;
                
                // 边界检查
                if (Math.abs(this.position.x) > 2000) this.position.x *= -1;
                if (Math.abs(this.position.y) > 2000) this.position.y *= -1;
                if (Math.abs(this.position.z) > 2000) this.position.z *= -1;
            }

            render(camera) {
                const projected = camera.project(this.position);
                if (!projected) return;

                const alpha = Math.sin(this.life * Math.PI) * 0.8 + 0.3;
                const size = this.baseSize * projected.scale;
                
                // 发光效果
                ctx.shadowColor = this.color;
                ctx.shadowBlur = size * 2;
                ctx.globalAlpha = alpha;
                
                // 绘制粒子
                ctx.beginPath();
                ctx.fillStyle = this.color;
                ctx.arc(projected.x, projected.y, size, 0, Math.PI * 2);
                ctx.fill();
                
                // 绘制光晕
                ctx.globalAlpha = alpha * 0.3;
                ctx.beginPath();
                ctx.arc(projected.x, projected.y, size * 3, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.shadowBlur = 0;
            }
        }

        // 初始化
        const camera = new AutoCamera3D();
        const particles = [];
        const numParticles = 12000; // 稍少一些，性能更好

        for (let i = 0; i < numParticles; i++) {
            particles.push(new Particle3D());
        }

        // 主渲染循环
        function animate() {
            requestAnimationFrame(animate);

            camera.update();

            // 清屏，带拖尾效果
            ctx.fillStyle = 'rgba(10, 10, 26, 0.95)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 更新和渲染粒子
            particles.forEach(particle => {
                particle.update();
                particle.render(camera);
            });

            ctx.globalAlpha = 1;
        }

        // 启动
        console.log('✨ 非交互3D星空背景启动完成');
        animate();
    </script>
</body>
</html>