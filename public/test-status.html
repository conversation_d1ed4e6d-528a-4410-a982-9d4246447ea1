<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 - 3D粒子状态</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a1a;
            color: white;
            font-family: Arial, sans-serif;
        }
        .status {
            background: rgba(0,0,0,0.5);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .test-btn {
            padding: 10px 20px;
            background: #666BCE;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
        }
    </style>
</head>
<body>
    <h1>🔍 3D粒子系统测试页面</h1>
    
    <div class="status">
        <h3>测试结果：</h3>
        <p>✅ 这个页面能正常显示</p>
        <p id="time-status">⏰ 页面加载时间: <span id="load-time"></span>ms</p>
        <p id="js-status">⚠️ JavaScript检测中...</p>
    </div>

    <div class="status">
        <h3>快速测试链接：</h3>
        <a href="/particle-3d.html" target="_blank">
            <button class="test-btn">🌌 测试纯3D粒子页面</button>
        </a>
        <a href="/iframe-test" target="_blank">
            <button class="test-btn">🖼️ 测试iframe集成页面</button>
        </a>
        <a href="/" target="_blank">
            <button class="test-btn">🏠 测试首页（切换模式）</button>
        </a>
    </div>

    <div class="status">
        <h3>问题诊断：</h3>
        <div id="diagnosis">检测中...</div>
    </div>

    <script>
        // 记录加载时间
        document.getElementById('load-time').textContent = Date.now() - performance.timing.navigationStart;
        
        // JavaScript工作检测
        document.getElementById('js-status').innerHTML = '✅ JavaScript正常工作';
        
        // 诊断常见问题
        let diagnosis = [];
        
        // 检查基本功能
        if (typeof fetch === 'function') {
            diagnosis.push('✅ 网络请求功能正常');
        } else {
            diagnosis.push('❌ 网络请求功能异常');
        }
        
        if (typeof HTMLCanvasElement !== 'undefined') {
            diagnosis.push('✅ Canvas支持正常');
        } else {
            diagnosis.push('❌ Canvas不支持');
        }
        
        // 测试3D页面连接
        fetch('/particle-3d.html')
            .then(response => {
                if (response.ok) {
                    diagnosis.push('✅ 3D粒子页面可访问');
                } else {
                    diagnosis.push('❌ 3D粒子页面无法访问 (状态: ' + response.status + ')');
                }
                updateDiagnosis();
            })
            .catch(error => {
                diagnosis.push('❌ 3D粒子页面连接失败: ' + error.message);
                updateDiagnosis();
            });
        
        function updateDiagnosis() {
            document.getElementById('diagnosis').innerHTML = diagnosis.join('<br>');
        }
        
        // 初始诊断显示
        updateDiagnosis();
        
        console.log('🔍 测试页面已加载，所有基本功能正常');
    </script>
</body>
</html>