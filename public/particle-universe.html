<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逆线宇宙 - 3D粒子系统</title>
    <script src="https://cdn.jsdelivr.net/npm/three@0.179.1/build/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%);
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        #container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
        }
        
        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            text-align: center;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div id="container"></div>
    <div id="info">
        <div>🌌 独立3D粒子系统</div>
        <div>WASD: 移动 | 鼠标: 视角</div>
        <div>颜色: 紫色 + 紫晶色 + 柠檬黄</div>
    </div>
    <div id="loading">
        <h2 style="background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent;">
            🌌 正在启动逆线宇宙
        </h2>
        <p>请稍候...</p>
    </div>

    <script>
        console.log('🚀 启动独立3D粒子系统');
        
        // 场景设置
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(65, window.innerWidth / window.innerHeight, 1, 5000);
        const renderer = new THREE.WebGLRenderer({ 
            antialias: false, 
            alpha: false,
            powerPreference: 'high-performance'
        });
        
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
        document.getElementById('container').appendChild(renderer.domElement);
        
        // 雾效
        scene.fog = new THREE.Fog(0x0a0a1a, 500, 2000);
        
        // 创建粒子系统
        const particleCount = 15000;
        const geometry = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);
        const colors = new Float32Array(particleCount * 3);
        const sizes = new Float32Array(particleCount);
        
        // 你的CP应援色
        const color1 = new THREE.Color(0x666BCE); // 紫色
        const color2 = new THREE.Color(0xC2A8F2); // 紫晶色
        const color3 = new THREE.Color(0xFFD64F); // 柠檬黄
        
        for (let i = 0; i < particleCount; i++) {
            const x = (Math.random() - 0.5) * 3000;
            const y = (Math.random() - 0.5) * 3000;
            const z = (Math.random() - 0.5) * 3000;
            
            positions[i * 3] = x;
            positions[i * 3 + 1] = y;
            positions[i * 3 + 2] = z;
            
            // 颜色计算（原始算法）
            const noiseX = Math.sin(x * 0.001) * Math.cos(y * 0.001);
            const noiseY = Math.cos(y * 0.001) * Math.sin(z * 0.001);
            const noiseZ = Math.sin(z * 0.001) * Math.cos(x * 0.001);
            const combined = (noiseX + noiseY + noiseZ) / 3;
            
            let color;
            if (combined > 0.3) {
                color = color3.clone();
                sizes[i] = Math.random() * 8 + 5;
            } else if (combined > 0.0) {
                const t = combined / 0.3;
                color = color2.clone().lerp(color3, t);
                sizes[i] = Math.random() * 6 + 3.5;
            } else if (combined > -0.3) {
                const t = (combined + 0.3) / 0.3;
                color = color1.clone().lerp(color2, t);
                sizes[i] = Math.random() * 4 + 2.5;
            } else {
                color = color1.clone();
                sizes[i] = Math.random() * 3 + 1.5;
            }
            
            colors[i * 3] = color.r;
            colors[i * 3 + 1] = color.g;
            colors[i * 3 + 2] = color.b;
        }
        
        geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
        geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
        geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
        
        // 粒子材质
        const material = new THREE.PointsMaterial({
            size: 4.6,
            vertexColors: true,
            transparent: true,
            blending: THREE.AdditiveBlending,
            sizeAttenuation: true,
            depthWrite: false,
            opacity: 0.95
        });
        
        const particles = new THREE.Points(geometry, material);
        scene.add(particles);
        
        // 相机初始位置
        camera.position.set(0, 0, 900);
        
        // 控制系统
        const velocity = new THREE.Vector3();
        const keys = {};
        let isLocked = false;
        
        // 键盘控制
        document.addEventListener('keydown', (e) => { keys[e.code] = true; });
        document.addEventListener('keyup', (e) => { keys[e.code] = false; });
        
        // 鼠标控制
        document.addEventListener('click', (e) => {
            if (e.target.tagName === 'CANVAS') {
                document.body.requestPointerLock();
            }
        });
        
        document.addEventListener('pointerlockchange', () => {
            isLocked = !!document.pointerLockElement;
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isLocked) return;
            const sensitivity = 0.002;
            camera.rotation.y -= e.movementX * sensitivity;
            camera.rotation.x -= e.movementY * sensitivity;
            camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, camera.rotation.x));
        });
        
        // 渲染循环
        function animate() {
            requestAnimationFrame(animate);
            
            // 粒子旋转
            particles.rotation.x += 0.002;
            particles.rotation.y += 0.001;
            
            // WASD控制
            const speed = 5;
            const damp = 0.85;
            
            if (keys['KeyW'] || keys['ArrowUp']) velocity.z -= speed;
            if (keys['KeyS'] || keys['ArrowDown']) velocity.z += speed;
            if (keys['KeyA'] || keys['ArrowLeft']) velocity.x -= speed;
            if (keys['KeyD'] || keys['ArrowRight']) velocity.x += speed;
            if (keys['Space']) velocity.y += speed;
            if (keys['ShiftLeft'] || keys['ControlLeft']) velocity.y -= speed;
            
            // 应用移动
            const forward = new THREE.Vector3(0, 0, -1).applyQuaternion(camera.quaternion);
            const right = new THREE.Vector3(1, 0, 0).applyQuaternion(camera.quaternion);
            const up = new THREE.Vector3(0, 1, 0);
            
            camera.position.add(forward.clone().multiplyScalar(velocity.z));
            camera.position.add(right.clone().multiplyScalar(velocity.x));
            camera.position.add(up.clone().multiplyScalar(velocity.y));
            
            velocity.multiplyScalar(damp);
            
            renderer.render(scene, camera);
        }
        
        // 窗口调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
        
        // 隐藏加载提示
        setTimeout(() => {
            document.getElementById('loading').style.display = 'none';
        }, 1000);
        
        console.log('✨ 3D粒子系统启动完成');
        animate();
    </script>
</body>
</html>