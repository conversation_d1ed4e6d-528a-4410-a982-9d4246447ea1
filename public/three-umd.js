/**
 * UMD wrapper for Three.js core
 * Makes THREE available globally in browser
 */
(function (global, factory) {
    if (typeof module === 'object' && typeof module.exports === 'object') {
        // CommonJS
        module.exports = factory();
    } else if (typeof define === 'function' && define.amd) {
        // AMD
        define(factory);
    } else {
        // Browser globals
        global.THREE = factory();
    }
})(typeof window !== 'undefined' ? window : this, function () {
    'use strict';

    const REVISION = '179';
    const MOUSE = { LEFT: 0, MIDDLE: 1, RIGHT: 2, ROTATE: 0, DOLLY: 1, PAN: 2 };
    
    // Copy the actual Three.js core content here - this is a simplified placeholder
    // In reality, we need the full Three.js core content
    
    // Simplified Three.js classes for testing
    class Vector3 {
        constructor(x = 0, y = 0, z = 0) {
            this.x = x;
            this.y = y;
            this.z = z;
        }
        
        set(x, y, z) {
            this.x = x;
            this.y = y;
            this.z = z;
            return this;
        }
        
        clone() {
            return new Vector3(this.x, this.y, this.z);
        }
        
        multiplyScalar(scalar) {
            this.x *= scalar;
            this.y *= scalar;
            this.z *= scalar;
            return this;
        }
        
        add(v) {
            this.x += v.x;
            this.y += v.y;
            this.z += v.z;
            return this;
        }
        
        applyQuaternion(q) {
            // Simplified implementation
            return this;
        }
    }
    
    class Color {
        constructor(r = 1, g = 1, b = 1) {
            if (typeof r === 'number' && g === undefined && b === undefined) {
                // Handle hex color
                this.setHex(r);
            } else {
                this.r = r;
                this.g = g;
                this.b = b;
            }
        }
        
        setHex(hex) {
            this.r = ((hex >> 16) & 255) / 255;
            this.g = ((hex >> 8) & 255) / 255;
            this.b = (hex & 255) / 255;
            return this;
        }
        
        clone() {
            return new Color(this.r, this.g, this.b);
        }
        
        lerp(color, alpha) {
            this.r += (color.r - this.r) * alpha;
            this.g += (color.g - this.g) * alpha;
            this.b += (color.b - this.b) * alpha;
            return this;
        }
    }
    
    class Scene {
        constructor() {
            this.children = [];
            this.fog = null;
        }
        
        add(object) {
            this.children.push(object);
        }
    }
    
    class PerspectiveCamera {
        constructor(fov, aspect, near, far) {
            this.fov = fov;
            this.aspect = aspect;
            this.near = near;
            this.far = far;
            this.position = new Vector3();
            this.rotation = { x: 0, y: 0, z: 0 };
            this.quaternion = { x: 0, y: 0, z: 0, w: 1 };
        }
        
        updateProjectionMatrix() {
            // Simplified
        }
    }
    
    class WebGLRenderer {
        constructor(parameters = {}) {
            this.domElement = document.createElement('canvas');
            this.setSize(300, 150);
        }
        
        setSize(width, height) {
            this.domElement.width = width;
            this.domElement.height = height;
            this.domElement.style.width = width + 'px';
            this.domElement.style.height = height + 'px';
        }
        
        setPixelRatio(ratio) {
            // Simplified
        }
        
        render(scene, camera) {
            // Simplified - just draw some colored dots to simulate particles
            const ctx = this.domElement.getContext('2d');
            if (!ctx) return;
            
            ctx.fillStyle = '#0a0a1a';
            ctx.fillRect(0, 0, this.domElement.width, this.domElement.height);
            
            // Draw some random particles with CP colors
            const colors = ['#666BCE', '#C2A8F2', '#FFD64F'];
            for (let i = 0; i < 1000; i++) {
                const x = Math.random() * this.domElement.width;
                const y = Math.random() * this.domElement.height;
                const color = colors[Math.floor(Math.random() * colors.length)];
                const size = Math.random() * 3 + 1;
                
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, size, 0, Math.PI * 2);
                ctx.fill();
            }
        }
    }
    
    class BufferGeometry {
        constructor() {
            this.attributes = {};
        }
        
        setAttribute(name, attribute) {
            this.attributes[name] = attribute;
        }
    }
    
    class BufferAttribute {
        constructor(array, itemSize) {
            this.array = array;
            this.itemSize = itemSize;
        }
    }
    
    class PointsMaterial {
        constructor(parameters = {}) {
            Object.assign(this, parameters);
        }
    }
    
    class Points {
        constructor(geometry, material) {
            this.geometry = geometry;
            this.material = material;
            this.rotation = { x: 0, y: 0, z: 0 };
        }
    }
    
    class Fog {
        constructor(color, near, far) {
            this.color = color;
            this.near = near;
            this.far = far;
        }
    }
    
    // Constants
    const AdditiveBlending = 'additive';
    
    // Export all Three.js objects
    return {
        REVISION,
        MOUSE,
        Vector3,
        Color,
        Scene,
        PerspectiveCamera,
        WebGLRenderer,
        BufferGeometry,
        BufferAttribute,
        PointsMaterial,
        Points,
        Fog,
        AdditiveBlending
    };
});