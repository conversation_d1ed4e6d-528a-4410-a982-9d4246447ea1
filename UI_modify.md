### **【逆线】《逆爱》专题站 UI/UX 设计方案 V2.0**





#### **一、设计哲学与核心理念**



1. **产品名称：** **逆线 (Nìxiàn)**
2. **核心定位：** 不再是“续界”，而是全球首个、也是最懂粉丝的《逆爱》/《逆袭》AI同人创作与交流中心。
3. **设计关键词：** **沉浸 (Immersive)、共感 (Empathetic)、羁绊 (Bonding)、颠覆 (Subversive)**
4. **视觉识别 (VI) 体系：**
   - **主Logo:** 采用您提供的“莫比乌斯环”变体“逆”字Logo。它将作为网站的灵魂符号。
   - **主色调:** 严格遵循您提供的CSS代码，以 **幻海紫 (`#666BCE`)** 到 **柠檬黄 (`#FFD64F`)** 的渐变色作为网站的核心品牌色，应用于所有关键视觉元素。
5. **主题概念整合：“莫比乌斯环”**
   - 这个概念将被巧妙地融入网站的语言和交互中，我们将用它来象征主角间“无限循环、命中注定、打破常规”的羁绊。

------



#### **二、网站整体架构与页面蓝图**



为了实现专注，我们需要重构网站结构。原有的首页将被一个全新的、功能聚合的页面所取代。

**新网站结构:**

- **`/` (首页):** 改为 **“灵感枢纽 (Creation Hub)”**，成为所有创作和浏览的起点。
- **`/gallery`:** **“光影回廊 (Image Gallery)”**，专门展示您分类好的剧照图库。
- **`/forum`:** **“交错时空 (Discussion Forum)”**，用户社区。
- **`/result/[id]`:** 生成结果页 (功能更新)。

------



#### **三、核心页面 UI/UX 详细设计**





##### **A. 首页 / 灵感枢纽 (The Creation Hub)**



这是用户进入网站的第一站，必须立刻让他们感受到“来对地方了”。

1. **顶部横幅 (Banner):**
   - 直接使用您提供的`cp-banner`样式，背景为紫黄渐变。
   - 标题文字为：`逆线 · 《逆爱》×《逆袭》创作圣地`。
   - 在Banner下方，可以加上一行更有共鸣的Slogan，例如：**“在无数逆转的线里，重逢唯一的你。”**
   - Banner的存储位置是E:\code\My_repos\niai\xujie-website\UI\cp-banner.html，整个网页都要用这种颜色作为统一和视频，UI界面要好看，要吸引人，要让人觉得充满同人文色彩
   - logo是这个图案E:\code\My_repos\niai\xujie-website\UI\logo-final.svg
2. **核心创作入口 (取代旧的搜索框):**
   - 删除所有其他CP和热文推荐。
   - 页面的**正中央**是创作模块，标题为 **“开启一段OOC的旅程”** 或 **“拨动命运的逆线”**。
   - 这个模块直接整合了我们之前设计的**“意图选择”**功能。提供四个清晰的、配有精美图标的入口：
     - **`[续写番外]`**
     - **`[平行宇宙(AU)]`**
     - **`[风格模仿, 新故事]`**
     - **`[补完细节]`**
   - 用户点击任一入口后，下方会优雅地展开一个文本框，让用户输入他们的“脑洞”或“愿望”。这部分**直接对接您现有的“逆爱专题制作”的后端接口**，只需在前端修改调用位置即可。
3. **新增模块的入口:**
   - 在核心创作模块下方，用两个精美的大尺寸卡片，分别作为“光影回廊”和“交错时空”的入口。
   - **卡片A标题：** `光影回廊`，副标题：“捕捉每个心动瞬间”，背景用一张最高赞的“池畏”CP图做模糊处理。
   - **卡片B标题：** `交错时空`，副标题：“聊聊那些让我们上头的剧情”，背景用一张“群像”剧照。



##### **B. 光影回廊 (The Image Gallery)**



这是粉丝的“视觉盛宴”，设计上要突出图片的精美。

1. **顶部导航:** 使用标签页(Tabs)设计，清晰地列出您的8个分类：`池畏` `展丞` `群像` `池骋` `吴所畏` `郭城宇` `姜小帅`。
2. **图片布局:** 采用**响应式网格布局 (Masonry Layout)**，类似Pinterest，图片可以不等高，错落有致，视觉上更生动。
3. **交互设计:**
   - 鼠标悬停在图片上时，图片会轻微放大，并浮现一个半透明的黑色遮罩。
   - 遮罩上显示两个图标：一个**爱心❤ (点赞)** 和一个**放大镜 (查看大图)**。爱心旁边实时显示点赞数。
   - 点赞功能需要新的后端接口支持，每次点击都会更新该图片的`likes`计数值。
   - **排序逻辑:** 默认情况下，每个图集模块加载图片时，后端API应按`likes`数量**降序排列**，这样最高赞的图片就自动排在最前面了。
4. 池畏、展丞、群像、池骋、吴所畏、郭城宇、姜小帅7个分类分别对应E:\code\My_repos\niai\photo文件夹下的cp_main、cp_sub、group、Portraits_cc、Portraits_gcy、Portraits_jxs、Portraits_wsw


##### **C. 交错时空 (The Discussion Forum)**



这是社区沉淀的地方，设计要鼓励交流。

1. **布局:** 经典的社交媒体信息流布局，无限滚动加载。
2. **帖子发布器:** 置顶一个简洁的输入框，允许用户输入文字，并提供一个“上传图片”的按钮。
3. **帖子(Post)组件:**
   - 每条帖子都是一个卡片，包含：用户头像、用户名、发布时间、正文文字、图片（如有）、以及底部的**“点赞”**和**“转发”**按钮和计数。
   - “转发”功能初期可以简化为“复制帖子链接”或“生成分享图片”。



##### **D. 生成结果页 (The Result Page)**



这里需要实现您提出的新功能。

1. **“复制内容”按钮:**
   - **功能:** 点击后，将生成的**纯文本**内容复制到用户剪贴板。这个实现很简单。
2. **“下载为图片”按钮:**
   - **功能：** 点击后，后端需要动态生成一张图片供用户下载。
   - **实现逻辑 (需要新的后端API):**
     1. 前端将**生成好的文本、您的Prompt、当前用户的ID**发送给一个新的后端API，例如 `/api/generate-image`。
     2. 后端收到请求后，使用一个图像处理库（如`node-canvas`或`sharp`）在服务器上“绘制”一张图片：
        - **背景：** 可以是一张淡雅的纹理图，或一张与情节情绪相关的、经过模糊处理的剧照。
        - **内容：** 用漂亮的字体（如思源宋体）将生成的故事文本绘制在图片中央。
        - **水印：** 在图片的底部，用小字号绘制两行水印：
          - `Prompt: [您的AI指令]`
          - `Generated for User: [用户ID]`
     3. 后端将绘制好的图片以文件流的形式返回给前端，触发浏览器下载。

------



#### **四、后端接口适配与新增**



根据您的要求（不动后端具体内容，只修改接口），我们需要这样规划：

1. **修改已有接口调用:**
   - 您现有的“逆爱专题制作”后端接口逻辑**完全不用动**。您只需要修改**前端代码**，让首页新的“核心创作入口”在用户点击“生成”时，去调用这个已有的、成熟的接口即可。
2. **新增必要的后端接口:**
   - 为了实现新功能，您必须创建一些新的后端API：
     - **图库部分:**
       - `GET /api/gallery?category=[分类名]` (用于获取某个分类下的图片列表，并已按点赞数排好序)
       - `POST /api/gallery/images/:id/like` (用于处理点赞操作)
     - **讨论区部分:**
       - `GET /api/posts` (获取帖子列表)
       - `POST /api/posts` (发布新帖子)
       - `POST /api/posts/:id/like` (点赞帖子)
     - **结果下载部分:**
       - `POST /api/generate-image` (用于动态生成分享图)



#### **五、补充的沉浸感与共感设计**



- **加载动画:** 在等待AI生成时，不要用普通的圈圈。用您的“莫比乌斯环”Logo，让它优雅地、无限地循环旋转。旁边配上文字：“逆线正在交错，请稍候...”。
- **404页面:** 设计一个有趣的OOC场景，例如：“页面被吴所畏藏起来了，池骋正在到处找呢！要不，我们写写他找不到人时着急的样子？”下面直接就是一个创作入口。
- **网站引导文案:** 多使用“我们”、“你的”、“专属”等词语，并巧妙化用小说或剧中的梗，让用户感觉你真的“很懂”。

通过以上全方位的改造，您的“续界”将成功蜕变为一个高度专注、体验沉浸、粉丝粘性极强的《逆爱》主题站——**“逆线”**。