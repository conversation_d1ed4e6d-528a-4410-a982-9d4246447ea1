#!/bin/bash

echo "🚀 续界网站 - 环境配置检查工具"
echo "=================================="

# 检查.env.local文件是否存在
if [ ! -f .env.local ]; then
    echo "❌ .env.local 文件不存在"
    echo "正在创建 .env.local 文件..."
    cp .env.example .env.local
    echo "✅ .env.local 文件已创建"
else
    echo "✅ .env.local 文件存在"
fi

# 检查API密钥配置
echo ""
echo "📋 检查API密钥配置:"

if grep -q "^GEMINI_API_KEY=AIza" .env.local; then
    echo "✅ Gemini API 密钥已配置"
else
    echo "⚠️ Gemini API 密钥未配置或无效"
fi

if grep -q "^KIMI_API_KEY=sk-" .env.local; then
    echo "✅ Kimi API 密钥已配置" 
else
    echo "⚠️ Kimi API 密钥未配置或无效"
fi

if grep -q "^DEEPSEEK_API_KEY=sk-" .env.local; then
    echo "✅ DeepSeek API 密钥已配置"
else
    echo "⚠️ DeepSeek API 密钥未配置或无效"
fi

echo ""
echo "🔧 如果API密钥未配置，请:"
echo "1. 编辑 .env.local 文件"
echo "2. 填入有效的API密钥"
echo "3. 重启开发服务器: npm run dev"

echo ""
echo "📝 获取API密钥链接:"
echo "- Gemini: https://makersuite.google.com/app/apikey"
echo "- Kimi: https://platform.moonshot.cn/console/api-keys"  
echo "- DeepSeek: https://platform.deepseek.com/api_keys"

echo ""
echo "🎯 测试AI功能:"
echo "1. 启动服务器后访问: http://localhost:3000/test-ai"
echo "2. 或直接使用网站进行测试"