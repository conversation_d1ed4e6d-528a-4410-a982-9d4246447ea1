#!/bin/bash

# 预编译所有主要页面以提高首次访问速度
echo "🚀 开始预编译所有页面..."

# 等待Next.js服务器启动
sleep 3

# 主要页面列表
pages=(
    "auth"
    "generate" 
    "creative-space"
    "notice"
    "gallery"
    "titles"
    "forum"
    "recharge"
)

base_url="http://localhost:3000"

# 并发预编译所有页面
for page in "${pages[@]}"; do
    echo "🔧 预编译 /$page..."
    curl -s "$base_url/$page" > /dev/null &
done

# 等待所有预编译完成
wait

echo "✅ 所有页面预编译完成！现在用户点击按钮将立即看到页面。"