<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试页面</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #1e293b, #581c87, #1e293b);
            color: white;
            text-align: center;
            padding: 50px;
            margin: 0;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .status { 
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin: 20px;
            backdrop-filter: blur(10px);
        }
        .success { border-left: 5px solid #10b981; }
        .info { border-left: 5px solid #3b82f6; }
    </style>
</head>
<body>
    <h1>🎉 连接测试成功！</h1>
    
    <div class="status success">
        <h2>✅ 服务器状态正常</h2>
        <p>如果你能看到这个页面，说明：</p>
        <ul style="text-align: left; display: inline-block;">
            <li>Next.js 开发服务器运行正常</li>
            <li>端口 3000 可以正常访问</li>
            <li>网络连接没有问题</li>
        </ul>
    </div>

    <div class="status info">
        <h2>🌐 访问信息</h2>
        <p><strong>本地访问:</strong> http://localhost:3000</p>
        <p><strong>网络访问:</strong> http://************:3000</p>
        <p><strong>当前时间:</strong> <span id="time"></span></p>
    </div>

    <div class="status info">
        <h2>🔧 下一步</h2>
        <p>如果测试成功，现在可以：</p>
        <ol style="text-align: left; display: inline-block;">
            <li>访问主页：<a href="/" style="color: #60a5fa;">返回首页</a></li>
            <li>确认浏览器可以正常访问项目</li>
            <li>开始恢复完整的 Three.js 功能</li>
        </ol>
    </div>

    <script>
        document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
        
        // 每秒更新时间
        setInterval(() => {
            document.getElementById('time').textContent = new Date().toLocaleString('zh-CN');
        }, 1000);
    </script>
</body>
</html>