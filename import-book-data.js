const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase配置
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://bhbzfbryaehofeafplcu.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoYnpmYnJ5YWVob2ZlYWZwbGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTA4MTM0NSwiZXhwIjoyMDcwNjU3MzQ1fQ.tnwhVuvkxRIBM2epa8X815smk14ySjceK1BOL1jM4V8';

console.log('Starting book data import...');
console.log('Supabase URL:', SUPABASE_URL);

// 创建Supabase客户端
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

// 分批插入数据
async function insertBatch(batch) {
  const { data, error } = await supabase
    .from('book_chunks')
    .insert(batch);
  
  if (error) {
    console.error('Error inserting batch:', error);
    throw error;
  }
  
  return data;
}

async function importBookData() {
  try {
    // 读取JSON文件
    const filePath = path.join(__dirname, 'data', '逆袭-index.json');
    console.log('Reading file:', filePath);
    
    // 由于文件很大，我们使用流式读取
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const jsonData = JSON.parse(fileContent);
    
    console.log('JSON file parsed successfully');
    console.log('Total chunks:', jsonData.chunks.length);
    
    // 格式化数据
    const formattedData = jsonData.chunks.map((item, index) => ({
      work_title: '逆袭',
      chapter_no: item.chapterNo || null,
      chapter_title: item.chapterTitle || null,
      chunk_text: item.text
    }));
    
    console.log('Data formatted, inserting into Supabase...');
    
    // 分批插入数据以避免超时，使用更小的批次
    const batchSize = 50;
    let insertedCount = 0;
    
    for (let i = 0; i < formattedData.length; i += batchSize) {
      const batch = formattedData.slice(i, i + batchSize);
      console.log(`Inserting batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(formattedData.length/batchSize)}...`);
      
      try {
        await insertBatch(batch);
        insertedCount += batch.length;
        console.log(`Batch inserted, total so far: ${insertedCount}`);
        
        // 添加一个小延迟以避免过于频繁的请求
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`Error inserting batch starting at index ${i}:`, error);
        // 继续处理下一个批次
      }
    }
    
    console.log(`Successfully inserted all ${insertedCount} chunks into Supabase!`);
  } catch (error) {
    console.error('Error during import:', error);
  }
}

// 执行导入
importBookData();