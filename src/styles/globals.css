@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  color: white;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: auto;
  height: 100%;
  width: 100%;
}

/* 全局CSS样式 - 保持A项目的粒子星空 + B项目的磨砂玻璃UI */

/* CP品牌渐变色 */
.cp-gradient-text {
  background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%) !important;
  -webkit-background-clip: text !important;
  background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  color: transparent !important;
}

/* 磨砂玻璃卡片 */
.frosted-glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

/* 磨砂玻璃按钮 */
.frosted-glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.frosted-glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 8px 32px rgba(102, 107, 206, 0.3);
}

/* 呼吸光效 */
.breathing-glow {
  animation: breathing-glow 3s ease-in-out infinite;
}

@keyframes breathing-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(102, 107, 206, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(102, 107, 206, 0.6);
  }
}

/* 悬浮效果 */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* 隐藏截图元素 */
.hide-on-screenshot {
  /* 可用于截图时隐藏UI元素 */
}

/* 弹幕动画 */
.danmu-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 10;
}

.danmu-item {
  position: absolute;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  animation: scroll-left 35s linear infinite; /* 从20s调整为35s，使弹幕更慢 */
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

@keyframes scroll-left {
  from { 
    transform: translateX(100vw); 
  }
  to { 
    transform: translateX(-100%); 
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .frosted-glass-card {
    border-radius: 12px;
    backdrop-filter: blur(15px);
  }
  
  .danmu-item {
    font-size: 12px;
    padding: 4px 8px;
    animation: scroll-left 30s linear infinite; /* 移动端稍快一些 */
  }
}

/* 输入框聚焦效果 */
input:focus, textarea:focus {
  outline: none;
  border-color: #C2A8F2;
  box-shadow: 0 0 0 2px rgba(194, 168, 242, 0.2);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(194, 168, 242, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(194, 168, 242, 0.8);
}

/* Z-index 层级定义 */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 弹幕动画 */
.danmu-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 10;
}

.danmu-item {
  position: absolute;
  white-space: nowrap;
  font-size: 14px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  animation: scroll-left 35s linear infinite; /* 从20s调整为35s，使弹幕更慢 */
  font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
}

@keyframes scroll-left {
  from { 
    transform: translateX(100vw); 
  }
  to { 
    transform: translateX(-100%); 
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .frosted-glass-card {
    border-radius: 12px;
    backdrop-filter: blur(15px);
  }
  
  .danmu-item {
    font-size: 12px;
    padding: 4px 8px;
    animation: scroll-left 30s linear infinite; /* 移动端稍快一些 */
  }
}

/* 输入框聚焦效果 */
input:focus, textarea:focus {
  outline: none;
  border-color: #C2A8F2;
  box-shadow: 0 0 0 2px rgba(194, 168, 242, 0.2);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(194, 168, 242, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(194, 168, 242, 0.8);
}

/* Z-index 层级定义 */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}