@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式定义 */
@layer base {
  :root {
    /* CP应援色 - 来自A项目的配色方案 */
    --cp-primary: #666BCE;      /* 幻影紫 */
    --cp-secondary: #C2A8F2;    /* 紫水晶 */
    --cp-accent: #FFD64F;       /* 柠檬黄 */
    
    /* 磨砂玻璃效果的基础颜色 */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: rgba(0, 0, 0, 0.1);
    
    /* 背景渐变 */
    --bg-gradient: linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%);
    --bg-gradient-light: linear-gradient(135deg, rgba(10, 10, 26, 0.8) 0%, rgba(26, 10, 42, 0.8) 50%, rgba(10, 10, 26, 0.8) 100%);
  }
}

@layer components {
  /* 磨砂玻璃卡片样式 */
  .frosted-glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 1.5rem;
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .frosted-glass-card:hover {
    transform: translateY(-4px);
    box-shadow: 
      0 20px 60px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.35);
    background: rgba(255, 255, 255, 0.2);
  }
  
  /* 磨砂玻璃按钮样式 */
  .frosted-glass-button {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: 1rem;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }
  
  .frosted-glass-button:hover {
    transform: scale(1.05) translateY(-2px);
    box-shadow: 
      0 0 30px rgba(102, 107, 206, 0.6),
      0 0 60px rgba(194, 168, 242, 0.4);
    border-color: var(--cp-secondary);
    background: rgba(255, 255, 255, 0.25);
  }
  
  .frosted-glass-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }
  
  .frosted-glass-button:hover::before {
    left: 100%;
  }
  
  /* CP应援色文字渐变 */
  .cp-gradient-text {
    background: linear-gradient(135deg, var(--cp-primary) 0%, var(--cp-secondary) 50%, var(--cp-accent) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
  
  /* 呼吸光晕效果 */
  .breathing-glow {
    animation: breathing-glow 2.5s ease-in-out infinite;
  }
  
  @keyframes breathing-glow {
    0%, 100% {
      box-shadow: 
        0 0 20px rgba(102, 107, 206, 0.4),
        0 0 40px rgba(194, 168, 242, 0.2);
    }
    50% {
      box-shadow: 
        0 0 40px rgba(102, 107, 206, 0.8), 
        0 0 80px rgba(194, 168, 242, 0.6),
        0 0 120px rgba(255, 214, 79, 0.3);
    }
  }
  
  /* 微交互悬浮效果 */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  }
  
  /* 星空背景容器 */
  .starfield-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
  }
  
  .starfield-container.interactive {
    pointer-events: auto;
  }
  
  /* 内容区域 */
  .content-overlay {
    position: relative;
    z-index: 10;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--cp-primary), var(--cp-secondary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--cp-secondary), var(--cp-accent));
}
