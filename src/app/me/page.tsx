"use client";

import { useEffect, useState } from 'react';
import { FolderPlus, Folder, Trash2, MoveR<PERSON>, Co<PERSON>, Eye, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function MePage(){
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [folders, setFolders] = useState<any[]>([]);
  const [items, setItems] = useState<any[]>([]);
  const [activeFolder, setActiveFolder] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [newFolderName, setNewFolderName] = useState('');

  useEffect(()=>{
    try{ const u = JSON.parse(localStorage.getItem('user')||'null'); setUser(u||null); }catch{}
  },[]);

  // 同步服务器用户信息，确保撤销后余额及时更新
  useEffect(()=>{
    async function sync(){
      try{
        const u = JSON.parse(localStorage.getItem('user')||'null');
        if (u?.id){
          const r = await fetch('/api/me', { headers:{ 'x-user-id': u.id } });
          const j = await r.json();
          if (j?.ok){ localStorage.setItem('user', JSON.stringify(j.user)); setUser(j.user); }
        }
      }catch{}
    }
    sync();
    const onFocus = ()=>sync();
    window.addEventListener('focus', onFocus);
    return ()=> window.removeEventListener('focus', onFocus);
  },[]);

  useEffect(()=>{ if(!user?.id) return; refresh(); }, [user]);

  async function ensureDefaultFolder(userId: string){
    // 若无任何文件夹，则创建“默认”
    if (folders.length === 0) {
      await fetch('/api/favorites', { method:'POST', headers:{ 'Content-Type':'application/json','x-user-id': userId }, body: JSON.stringify({ type:'folder', name: '默认' }) });
    }
  }

  async function refresh(){
    setLoading(true);
    try{
      const r = await fetch('/api/favorites', { headers:{ 'x-user-id': user.id } });
      const j = await r.json();
      if (j?.ok){
        let fs = j.folders||[];
        let its = j.items||[];
        // 若不存在任何文件夹，自动创建“默认”后再拉取一次
        if (fs.length === 0) {
          await ensureDefaultFolder(user.id);
          const r2 = await fetch('/api/favorites', { headers:{ 'x-user-id': user.id } });
          const j2 = await r2.json();
          fs = j2.folders||[];
          its = j2.items||[];
        }
        setFolders(fs);
        setItems(its);
        // 优先选中“默认”，否则选中第一个
        const def = fs.find((f:any)=>f.name === '默认');
        if (def) setActiveFolder(def.id);
        else if (!activeFolder && fs[0]) setActiveFolder(fs[0].id);
      }
    } finally { setLoading(false); }
  }

  async function createFolder(){
    if (!newFolderName.trim()) return;
    const r = await fetch('/api/favorites', { method:'POST', headers:{ 'Content-Type':'application/json','x-user-id': user.id }, body: JSON.stringify({ type:'folder', name: newFolderName.trim() }) });
    const j = await r.json();
    if (j?.ok){ setNewFolderName(''); refresh(); }
  }

  async function deleteFolder(id: string){
    if (!confirm('删除该文件夹（含其收藏）？')) return;
    await fetch('/api/favorites', { method:'DELETE', headers:{ 'Content-Type':'application/json','x-user-id': user.id }, body: JSON.stringify({ type:'folder', id }) });
    refresh();
  }

  async function deleteItem(id: string){
    await fetch('/api/favorites', { method:'DELETE', headers:{ 'Content-Type':'application/json','x-user-id': user.id }, body: JSON.stringify({ type:'item', id }) });
    refresh();
  }

  async function moveItem(id: string, folderId: string){
    await fetch('/api/favorites', { method:'PUT', headers:{ 'Content-Type':'application/json','x-user-id': user.id }, body: JSON.stringify({ type:'item', id, folderId }) });
    refresh();
  }

  async function copyItem(id: string, folderId: string){
    const it = items.find(x=>x.id===id);
    if (!it) return;
    await fetch('/api/favorites', { method:'POST', headers:{ 'Content-Type':'application/json','x-user-id': user.id }, body: JSON.stringify({ type:'item', title: it.title, content: it.content, folderId }) });
    refresh();
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F6F5FF] to-[#FFF7E6]">
      <nav className="border-b bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
          <button onClick={()=>router.back()} className="text-gray-600 hover:text-[#666BCE] flex items-center gap-2"><ArrowLeft className="w-4 h-4"/>返回</button>
          <div className="text-sm text-gray-700">{(user?.name||'未登录') + (user?.id ? ` (${user.id})` : '')}</div>
        </div>
      </nav>

      <div className="max-w-6xl mx-auto px-4 pt-6">
        <div className="rounded-2xl p-6 text-white shadow-md bg-gradient-to-r from-[#666BCE] via-[#C2A8F2] to-[#FFD64F]">
          <div className="text-xl font-bold tracking-wide">逆线 · 个人中心</div>
          <div className="mt-1 text-sm opacity-90">{(user?.name||'未登录') + (user?.id ? ` (${user.id})` : '')}</div>
        </div>
        {user?.id && (
          <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="rounded-2xl p-5 bg-white shadow-sm border">
              <div className="text-lg font-bold text-gray-800">我的邀请码</div>
              <div className="text-xs text-gray-500 mt-1">分享给好友注册，双方各+30 灵感（至 2025-08-12）</div>
              <div className="mt-3">
                <div className="text-sm text-gray-600">邀请码</div>
                <div className="flex items-center gap-2 mt-1">
                  <input readOnly value={user.inviteCode || ''} className="flex-1 border rounded px-2 py-1 text-sm" />
                  <button className="px-3 py-1 rounded bg-[#666BCE] text-white" onClick={()=>{ navigator.clipboard.writeText(user.inviteCode||''); alert('邀请码已复制'); }}>复制</button>
                </div>
              </div>
              <div className="mt-3">
                <div className="text-sm text-gray-600">邀请链接</div>
                <div className="flex items-center gap-2 mt-1">
                  <input readOnly value={`${location.origin}/auth?invite=${encodeURIComponent(user.inviteCode||'')}`} className="flex-1 border rounded px-2 py-1 text-sm" />
                  <button className="px-3 py-1 rounded bg-[#C2A8F2] text-white" onClick={()=>{ navigator.clipboard.writeText(`${location.origin}/auth?invite=${encodeURIComponent(user.inviteCode||'')}`); alert('邀请链接已复制'); }}>复制</button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      <main className="max-w-6xl mx-auto px-4 py-8 grid grid-cols-1 md:grid-cols-4 gap-6">
        {/* 侧栏：文件夹 */}
        <aside className="md:col-span-1 bg-white/85 backdrop-blur rounded-2xl border border-violet-100 shadow-sm p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="font-bold text-gray-800">我的收藏</div>
            <button onClick={createFolder} className="bg-gradient-to-r from-[#666BCE] to-[#FFD64F] text-white text-sm flex items-center gap-1 rounded-full px-3 py-1 shadow-sm hover:opacity-90"><FolderPlus className="w-4 h-4"/>新建</button>
          </div>
          <div className="flex items-center gap-2 mb-3">
            <input className="flex-1 border border-violet-200 rounded-full px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-violet-200" placeholder="新建文件夹名" value={newFolderName} onChange={e=>setNewFolderName(e.target.value)} />
          </div>
          <div className="space-y-1">
            {folders.map(f=> (
              <div key={f.id} className={`flex items-center justify-between px-3 py-2 rounded-xl cursor-pointer border ${activeFolder===f.id? 'bg-gradient-to-r from-[#F1EFFF] to-[#FFF7D6] text-[#3D4087] border-violet-100 shadow-sm' : 'hover:bg-slate-50 border-transparent'}`} onClick={()=>setActiveFolder(f.id)}>
                <div className="flex items-center gap-2"><Folder className="w-4 h-4"/>{f.name} <span className="text-xs text-gray-400">{f.id}</span></div>
                <button onClick={(e)=>{e.stopPropagation(); deleteFolder(f.id);}} className="text-gray-400 hover:text-red-500"><Trash2 className="w-4 h-4"/></button>
              </div>
            ))}
            {folders.length===0 && <div className="text-xs text-gray-400">暂无收藏夹</div>}
          </div>
        </aside>

        {/* 列表：收藏项 */}
        <section className="md:col-span-3 bg-white/90 backdrop-blur rounded-2xl border border-slate-100 shadow-sm p-5">
          {loading ? <div className="text-gray-400">加载中...</div> : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {items.filter(it=>!activeFolder || it.folderId===activeFolder).map(it=> (
                <div key={it.id} className="border border-slate-100 rounded-2xl p-4 hover:shadow-md transition bg-white/95">
                  <div className="font-semibold text-gray-800 mb-2 line-clamp-1">{it.title}</div>
                  <div className="text-[15px] text-gray-700 whitespace-pre-line line-clamp-5 font-serif leading-7">{it.content}</div>
                  <div className="flex items-center justify-between mt-3 text-sm">
                    <div className="text-gray-400">{new Date(it.createdAt).toLocaleString()}</div>
                    <div className="flex items-center gap-2">
                      <button className="bg-gradient-to-r from-[#666BCE] to-[#FFD64F] text-white rounded-full px-3 py-1 flex items-center gap-1 shadow-sm hover:opacity-90" onClick={()=>{ navigator.clipboard.writeText(it.content); alert('已复制'); }}><Copy className="w-4 h-4"/>复制文本</button>
                      <div className="flex items-center gap-1">
                        <span className="text-gray-500">复制到</span>
                        <select className="border border-violet-200 rounded-full px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-violet-200" onChange={(e)=>{ const fid=e.target.value; if(fid) copyItem(it.id, fid); e.currentTarget.selectedIndex = 0; }}>
                          <option value="">选择文件夹</option>
                          {folders.filter(f=>f.id!==it.folderId).map(f=> <option key={f.id} value={f.id}>{f.name}</option>)}
                        </select>
                      </div>
                      <div className="flex items-center gap-1">
                        <span className="text-gray-500">移动到</span>
                        <select className="border border-violet-200 rounded-full px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-violet-200" onChange={(e)=>{ const fid=e.target.value; if(fid) moveItem(it.id, fid); e.currentTarget.selectedIndex = 0; }}>
                          <option value="">选择文件夹</option>
                          {folders.filter(f=>f.id!==it.folderId).map(f=> <option key={f.id} value={f.id}>{f.name}</option>)}
                        </select>
                      </div>
                      <button className="text-red-500 flex items-center gap-1 px-3 py-1 rounded-full border border-red-200 hover:bg-red-50" onClick={()=>deleteItem(it.id)}><Trash2 className="w-4 h-4"/>删除</button>
                    </div>
                  </div>
                </div>
              ))}
              {items.filter(it=>!activeFolder || it.folderId===activeFolder).length===0 && (
                <div className="text-gray-400">该文件夹暂无收藏</div>
              )}
            </div>
          )}
        </section>
      </main>
    </div>
  );
}