'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import UnifiedLayout from '@/components/UnifiedLayout';
import { 
  UnifiedButton, 
  UnifiedCard, 
  UnifiedLoading,
  UnifiedBadge,
  cpColors 
} from '@/components/ui/UnifiedComponents';
import { ALL_TITLES, type Title } from '@/lib/titles/titles';

export default function TitlesPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('collection');
  const [user, setUser] = useState<any>(null);
  const [userTitles, setUserTitles] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [drawLoading, setDrawLoading] = useState(false);

  useEffect(() => {
    loadUserTitles();
  }, []);

  const loadUserTitles = async () => {
    try {
      const response = await fetch('/api/user/titles');
      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        setUserTitles(data.titles || []);
      }
    } catch (error) {
      console.error('Failed to load user titles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDraw = async (pool: 'R' | 'SR' | 'SSR', cost: number) => {
    if (drawLoading) return;
    
    setDrawLoading(true);
    try {
      const response = await fetch('/api/titles/draw', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pool, cost })
      });
      
      const result = await response.json();
      if (result.success) {
        alert(`🎉 恭喜获得头衔：${result.title.name}`);
        loadUserTitles();
      } else {
        alert(result.error || '抽取失败');
      }
    } catch (error) {
      alert('网络错误，请重试');
    } finally {
      setDrawLoading(false);
    }
  };

  const userStats = {
    unlocked: userTitles.filter(t => t.unlocked).length,
    equipped: user?.equippedTitles?.length || 0,
    total: ALL_TITLES.length
  };

  // 按稀有度分组头衔
  const groupedTitles = {
    R: ALL_TITLES.filter(t => t.rarity === 'R'),
    SR: ALL_TITLES.filter(t => t.rarity === 'SR'),
    SSR: ALL_TITLES.filter(t => t.rarity === 'SSR')
  };

  // 获取每个池子的前3个头衔用于展示
  const previewTitles = {
    R: groupedTitles.R.slice(0, 3),
    SR: groupedTitles.SR.slice(0, 3),
    SSR: groupedTitles.SSR.slice(0, 3)
  };

  // 检查用户是否拥有某个头衔
  const hasTitle = (titleId: string) => {
    return userTitles.some(t => t.id === titleId && t.unlocked);
  };

  // 提取标题中的emoji或使用默认emoji
  const getTitleEmoji = (title: Title) => {
    // 检查标题名称是否以emoji开头
    const emojiRegex = /^[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F900}-\u{1F9FF}\u{1F018}-\u{1F270}\u{238C}\u{2B06}\u{2B07}\u{2B05}\u{27A1}\u{2194}-\u{2199}\u{2195}\u{25AA}-\u{25AB}\u{25FE}\u{25FD}\u{25FB}\u{25FC}\u{2B50}\u{26A0}\u{26A1}\u{26AA}\u{26AB}\u{26BD}\u{26BE}\u{1F004}\u{1F0CF}\u{1F170}\u{1F171}\u{1F17E}\u{1F17F}\u{1F18E}\u{1F191}-\u{1F19A}\u{1F200}-\u{1F202}\u{1F21A}\u{1F22F}\u{1F232}-\u{1F23A}\u{1F250}\u{1F251}\u{1F300}-\u{1F320}\u{1F32D}-\u{1F335}\u{1F337}-\u{1F37C}\u{1F37E}-\u{1F393}\u{1F3A0}-\u{1F3F0}\u{1F400}-\u{1F43E}\u{1F440}\u{1F442}-\u{1F4FC}\u{1F4FF}-\u{1F53D}\u{1F54B}-\u{1F54E}\u{1F550}-\u{1F567}\u{1F57A}\u{1F595}\u{1F596}\u{1F5A4}\u{1F5FB}-\u{1F5FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6C5}\u{1F6CC}\u{1F6D0}-\u{1F6D2}\u{1F6EB}-\u{1F6EC}\u{1F6F4}-\u{1F6F9}\u{1F7E0}-\u{1F7EB}\u{1F910}-\u{1F93A}\u{1F93C}-\u{1F93E}\u{1F940}-\u{1F970}\u{1F973}-\u{1F976}\u{1F97A}-\u{1F97C}\u{1F97E}-\u{1F97F}\u{1F980}-\u{1F991}\u{1F992}-\u{1F997}\u{1F998}-\u{1F9A2}\u{1F9A5}-\u{1F9AA}\u{1F9AE}-\u{1F9CA}\u{1F9CD}-\u{1F9FF}\u{1FA70}-\u{1FA73}\u{1FA78}-\u{1FA7A}\u{1FA80}-\u{1FA82}\u{1FA90}-\u{1FA95}]/u;
    const match = title.name.match(emojiRegex);
    if (match) {
      return match[0];
    }
    
    // 根据稀有度返回默认emoji
    switch (title.rarity) {
      case 'R': return '📜';
      case 'SR': return '✨';
      case 'SSR': return '💎';
      case 'GUARANTEED': return '🏆';
      default: return '🏆';
    }
  };

  if (loading) {
    return (
      <UnifiedLayout title="我的头衔" backgroundIntensity="medium">
        <div style={{ textAlign: 'center', padding: '100px 20px' }}>
          <UnifiedLoading size={60} />
          <div style={{ 
            marginTop: '24px',
            color: 'rgba(255, 255, 255, 0.7)',
            fontSize: '16px'
          }}>
            正在加载档案馆...
          </div>
        </div>
      </UnifiedLayout>
    );
  }

  return (
    <UnifiedLayout title="🏆 我的头衔" backgroundIntensity="medium">
      {/* 统计卡片 */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
        gap: '20px',
        marginBottom: '32px'
      }}>
        <UnifiedCard variant="highlight" style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '32px', fontWeight: 'bold', color: cpColors.accent }}>  
            {userStats.unlocked}
          </div>
          <div style={{ color: 'rgba(255, 255, 255, 0.8)' }}>已解锁</div>
        </UnifiedCard>
        
        <UnifiedCard variant="highlight" style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '32px', fontWeight: 'bold', color: cpColors.secondary }}>  
            {userStats.equipped}
          </div>
          <div style={{ color: 'rgba(255, 255, 255, 0.8)' }}>已装备</div>
        </UnifiedCard>
        
        <UnifiedCard variant="highlight" style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '32px', fontWeight: 'bold', color: cpColors.primary }}>  
            {userStats.total}
          </div>
          <div style={{ color: 'rgba(255, 255, 255, 0.8)' }}>总数量</div>
        </UnifiedCard>
      </div>

      {/* 标签页导航 */}
      <div style={{
        display: 'flex',
        gap: '12px',
        marginBottom: '24px',
        flexWrap: 'wrap'
      }}>
        {[
          { id: 'collection', name: '🏆 头衔收藏', icon: '💎' },
          { id: 'pools', name: '🎲 卡池展示', icon: '🎰' },
          { id: 'equipped', name: '⭐ 当前头衔', icon: '👑' }
        ].map(tab => (
          <UnifiedButton
            key={tab.id}
            variant={activeTab === tab.id ? 'primary' : 'secondary'}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.icon} {tab.name}
          </UnifiedButton>
        ))}
      </div>

      {/* 内容区域 */}
      {activeTab === 'collection' && renderCollection()}
      {activeTab === 'pools' && renderPools()}
      {activeTab === 'equipped' && renderEquipped()}
    </UnifiedLayout>
  );

  function renderCollection() {
    return (
      <UnifiedCard title="头衔收藏" subtitle="查看所有已获得的头衔">
        {userTitles.filter(t => t.unlocked).length === 0 ? (
          <div style={{ textAlign: 'center', padding: '60px 20px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🏆</div>
            <div style={{ 
              fontSize: '18px', 
              marginBottom: '8px',
              color: 'rgba(255, 255, 255, 0.8)'
            }}>
              还没有头衔
            </div>
            <div style={{ 
              fontSize: '14px', 
              color: 'rgba(255, 255, 255, 0.6)'
            }}>
              去卡池展示页面抽取你的第一个头衔吧！
            </div>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(250px, 1fr))',
            gap: '16px'
          }}>
            {userTitles.filter(t => t.unlocked).map((title, idx) => (
              <div
                key={idx}
                style={{
                  padding: '16px',
                  borderRadius: '12px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  textAlign: 'center'
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                  {getTitleEmoji(title)}
                </div>
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: 'bold',
                  marginBottom: '4px',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}>
                  {title.name}
                </div>
                <UnifiedBadge 
                  variant={title.rarity === 'SSR' ? 'warning' : title.rarity === 'SR' ? 'info' : 'default'}
                  size="small"
                >
                  {title.rarity}
                </UnifiedBadge>
              </div>
            ))}
          </div>
        )}
      </UnifiedCard>
    );
  }

  function renderPools() {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
        {/* R池展示 */}
        <UnifiedCard 
          title="R池 - 边缘星轨" 
          subtitle="主攻R卡，大概率获得普通头衔，偶尔有惊喜"
          variant="default"
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px',
            marginBottom: '20px'
          }}>
            {previewTitles.R.map((title, index) => (
              <div
                key={index}
                style={{
                  padding: '16px',
                  borderRadius: '12px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  textAlign: 'center'
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                  {getTitleEmoji(title)}
                </div>
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: 'bold',
                  marginBottom: '4px',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}>
                  {title.name}
                </div>
                <UnifiedBadge 
                  variant="default"
                  size="small"
                >
                  R
                </UnifiedBadge>
              </div>
            ))}
          </div>
          
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ 
              fontSize: '18px', 
              marginBottom: '8px',
              color: 'rgba(255, 255, 255, 0.9)'
            }}>
              消耗：1张星轨召唤券
            </div>
            <div style={{ 
              fontSize: '14px', 
              marginBottom: '20px',
              color: 'rgba(255, 255, 255, 0.6)'
            }}>
              获得普通品质头衔 (80% R, 18% SR, 2% SSR)
            </div>
            <UnifiedButton
              variant="secondary"
              disabled={drawLoading}
              loading={drawLoading}
              onClick={() => handleDraw('R', 1)}
            >
              R池抽卡
            </UnifiedButton>
          </div>
        </UnifiedCard>

        {/* SR池展示 */}
        <UnifiedCard 
          title="SR池 - 命运轨迹" 
          subtitle="主攻SR卡，有近一半机会获得稀有头衔"
          variant="highlight"
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px',
            marginBottom: '20px'
          }}>
            {previewTitles.SR.map((title, index) => (
              <div
                key={index}
                style={{
                  padding: '16px',
                  borderRadius: '12px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  textAlign: 'center'
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                  {getTitleEmoji(title)}
                </div>
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: 'bold',
                  marginBottom: '4px',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}>
                  {title.name}
                </div>
                <UnifiedBadge 
                  variant="info"
                  size="small"
                >
                  SR
                </UnifiedBadge>
              </div>
            ))}
          </div>
          
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ 
              fontSize: '18px', 
              marginBottom: '8px',
              color: 'rgba(255, 255, 255, 0.9)'
            }}>
              消耗：1张星轨召唤券
            </div>
            <div style={{ 
              fontSize: '14px', 
              marginBottom: '20px',
              color: 'rgba(255, 255, 255, 0.6)'
            }}>
              获得稀有品质头衔 (50% R, 45% SR, 5% SSR)
            </div>
            <UnifiedButton
              variant="primary"
              disabled={drawLoading}
              loading={drawLoading}
              onClick={() => handleDraw('SR', 1)}
            >
              SR池抽卡
            </UnifiedButton>
          </div>
        </UnifiedCard>

        {/* SSR池展示 */}
        <UnifiedCard 
          title="SSR池 - 现实引力" 
          subtitle="挑战SSR，虽然很难，但中奖率是最高的"
          variant="highlight"
        >
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '16px',
            marginBottom: '20px'
          }}>
            {previewTitles.SSR.map((title, index) => (
              <div
                key={index}
                style={{
                  padding: '16px',
                  borderRadius: '12px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  textAlign: 'center'
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                  {getTitleEmoji(title)}
                </div>
                <div style={{ 
                  fontSize: '16px', 
                  fontWeight: 'bold',
                  marginBottom: '4px',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}>
                  {title.name}
                </div>
                <UnifiedBadge 
                  variant="warning"
                  size="small"
                >
                  SSR
                </UnifiedBadge>
              </div>
            ))}
          </div>
          
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ 
              fontSize: '18px', 
              marginBottom: '8px',
              color: 'rgba(255, 255, 255, 0.9)'
            }}>
              消耗：1张星轨召唤券
            </div>
            <div style={{ 
              fontSize: '14px', 
              marginBottom: '20px',
              color: 'rgba(255, 255, 255, 0.6)'
            }}>
              获得传说品质头衔 (30% R, 60% SR, 10% SSR)
            </div>
            <UnifiedButton
              variant="primary"
              disabled={drawLoading}
              loading={drawLoading}
              onClick={() => handleDraw('SSR', 1)}
            >
              SSR池抽卡
            </UnifiedButton>
          </div>
        </UnifiedCard>
      </div>
    );
  }

  function renderEquipped() {
    return (
      <UnifiedCard title="当前头衔" subtitle="正在使用的头衔">
        <div style={{ textAlign: 'center', padding: '60px 20px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>👑</div>
          <div style={{ 
            fontSize: '18px', 
            marginBottom: '8px',
            color: 'rgba(255, 255, 255, 0.8)'
          }}>
            装备功能开发中
          </div>
          <div style={{ 
            fontSize: '14px', 
            color: 'rgba(255, 255, 255, 0.6)'
          }}>
            敬请期待头衔装备系统！
          </div>
        </div>
      </UnifiedCard>
    );
  }
}