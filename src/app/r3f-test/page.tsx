'use client';

import React from 'react';
import { Canvas } from '@react-three/fiber';

export default function R3FTestPage() {
  return (
    <div className="w-full h-screen">
      <Canvas camera={{ position: [3, 3, 3], fov: 60 }}>
        <ambientLight intensity={0.5} />
        <directionalLight position={[5, 5, 5]} intensity={1} />
        <mesh rotation={[0.4, 0.8, 0]}>
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#66ccff" />
        </mesh>
      </Canvas>
    </div>
  );
}



