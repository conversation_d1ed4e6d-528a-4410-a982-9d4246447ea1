import type { Metadata } from "next";
import '@/styles/globals.css';
import './globals.css';
import GlobalParticleBackground from '@/components/GlobalParticleBackground';

export const metadata: Metadata = {
  title: "逆线 Nixian - 3D逆线宇宙",
  description: "在无数逆转的线里，重逢唯一的你！每一颗星辰，都是一份回响——而你，点亮了整个宇宙！",
  keywords: "3D, 粒子, 宇宙, 逆转, CP, 创作, 分享",
  icons: {
    icon: '/favicon.svg',
  },
  openGraph: {
    title: "逆线 Nixian - 3D逆线宇宙",
    description: "在无数逆转的线里，重逢唯一的你！每一颗星辰，都是一份回响——而你，点亮了整个宇宙！",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#666BCE" />
      </head>
      <body style={{ background: '#0a0a1a', margin: 0, padding: 0 }}>
        <GlobalParticleBackground />
        <div style={{ position: 'relative', zIndex: 1 }}>
          {children}
        </div>
      </body>
    </html>
  );
}