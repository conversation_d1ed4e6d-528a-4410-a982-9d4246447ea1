'use client';

import React, { useEffect, useState } from 'react';

// 纯净的粒子测试页面，不包含任何其他组件
export default function CleanParticleTest() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div style={{ 
        width: '100vw', 
        height: '100vh', 
        background: '#0a0a1a',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white'
      }}>
        <p>正在加载...</p>
      </div>
    );
  }

  // 生成100个粒子
  const particles = Array.from({ length: 100 }, (_, i) => {
    const colors = ['#666BCE', '#C2A8F2', '#FFD64F'];
    const color = colors[i % 3];
    
    return (
      <div
        key={i}
        className="particle"
        style={{
          position: 'absolute',
          width: '4px',
          height: '4px',
          backgroundColor: color,
          borderRadius: '50%',
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          animation: `float ${5 + Math.random() * 5}s infinite linear`,
          opacity: 0.7,
          boxShadow: `0 0 10px ${color}`,
        }}
      />
    );
  });

  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* CSS 动画 */}
      <style jsx>{`
        @keyframes float {
          0% { 
            transform: translateY(0px) translateX(0px) rotate(0deg); 
          }
          25% { 
            transform: translateY(-20px) translateX(10px) rotate(90deg); 
          }
          50% { 
            transform: translateY(0px) translateX(20px) rotate(180deg); 
          }
          75% { 
            transform: translateY(20px) translateX(10px) rotate(270deg); 
          }
          100% { 
            transform: translateY(0px) translateX(0px) rotate(360deg); 
          }
        }
      `}</style>
      
      {particles}
      
      {/* 简单的文字显示 */}
      <div style={{
        position: 'absolute',
        top: '20px',
        left: '20px',
        color: 'white',
        background: 'rgba(0,0,0,0.7)',
        padding: '15px',
        borderRadius: '5px',
        zIndex: 100
      }}>
        <h1 style={{ margin: 0, fontSize: '18px' }}>🌟 纯净粒子测试</h1>
        <p style={{ margin: '5px 0', fontSize: '14px' }}>如果你看到彩色粒子在移动，说明粒子系统工作正常</p>
        <p style={{ margin: '5px 0', fontSize: '14px' }}>颜色：紫色(#666BCE) + 紫晶色(#C2A8F2) + 柠檬黄(#FFD64F)</p>
        <p style={{ margin: '5px 0', fontSize: '12px', color: '#ccc' }}>
          这个页面不包含任何其他组件，纯粹测试粒子效果
        </p>
      </div>
    </div>
  );
}