'use client';

import { useEffect, useState } from 'react';

interface EchoItem { id: string; type: 'idea'|'bug'; content: string; author?: string; createdAt: string; likes: number, imageUrl?: string }

type Tab = 'idea'|'bug';

type Sort = 'time'|'likes';

export default function EchoPage(){
  const [tab, setTab] = useState<Tab>('idea');
  const [sort, setSort] = useState<Sort>('time');
  const [items, setItems] = useState<EchoItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [text, setText] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [liked, setLiked] = useState<Set<string>>(new Set());
  const [adminToken, setAdminToken] = useState<string>('');
  const [submitting, setSubmitting] = useState(false);

  const load = async (t: Tab) => {
    setLoading(true);
    try{
      const r = await fetch(`/api/echo?type=${t}`);
      const j = await r.json();
      const list = (j.items||[]) as EchoItem[];
      setItems(list.sort((a,b)=> sort==='likes' ? (b.likes||0)-(a.likes||0) : new Date(b.createdAt).getTime()-new Date(a.createdAt).getTime()));
    } finally{ setLoading(false); }
  };

  useEffect(()=>{
    // init liked set from localStorage
    try{
      const raw = localStorage.getItem('liked_echo');
      if (raw) setLiked(new Set(JSON.parse(raw)));
      const at = localStorage.getItem('adminToken')||'';
      if (at) setAdminToken(at);
    }catch{}
    load(tab);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(()=>{ load(tab); }, [tab, sort]);

  const submit = async () => {
    if (!text.trim() && !imageUrl.trim()) { alert('请填写内容或选择图片'); return; }
    setSubmitting(true);
    try{
      const u = JSON.parse(localStorage.getItem('user')||'{}');
      const res = await fetch('/api/echo', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ type: tab, content: text, author: u?.name || u?.id || 'guest', imageUrl }) });
      const j = await res.json();
      if (j?.ok) { setText(''); setImageUrl(''); load(tab); }
      else { alert(j?.error||'提交失败'); }
    } finally { setSubmitting(false); }
  };

  const onChooseFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (!f) return;
    const fd = new FormData();
    fd.append('file', f);
    const r = await fetch('/api/uploads', { method:'POST', body: fd });
    const j = await r.json();
    if (j?.ok) setImageUrl(j.url);
  };

  const toggleLike = async (id: string) => {
    const u = JSON.parse(localStorage.getItem('user')||'{}');
    const uid = u?.id || 'guest';
    const isLiked = liked.has(id);
    const method = isLiked ? 'DELETE' : 'POST';
    const r = await fetch(`/api/echo/like?id=${encodeURIComponent(id)}`, { method, headers:{ 'x-user-id': uid } });
    const j = await r.json();
    if (j?.ok) {
      const ns = new Set(liked);
      if (j.liked) ns.add(id); else ns.delete(id);
      setLiked(ns);
      try{ localStorage.setItem('liked_echo', JSON.stringify(Array.from(ns))); }catch{}
      load(tab);
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-10">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-3xl font-extrabold bg-gradient-to-r from-[#666BCE] to-[#FFD64F] bg-clip-text text-transparent">回响之墙</h1>
        <div className="text-sm text-gray-600">
          排序：
          <select value={sort} onChange={e=>setSort(e.target.value as Sort)} className="border rounded px-2 py-1 ml-1">
            <option value="time">按时间（新→旧）</option>
            <option value="likes">按点赞（多→少）</option>
          </select>
        </div>
      </div>

      <div className="flex bg-gray-100 rounded-xl p-1 mb-4 w-fit">
        <button onClick={()=>setTab('idea')} className={`px-4 py-2 rounded-lg text-sm font-medium ${tab==='idea'?'bg-white text-[#666BCE] shadow':'text-gray-600 hover:text-[#666BCE]'}`}>脑洞与建议</button>
        <button onClick={()=>setTab('bug')} className={`px-4 py-2 rounded-lg text-sm font-medium ${tab==='bug'?'bg-white text-[#666BCE] shadow':'text-gray-600 hover:text-[#666BCE]'}`}>BUG与修复</button>
      </div>

      <div className="mb-4 bg-white border rounded-xl p-4">
        <textarea value={text} onChange={e=>setText(e.target.value)} placeholder={tab==='idea'?'分享你的灵感、建议或期待...':'反馈遇到的问题，或提出修复建议...'} className="w-full border rounded p-3 min-h-[120px]" />
        <div className="mt-2 flex items-center justify-between">
          <div>
            <input type="file" accept="image/*" onChange={onChooseFile} />
            {imageUrl ? (
              <span className="ml-2 text-xs text-gray-500 inline-flex items-center gap-2">
                已添加图片 <button onClick={()=>setImageUrl('')} className="px-2 py-0.5 rounded bg-gray-200 hover:bg-gray-300">移除</button>
              </span>
            ) : null}
          </div>
          <button disabled={submitting} onClick={submit} className="px-4 py-2 rounded text-white disabled:opacity-50" style={{ background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)' }}>{submitting?'处理中...':'提交'}</button>
        </div>
        {imageUrl ? (
          <div className="mt-3">
            <img src={imageUrl} alt="预览" className="max-h-48 rounded border" />
          </div>
        ) : null}
      </div>

      {loading ? <div>加载中...</div> : (
        items.length===0 ? <div className="text-gray-400">暂无内容</div> : (
          <div className="space-y-3">
            {items.map(it => (
              <div key={it.id} className="bg-white border rounded-xl p-4">
                <div className="flex items-center justify-between mb-1">
                  <div className="text-xs text-gray-400">{new Date(it.createdAt).toLocaleString()} · {it.author || 'guest'}</div>
                 {adminToken ? (
                   <button onClick={async()=>{
                     const r = await fetch(`/api/echo?id=${encodeURIComponent(it.id)}`, { method:'DELETE', headers:{ 'x-admin-token': adminToken } });
                     const j = await r.json();
                     if (j?.ok) load(tab); else alert(j?.error||'删除失败');
                   }} className="px-2 py-1 rounded text-white" style={{ background:'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)' }}>删除</button>
                 ) : null}
                </div>
                {it.imageUrl ? <div className="mb-2"><img src={it.imageUrl} alt="" className="max-h-64 rounded" /></div> : null}
                <div className="text-gray-800 whitespace-pre-wrap">{it.content}</div>
                <div className="mt-2"><button onClick={()=>toggleLike(it.id)} className={`px-3 py-1 rounded text-white ${liked.has(it.id)?'opacity-100':'opacity-90 hover:opacity-100'}`} style={{ background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)' }}>{liked.has(it.id)?'💟':'👍'} {it.likes}</button></div>
              </div>
            ))}
          </div>
        )
      )}
    </div>
  );
} 