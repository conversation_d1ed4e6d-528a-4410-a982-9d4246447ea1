'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { ArrowLeft, BookOpen, Sparkles, Globe, PlusCircle, Zap } from 'lucide-react';

function IntentClient() {
  const [selectedIntent, setSelectedIntent] = useState('');
  const [userPrompt, setUserPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [workTitle, setWorkTitle] = useState('');
  const [lengthTier, setLengthTier] = useState<'fragment'|'standard'|'deep'>('deep');
  
  const searchParams = useSearchParams();
  const router = useRouter();

  useEffect(() => {
    const work = searchParams.get('work');
    if (work) setWorkTitle(work);
    const defaultIntent = searchParams.get('default_intent');
    if (defaultIntent && ['sequel','au','style','detail'].includes(defaultIntent)) setSelectedIntent(defaultIntent);
    const prefill = searchParams.get('prefill');
    if (prefill) setUserPrompt(prefill);
  }, [searchParams]);

  const intents = [
    { id: 'sequel', title: '续写番外', description: '为原作续写新的章节和故事', icon: BookOpen, gradient: 'from-purple-500 to-blue-600', examples: ['魏无羡第一次带蓝忘机回云梦','蓝忘机喝醉后，又干了什么好事','他们在现代重逢的第一次见面'] },
    { id: 'au', title: '平行宇宙(AU)', description: '将角色放到全新的世界设定中', icon: Globe, gradient: 'from-blue-500 to-teal-600', examples: ['如果他们在现代校园相遇会怎样？','星际时代的他们会有什么故事？','如果他们是普通的上班族...'] },
    { id: 'style', title: '风格模仿，新故事', description: '用原作的文笔和感觉，写一个全新的故事', icon: Sparkles, gradient: 'from-teal-500 to-green-600', examples: ['用相同的写作风格，创作新角色的故事','保持原作氛围，换个时代背景','用同样的笔调，讲述另一段传奇'] },
    { id: 'detail', title: '补完细节', description: '填补原文中的留白和跳跃', icon: PlusCircle, gradient: 'from-green-500 to-yellow-600', examples: ['原文中提到的"几个月后"发生了什么？','那段没有写详细的重要对话','他们分别时心理活动的补完'] }
  ];

  const handleIntentSelect = (intentId: string) => setSelectedIntent(intentId);

  const handleGenerate = () => {
    if (!selectedIntent || !userPrompt.trim()) return;
    setIsGenerating(true);
    setTimeout(() => {
      router.push(`/generate?work=${encodeURIComponent(workTitle)}&intent=${selectedIntent}&tier=${lengthTier}&prompt=${encodeURIComponent(userPrompt)}`);
    }, 1000);
  };

  const selectedIntentData = intents.find(intent => intent.id === selectedIntent);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* 导航栏 */}
      <nav className="border-b border-white/20 bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-purple-600 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>返回</span>
            </button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-blue-600 rounded-full flex items-center justify-center">
                <BookOpen className="w-4 h-4 text白" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                续界
              </h1>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button className="text-gray-600 hover:text紫-600 transition-colors">登录</button>
            <button className="bg-gradient-to-r from-purple-500 to-blue-600 text白 px-4 py-2 rounded-full hover:shadow-lg transition-all">
              注册
            </button>
          </div>
        </div>
      </nav>

      <main className="max-w-4xl mx-auto px-4 py-12">
        {/* 标题区域 */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4 text-gray-800">为《{workTitle}》选择创作方向</h1>
          <p className="text-lg text-gray-600">选择你想要的创作类型，让AI为你续写专属故事</p>
        </div>

        {/* 意图选择卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-12">
          {intents.map((intent) => (
            <div key={intent.id} onClick={() => handleIntentSelect(intent.id)} className={`relative p-6 rounded-2xl cursor-pointer transition-all duration-300 ${selectedIntent === intent.id ? 'ring-4 ring-purple-200 shadow-2xl transform scale-105' : 'shadow-lg hover:shadow-xl hover:transform hover:scale-102'}`}>
              <div className={`absolute inset-0 bg-gradient-to-br ${intent.gradient} rounded-2xl opacity-10`}></div>
              <div className="relative z-10">
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`w-12 h-12 bg-gradient-to-br ${intent.gradient} rounded-full flex items-center justify-center`}>
                    <intent.icon className="w-6 h-6 text白" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">{intent.title}</h3>
                    <p className="text-gray-600 text-sm">{intent.description}</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <p className="text-sm font-semibold text-gray-700">示例场景：</p>
                  {intent.examples.map((example, index) => (
                    <div key={index} className="text-sm text-gray-600 bg白/50 rounded-lg p-2">"{example}"</div>
                  ))}
                </div>
              </div>
              {selectedIntent === intent.id && (
                <div className="absolute top-4 right-4 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg白 rounded-full"></div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* 用户输入区域 */}
        {selectedIntent && (
          <div className="bg白 rounded-2xl shadow-lg p-8 mb-8">
            <div className="flex items-center space-x-3 mb-6">
              {selectedIntentData && <selectedIntentData.icon className="w-6 h-6 text紫-600" />}
              <h2 className="text-2xl font-bold text-gray-800">描述你想看到的场景</h2>
            </div>
            <p className="text-gray-600 mb-4">请简单描述你希望看到的具体场景或情节，我们的AI将基于原作风格为你创作。</p>
            <textarea value={userPrompt} onChange={(e) => setUserPrompt(e.target.value)} className="w-full h-32 p-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-purple-200 focus:border-purple-400 outline-none transition-all resize-none" placeholder={selectedIntentData ? `例如：${selectedIntentData.examples[0]}` : '请描述你想看到的场景...'} />
            <div className="mt-6">
              <div className="text-sm font-semibold text-gray-700 mb-2">选择创作长度（将消耗灵感）</div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                <label className={`border rounded-xl p-3 cursor-pointer ${lengthTier==='fragment'?'border-purple-400 bg-purple-50':'border-gray-200'}`}>
                  <input type="radio" name="tier" className="mr-2" checked={lengthTier==='fragment'} onChange={()=>setLengthTier('fragment')} />
                  <span className="font-medium">灵感片段</span>
                  <span className="ml-2 text-xs text-gray-500">约500字 · 消耗 5 灵感</span>
                </label>
                <label className={`border rounded-xl p-3 cursor-pointer ${lengthTier==='standard'?'border-purple-400 bg-purple-50':'border-gray-200'}`}>
                  <input type="radio" name="tier" className="mr-2" checked={lengthTier==='standard'} onChange={()=>setLengthTier('standard')} />
                  <span className="font-medium">标准番外</span>
                  <span className="ml-2 text-xs text-gray-500">约1500字 · 消耗 8 灵感</span>
                </label>
                <label className={`border rounded-xl p-3 cursor-pointer ${lengthTier==='deep'?'border-purple-400 bg-purple-50':'border-gray-200'}`}>
                  <input type="radio" name="tier" className="mr-2" checked={lengthTier==='deep'} onChange={()=>setLengthTier('deep')} />
                  <span className="font-medium">深度章节</span>
                  <span className="ml-2 text-xs text-gray-500">约3000字 · 消耗 10 灵感</span>
                </label>
              </div>
            </div>
            <div className="flex justify-between items-center mt-6">
              <div className="text-sm text-gray-500">提示：根据选择的长度将消耗相应灵感</div>
              <button onClick={handleGenerate} disabled={!userPrompt.trim() || isGenerating} className="bg-gradient-to-r from-purple-500 to-blue-600 text白 px-8 py-3 rounded-xl hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
                {isGenerating ? (<><div className="animate-spin rounded-full h-4 w-4 border-2 border白 border-t-transparent"></div><span>正在生成...</span></>) : (<><Zap className="w-4 h-4" /><span>立即生成</span></>)}
              </button>
            </div>
          </div>
        )}

        {/* 提示卡片 */}
        <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">💡 创作小贴士</h3>
          <ul className="space-y-2 text-gray-600">
            <li className="flex items-start space-x-2"><span className="w-2 h-2 bg紫-400 rounded-full mt-2 flex-shrink-0"></span><span>描述越具体，生成的故事越贴合你的期望</span></li>
            <li className="flex items-start space-x-2"><span className="w-2 h-2 bg蓝-400 rounded-full mt-2 flex-shrink-0"></span><span>可以指定特定的人物、场景、情绪氛围</span></li>
            <li className="flex items-start space-x-2"><span className="w-2 h-2 bg青-400 rounded-full mt-2 flex-shrink-0"></span><span>AI将严格保持原作的人物性格，确保不会OOC</span></li>
          </ul>
        </div>
      </main>
    </div>
  );
}

export default function IntentPage() {
  return (
    <Suspense fallback={<div />}> 
      <IntentClient />
    </Suspense>
  );
}