'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import UnifiedLayout from '@/components/UnifiedLayout';
import { 
  UnifiedButton, 
  UnifiedCard, 
  UnifiedInput,
  UnifiedBadge,
  cpColors 
} from '@/components/ui/UnifiedComponents';

// 弹幕轨道数量
const DANMAKU_LANES = 25;

interface DanmakuMessage {
  id: string;
  content: string;
  author: string;
  titles: string[];
  timestamp: number;
  lane: number;
  speed: number;
  color: string;
}

interface User {
  id: string;
  name: string;
  equippedTitles: string[];
}

export default function ForumPage() {
  const router = useRouter();
  const danmakuContainerRef = useRef<HTMLDivElement>(null);
  const [user, setUser] = useState<User | null>(null);
  const [messages, setMessages] = useState<DanmakuMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [occupiedLanes, setOccupiedLanes] = useState<Set<number>>(new Set());
  const [onlineCount, setOnlineCount] = useState(Math.floor(Math.random() * 50) + 20);
  const [showChat, setShowChat] = useState(false);
  const [danmakuSpeed, setDanmakuSpeed] = useState(12000); // 弹幕速度控制
  const [speedLabel, setSpeedLabel] = useState('正常'); // 速度标签

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
    
    // 模拟在线人数变化
    const interval = setInterval(() => {
      setOnlineCount(prev => {
        const change = Math.floor(Math.random() * 5) - 2;
        return Math.max(15, Math.min(100, prev + change));
      });
    }, 10000);

    // 模拟弹幕消息
    const messageInterval = setInterval(() => {
      addRandomMessage();
    }, 3000);
    
    return () => {
      clearInterval(interval);
      clearInterval(messageInterval);
    };
  }, []);

  const addRandomMessage = () => {
    const sampleMessages = [
      '池骋和吴所畏的CP感太强了！',
      '这个剧情转折太精彩了',
      '逆线真的好看啊！',
      '求更新求更新！',
      '池畏CP锁死！',
      '这个网站做得好棒',
      '粒子效果太美了',
      '期待下一章节',
      '作者大大辛苦了',
      '来支持一波！'
    ];

    const lane = getAvailableLane();
    const newMessage: DanmakuMessage = {
      id: Date.now().toString() + Math.random(),
      content: sampleMessages[Math.floor(Math.random() * sampleMessages.length)],
      author: `用户${Math.floor(Math.random() * 1000)}`,
      titles: [],
      timestamp: Date.now(),
      lane,
      speed: 0.8 + Math.random() * 0.4, // 从1.5调整为0.8-1.2，使弹幕更慢
      color: [cpColors.primary, cpColors.secondary, cpColors.accent][Math.floor(Math.random() * 3)]
    };

    setMessages(prev => [...prev.slice(-50), newMessage]);
    
    setOccupiedLanes(prev => new Set([...prev, lane]));
    setTimeout(() => {
      setOccupiedLanes(prev => {
        const newSet = new Set(prev);
        newSet.delete(lane);
        return newSet;
      });
    }, danmakuSpeed);
  };

  // 速度控制函数
  const changeSpeed = (speed: number, label: string) => {
    setDanmakuSpeed(speed);
    setSpeedLabel(label);
  };

  const getAvailableLane = () => {
    for (let i = 0; i < DANMAKU_LANES; i++) {
      if (!occupiedLanes.has(i)) {
        return i;
      }
    }
    return Math.floor(Math.random() * DANMAKU_LANES);
  };

  const sendMessage = () => {
    if (!inputMessage.trim() || !user) return;
    
    const trimmedMessage = inputMessage.trim().slice(0, 50);
    const lane = getAvailableLane();
    
    const newMessage: DanmakuMessage = {
      id: Date.now().toString(),
      content: trimmedMessage,
      author: user.name,
      titles: user.equippedTitles || [],
      timestamp: Date.now(),
      lane,
      speed: 0.8, // 从1.5调整为0.8，使用户发送的弹幕更慢
      color: cpColors.accent
    };

    setMessages(prev => [...prev.slice(-50), newMessage]);
    setInputMessage('');
    
    setOccupiedLanes(prev => new Set([...prev, lane]));
    setTimeout(() => {
      setOccupiedLanes(prev => {
        const newSet = new Set(prev);
        newSet.delete(lane);
        return newSet;
      });
    }, danmakuSpeed);
  };

  return (
    <div style={{
      position: 'relative',
      minHeight: '100vh'
    }}>
      <UnifiedLayout 
        title="💬 实时弹幕" 
        backgroundIntensity="subtle"
      >
      {/* 控制面板 */}
      <div style={{ marginBottom: '20px' }}>
        <UnifiedCard>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: '16px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px'
            }}>
              <UnifiedBadge variant="success">
                🟢 在线 {onlineCount} 人
              </UnifiedBadge>
              
              <UnifiedButton
                variant="secondary"
                size="small"
                onClick={() => setShowChat(!showChat)}
              >
                {showChat ? '隐藏' : '显示'}聊天室
              </UnifiedButton>
              
              {/* 弹幕速度控制 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <span style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>
                  弹幕速度: {speedLabel}
                </span>
                <UnifiedButton
                  variant={danmakuSpeed === 20000 ? 'primary' : 'secondary'}
                  size="small"
                  onClick={() => changeSpeed(20000, '慢速')}
                >
                  慢
                </UnifiedButton>
                <UnifiedButton
                  variant={danmakuSpeed === 12000 ? 'primary' : 'secondary'}
                  size="small"
                  onClick={() => changeSpeed(12000, '正常')}
                >
                  正常
                </UnifiedButton>
                <UnifiedButton
                  variant={danmakuSpeed === 6000 ? 'primary' : 'secondary'}
                  size="small"
                  onClick={() => changeSpeed(6000, '快速')}
                >
                  快
                </UnifiedButton>
              </div>
            </div>

            {user ? (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '8px',
                color: 'rgba(255, 255, 255, 0.8)'
              }}>
                <span>👋 {user.name}</span>
                {user.equippedTitles?.map((title, idx) => (
                  <UnifiedBadge key={idx} size="small">
                    {title}
                  </UnifiedBadge>
                ))}
              </div>
            ) : (
              <UnifiedButton 
                variant="primary"
                size="small"
                onClick={() => router.push('/auth')}
              >
                登录参与讨论
              </UnifiedButton>
            )}
          </div>
        </UnifiedCard>
      </div>

      {/* 弹幕显示区域 */}
      <UnifiedCard 
        style={{ 
          position: 'relative',
          height: '400px',
          overflow: 'hidden',
          marginBottom: '20px'
        }}
      >
        <div
          ref={danmakuContainerRef}
          style={{
            position: 'relative',
            width: '100%',
            height: '100%',
            background: 'transparent',
            borderRadius: '8px'
          }}
        >
          {/* 弹幕消息 */}
          {messages.map((message) => (
            <div
              key={message.id}
              style={{
                position: 'absolute',
                top: `${(message.lane + 1) * (100 / (DANMAKU_LANES + 1))}%`,
                right: '-100%',
                whiteSpace: 'nowrap',
                color: message.color,
                fontSize: '14px',
                fontWeight: 'bold',
                textShadow: '1px 1px 2px rgba(0, 0, 0, 0.8)',
                animation: `danmaku-move ${10 / message.speed}s linear forwards`,
                zIndex: 5,
                transform: 'translateY(-50%)'
              }}
            >
              <span style={{ 
                background: 'transparent',
                padding: '4px 8px',
                borderRadius: '12px',
                backdropFilter: 'blur(4px)'
              }}>
                {message.author}: {message.content}
              </span>
            </div>
          ))}

          {/* 无弹幕时的提示 */}
          {messages.length === 0 && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              color: 'rgba(255, 255, 255, 0.5)'
            }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>💬</div>
              <div>等待弹幕消息...</div>
            </div>
          )}
        </div>
      </UnifiedCard>

      {/* 发送消息区域 */}
      <UnifiedCard title="发送弹幕">
        <div style={{
          display: 'flex',
          gap: '12px',
          alignItems: 'flex-end'
        }}>
          <div style={{ flex: 1 }}>
            <UnifiedInput
              placeholder={user ? "输入弹幕内容（最多50字）..." : "请先登录后发送弹幕"}
              value={inputMessage}
              onChange={setInputMessage}
              disabled={!user}
              maxLength={50}
            />
            <div style={{
              fontSize: '12px',
              color: 'rgba(255, 255, 255, 0.5)',
              marginTop: '4px'
            }}>
              {inputMessage.length}/50
            </div>
          </div>
          
          <UnifiedButton
            variant="primary"
            onClick={sendMessage}
            disabled={!user || !inputMessage.trim()}
          >
            发送 🚀
          </UnifiedButton>
        </div>
      </UnifiedCard>

      {/* 聊天室面板 */}
      {showChat && (
        <UnifiedCard title="聊天室" style={{ marginTop: '20px' }}>
          <div style={{
            height: '300px',
            overflowY: 'auto',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '8px',
            padding: '12px',
            backgroundColor: 'transparent'
          }}>
            {messages.slice(-20).map((message, idx) => (
              <div
                key={message.id}
                style={{
                  marginBottom: '12px',
                  padding: '8px 12px',
                  borderRadius: '12px',
                  backgroundColor: 'transparent',
                  border: '1px solid rgba(255, 255, 255, 0.1)'
                }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  marginBottom: '4px'
                }}>
                  <span style={{
                    color: message.color,
                    fontWeight: 'bold',
                    fontSize: '14px'
                  }}>
                    {message.author}
                  </span>
                  {message.titles?.map((title, titleIdx) => (
                    <UnifiedBadge key={titleIdx} size="small">
                      {title}
                    </UnifiedBadge>
                  ))}
                  <span style={{
                    fontSize: '11px',
                    color: 'rgba(255, 255, 255, 0.4)',
                    marginLeft: 'auto'
                  }}>
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                <div style={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  fontSize: '14px',
                  lineHeight: 1.4
                }}>
                  {message.content}
                </div>
              </div>
            ))}
          </div>
        </UnifiedCard>
      )}

      {/* CSS动画 */}
      <style jsx>{`
        @keyframes danmaku-move {
          from {
            right: -100%;
          }
          to {
            right: 100%;
          }
        }
      `}</style>
    </UnifiedLayout>
    </div>
  );
}