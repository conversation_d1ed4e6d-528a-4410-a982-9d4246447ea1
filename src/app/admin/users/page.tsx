"use client";
import { useEffect, useState } from 'react';

export default function AdminUsersPage(){
  const [items, setItems] = useState<any[]>([]);
  const [q, setQ] = useState('');
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);

  async function refresh(){
    if(!token) return;
    setLoading(true);
    try{
      const r = await fetch(`/api/admin/users?q=${encodeURIComponent(q)}`, { headers:{ 'x-admin-token': token } });
      const j = await r.json();
      if (j?.ok) setItems(j.items||[]); else alert(j?.error||'加载失败');
    } finally { setLoading(false); }
  }

  useEffect(()=>{ refresh(); }, [token]);

  async function adjust(userId: string, inputId: string, remarkId: string){
    const valEl = document.getElementById(inputId) as HTMLInputElement|null;
    const rmEl = document.getElementById(remarkId) as HTMLInputElement|null;
    const delta = valEl?.value ? Number(valEl.value) : 0;
    const remark = rmEl?.value || '';
    if (!Number.isFinite(delta) || delta===0){ alert('请输入非零数字'); return; }
    const r = await fetch('/api/admin/users/adjust', { method:'POST', headers:{ 'Content-Type':'application/json','x-admin-token': token }, body: JSON.stringify({ userId, delta, allowNegative: true, remark }) });
    const j = await r.json();
    if (j?.ok){ alert('已调账'); refresh(); } else alert(j?.error||'调账失败');
  }

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">用户管理</h1>
      <div className="flex gap-2 items-center mb-4">
        <input placeholder="管理员令牌" value={token} onChange={e=>setToken(e.target.value)} className="border rounded px-3 py-2 w-64" />
        <input placeholder="搜索 用户ID/昵称/联系方式" value={q} onChange={e=>setQ(e.target.value)} className="border rounded px-3 py-2 flex-1" />
        <button onClick={refresh} className="px-4 py-2 rounded bg-[#666BCE] text-white">搜索</button>
      </div>
      {loading ? <div>加载中...</div> : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {items.map(u=> (
            <div key={u.id} className="border rounded-xl p-4 bg-white">
              <div className="flex items-center justify-between">
                <div className="font-semibold">{u.name} <span className="text-gray-500 text-sm">(ID: {u.displayId || u.id})</span></div>
                <div className="text-sm text-gray-600">注册：{new Date(u.createdAt).toLocaleString()}</div>
              </div>
              <div className="mt-1 text-sm text-gray-700">
                <div>联系方式：{u.contact} ({u.contactType})</div>
                <div>真实ID：{u.id}</div>
                {u.inviteCode && <div>邀请码：{u.inviteCode}</div>}
              </div>
              <div className="mt-1 text-sm">余额：<span className="font-bold">{u.credits}</span></div>
              {u.subscription ? (
                <div className="mt-2 p-2 rounded bg-violet-50 text-sm">
                  <div>订阅：{u.subscription.plan} · {u.subscription.periodMonths}个月</div>
                  <div>开始：{new Date(u.subscription.startedAt).toLocaleDateString()}，到期：{new Date(u.subscription.expiresAt).toLocaleDateString()}</div>
                  <div>订阅钱包：{u.subscription.walletCredits}，每月加赠：{u.subscription.perMonthBonus}，剩余发放月数：{u.subscription.bonusMonthsRemaining}</div>
                  <div>下次发放：{new Date(u.subscription.bonusNextAt).toLocaleDateString()}</div>
                </div>
              ) : <div className="mt-2 text-sm text-gray-500">无订阅</div>}
              <div className="mt-3 border-t pt-2">
                <div className="text-xs text-gray-600 mb-1">调账（支持正负）</div>
                <div className="flex gap-2 items-center">
                  <input id={`v_${u.id}`} type="number" step="1" placeholder="如 -20 或 50" className="border rounded px-2 py-1 w-32" />
                  <input id={`rm_${u.id}`} placeholder="备注（必填）" className="border rounded px-2 py-1 flex-1" />
                  <button onClick={()=>adjust(u.id, `v_${u.id}`, `rm_${u.id}`)} className="px-3 py-1 rounded bg-indigo-600 text-white">执行</button>
                </div>
              </div>
              <UserAudit userId={u.id} token={token} />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function UserAudit({ userId, token }:{ userId:string; token:string }){
  const [logs, setLogs] = useState<any[]>([]);
  useEffect(()=>{ (async()=>{
    if(!token) return;
    const r = await fetch(`/api/admin/users/audit?userId=${encodeURIComponent(userId)}`, { headers:{ 'x-admin-token': token } });
    const j = await r.json();
    if (j?.ok) setLogs(j.items||[]);
  })(); }, [userId, token]);
  if (!logs.length) return null;
  return (
    <div className="mt-3 border-t pt-2 text-sm">
      <div className="text-xs text-gray-600 mb-1">调账记录</div>
      <ul className="space-y-1 max-h-40 overflow-auto pr-1">
        {logs.map((l:any)=>(
          <li key={l.id} className="flex justify-between gap-2">
            <span className="text-gray-600">{new Date(l.createdAt).toLocaleString()}</span>
            <span className={l.delta>=0? 'text-green-700':'text-red-700'}>{l.delta>=0? '+':''}{l.delta}</span>
            <span className="flex-1 text-right text-gray-700 truncate" title={l.remark}>{l.remark}</span>
          </li>
        ))}
      </ul>
    </div>
  );
} 