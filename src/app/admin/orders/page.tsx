"use client";
import { useEffect, useState } from 'react';

function statusClasses(s: string){
  if (s === 'COMPLETED') return 'border-green-200 bg-green-50';
  if (s === 'FAILED') return 'border-red-200 bg-red-50';
  return 'border-amber-200 bg-amber-50';
}
function statusBadge(s: string){
  const base = 'text-xs px-2 py-0.5 rounded-full';
  if (s === 'COMPLETED') return <span className={`${base} bg-green-600 text-white`}>已通过</span>;
  if (s === 'FAILED') return <span className={`${base} bg-red-600 text-white`}>已驳回/撤销</span>;
  return <span className={`${base} bg-amber-500 text-white`}>待审核</span>;
}

function last4(s: string){ return (s||'').slice(-4); }

export default function AdminOrdersPage(){
  const [items, setItems] = useState<any[]>([]);
  const [token, setToken] = useState('');
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState<'ALL'|'PENDING'|'COMPLETED'|'FAILED'>('ALL');

  async function refresh(){
    if (!token) return;
    setLoading(true);
    try{
      const r = await fetch('/api/admin/orders', { headers:{ 'x-admin-token': token }});
      const j = await r.json();
      if (j?.ok) setItems(j.items||[]);
      else alert(j?.error||'加载失败');
    } finally { setLoading(false); }
  }

  useEffect(()=>{ const saved = localStorage.getItem('adminToken')||''; if (saved) setToken(saved); },[]);
  useEffect(()=>{ refresh(); }, [token]);

  async function approve(id: string){
    const r = await fetch(`/api/orders/${id}/approve`, { method:'POST', headers:{ 'x-admin-token': token } });
    const j = await r.json();
    if (j?.ok) { alert('已通过'); refresh(); }
    else alert(j?.error||'失败');
  }
  async function reject(id: string){
    const r = await fetch(`/api/orders/${id}/reject`, { method:'POST', headers:{ 'x-admin-token': token } });
    const j = await r.json();
    if (j?.ok) { alert('已驳回'); refresh(); }
    else alert(j?.error||'失败');
  }
  async function revoke(id: string){
    const r = await fetch(`/api/orders/${id}/revoke`, { method:'POST', headers:{ 'x-admin-token': token } });
    const j = await r.json();
    if (j?.ok) { alert('已撤销并回退额度'); refresh(); }
    else alert(j?.error||'撤销失败');
  }

  const filtered = items.filter((o:any)=> filter==='ALL' ? true : o.status===filter);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">订单审核</h1>
      <div className="mb-4 flex items-center gap-2">
        <input placeholder="管理员令牌" value={token} onChange={e=>{ setToken(e.target.value); localStorage.setItem('adminToken', e.target.value); }} className="border rounded px-3 py-2 w-80" />
        <button onClick={refresh} className="px-4 py-2 rounded bg-[#666BCE] text-white">刷新</button>
      </div>

      <div className="mb-4 flex gap-2 text-sm">
        {(['ALL','PENDING','COMPLETED','FAILED'] as const).map(s=> (
          <button key={s} onClick={()=>setFilter(s)} className={`px-3 py-1 rounded-full border ${filter===s? 'bg-indigo-600 text-white border-indigo-600':'bg-white text-gray-700 border-gray-200'}`}>{s==='ALL'?'全部':(s==='PENDING'?'待审核':(s==='COMPLETED'?'已通过':'已驳回'))}</button>
        ))}
      </div>

      {loading ? <div>加载中...</div> : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {filtered.map((o:any)=>{
            const u4 = last4(o.userId||'');
            const n4 = last4(o.orderNumber||'');
            const match = !!(u4 && n4 && u4 === n4);
            return (
            <div key={o.id} className={`border rounded-xl p-4 ${statusClasses(o.status)}`}>
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">{new Date(o.createdAt).toLocaleString()}</div>
                {statusBadge(o.status)}
              </div>
              <div className="font-semibold mt-1">订单号：{o.id}</div>
              <div className="mt-1">用户：{o.userId}</div>
              <div>类型：{o.subscriptionPlan ? `订阅(${o.subscriptionPlan})` : '充值'}</div>
              <div>金额：¥ {o.amountCny} {o.payAmountCny ? <span className="text-gray-500">（建议¥ {o.payAmountCny.toFixed(2)}）</span> : null}</div>
              {o.orderCode ? <div>备注码：{o.orderCode}</div> : null}
              {o.creditsToAdd ? <div>灵感：+{o.creditsToAdd}</div> : null}
                <div className="mt-2 text-sm">
                  <div>提交订单号：<span className={match? 'text-green-700 font-semibold':'text-red-700 font-semibold'}>{o.orderNumber || '（未提交）'}</span>
                    {o.orderNumber ? <span className="text-gray-500 ml-2">（对比：用户ID后4位 {u4||'—'}）</span> : null}
                  </div>
                </div>
                {o.paymentScreenshotUrl ? (
                  <a href={o.paymentScreenshotUrl} target="_blank" className="block mt-2 w-full h-40 border rounded overflow-hidden bg-white">
                    <img src={o.paymentScreenshotUrl} alt="支付截图" className="w-full h-full object-cover" />
                  </a>
                ) : <div className="text-xs text-gray-500 mt-2">无截图</div>}
              <div className="mt-3 flex gap-2">
                <button onClick={()=>approve(o.id)} disabled={o.status!=='PENDING'} className={`px-3 py-1 rounded ${o.status==='PENDING'?'bg-green-600 hover:opacity-90':'bg-gray-300 cursor-not-allowed'} text-white`}>通过</button>
                <button onClick={()=>reject(o.id)} disabled={o.status!=='PENDING'} className={`px-3 py-1 rounded ${o.status==='PENDING'?'bg-red-600 hover:opacity-90':'bg-gray-300 cursor-not-allowed'} text-white`}>驳回</button>
                <button onClick={()=>revoke(o.id)} disabled={o.status!=='COMPLETED'} className={`px-3 py-1 rounded ${o.status==='COMPLETED'?'bg-rose-700 hover:opacity-90':'bg-gray-300 cursor-not-allowed'} text-white`}>撤销</button>
              </div>
              <div className="mt-3 border-t pt-2">
                <div className="text-xs text-gray-600 mb-1">调账（支持正负）：</div>
                <div className="flex gap-2 items-center">
                  <input id={`adj_${o.id}`} type="number" step="1" placeholder="如 -20 或 50" className="border rounded px-2 py-1 w-32" />
                  <button onClick={async()=>{
                    const el = document.getElementById(`adj_${o.id}`) as HTMLInputElement|null;
                    const val = el?.value ? Number(el.value) : 0;
                    if (!Number.isFinite(val) || val===0){ alert('请输入非零数字'); return; }
                    const r = await fetch('/api/admin/users/adjust', { method:'POST', headers:{ 'Content-Type':'application/json','x-admin-token': token }, body: JSON.stringify({ userId: o.userId, delta: val, allowNegative: true }) });
                    const j = await r.json();
                    if (j?.ok) { alert('已调账'); refresh(); }
                    else alert(j?.error||'调账失败');
                  }} className="px-3 py-1 rounded bg-indigo-600 text-white">执行</button>
                </div>
              </div>
            </div>
            );
          })}
        </div>
      )}
    </div>
  );
} 