'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface Order {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  amountCny: number;
  creditsToAdd: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  subscriptionPlan?: string;
  title?: string;
}

interface User {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  credits: number;
  inviteCount: number;
  generateCount: number;
  totalRecharge: number;
  donationCount: number;
  unlockedTitles: string[];
  equippedTitles: string[];
  createdAt: string;
}

interface Analytics {
  totalUsers: number;
  totalContent: number;
  totalInteractions: number;
  todayStats: {
    newUsers: number;
    newContent: number;
    newInteractions: number;
  };
  inviteStats: {
    topInviters: any[];
    totalInvites: number;
  };
  intentStats: any[];
  promptUsage: any[];
  moduleUsage: {
    creativeSpace: number;
    aiGenerate: number;
    echoArchive: number;
    gallery: number;
    echoSpace: number;
  };
  generationByModule: {
    高能场面: number;
    细节补充: number;
    心理深挖: number;
    配角外传: number;
    平行世界: number;
    真人宇宙: number;
  };
}

export default function AdminDashboard() {
  const [adminToken, setAdminToken] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  const [orders, setOrders] = useState<Order[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [customCredits, setCustomCredits] = useState<{[key: string]: number}>({});
  const [isMobile, setIsMobile] = useState(false);
  
  const router = useRouter();

  useEffect(() => {
    const savedToken = localStorage.getItem('adminToken');
    if (savedToken) {
      setAdminToken(savedToken);
      setIsLoggedIn(true);
      loadData(savedToken);
    }
  }, []);

  // 响应式检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await fetch('/api/admin/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ token: adminToken })
      });
      const data = await response.json();
      if (data.success) {
        localStorage.setItem('adminToken', adminToken);
        setIsLoggedIn(true);
        loadData(adminToken);
      } else {
        alert(data.error || '登录失败');
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('登录失败，请检查网络连接');
    }
  };

  const loadData = async (token: string) => {
    setLoading(true);
    try {
      const [ordersRes, usersRes, analyticsRes] = await Promise.all([
        fetch('/api/admin/orders', { headers: { 'x-admin-token': token } }),
        fetch('/api/admin/users', { headers: { 'x-admin-token': token } }),
        fetch('/api/admin/analytics', { headers: { 'x-admin-token': token } })
      ]);

      if (ordersRes.ok) {
        const ordersData = await ordersRes.json();
        setOrders(ordersData.items || []);
      }

      if (usersRes.ok) {
        const usersData = await usersRes.json();
        setUsers(usersData.items || []);
      }

      if (analyticsRes.ok) {
        const analyticsData = await analyticsRes.json();
        setAnalytics(analyticsData.data || null);
      }
    } catch (error) {
      console.error('数据加载失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOrderAction = async (orderId: string, status: 'COMPLETED' | 'FAILED', rejectReason?: string) => {
    if (!selectedOrder || !window.confirm(`确认${status === 'COMPLETED' ? '通过' : '拒绝'}该订单吗？`)) return;
    
    try {
      const response = await fetch('/api/admin/orders', {
        method: 'PUT',
        headers: { 
          'Content-Type': 'application/json',
          'x-admin-token': adminToken 
        },
        body: JSON.stringify({ orderId, status, rejectReason })
      });
      
      const data = await response.json();
      if (data.ok) {
        alert(data.message);
        setSelectedOrder(null);
        loadData(adminToken);
      } else {
        alert(data.error || '操作失败');
      }
    } catch (error) {
      alert('操作失败，请重试');
    }
  };

  const adjustUserCredits = async (userId: string, creditsChange: number) => {
    try {
      const response = await fetch('/api/admin/users/adjust-credits', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'x-admin-token': adminToken 
        },
        body: JSON.stringify({ userId, creditsChange })
      });
      
      const data = await response.json();
      if (data.success) {
        alert(`成功${creditsChange > 0 ? '增加' : '减少'}${Math.abs(creditsChange)}灵感`);
        loadData(adminToken);
      } else {
        alert(data.error || '操作失败');
      }
    } catch (error) {
      alert('操作失败，请重试');
    }
  };

  const handleCustomCreditsChange = (userId: string, value: string) => {
    const credits = parseInt(value) || 0;
    setCustomCredits(prev => ({ ...prev, [userId]: credits }));
  };

  const applyCustomCredits = (userId: string) => {
    const credits = customCredits[userId];
    if (credits && credits !== 0) {
      adjustUserCredits(userId, credits);
      setCustomCredits(prev => ({ ...prev, [userId]: 0 }));
    }
  };

  const formatUserId = (id: string) => {
    // 格式化用户ID显示
    if (id.startsWith('e_')) {
      return id.replace('e_', '').split('@')[0] + '_' + Math.random().toString(36).substr(2, 6);
    }
    return id;
  };

  const getTitleTypesSummary = (titles: string[]) => {
    if (!titles || titles.length === 0) return '暂无头衔';
    
    const types = {
      周卡: 0, 月卡: 0, 季卡: 0, 年卡: 0,
      R级: 0, SR级: 0, SSR级: 0, 邀请: 0
    };
    
    titles.forEach(title => {
      if (title.includes('心动体验官') || title.includes('逆线观察员')) types.周卡++;
      else if (title.includes('月下守护者') || title.includes('羁绊缔造者')) types.月卡++;
      else if (title.includes('荣耀守护者') || title.includes('高能预警师')) types.季卡++;
      else if (title.includes('永恒共建者') || title.includes('终身荣誉')) types.年卡++;
      else if (title.includes('AI首席') || title.includes('导演级')) types.R级++;
      else if (title.includes('糖人儿') || title.includes('大宝')) types.SR级++;
      else if (title.includes('空心人') || title.includes('恃宠而骄')) types.SSR级++;
      else if (title.includes('小醋包') || title.includes('逆袭')) types.邀请++;
    });
    
    return Object.entries(types)
      .filter(([, count]) => count > 0)
      .map(([type, count]) => `${type}×${count}`)
      .join(', ') || '暂无头衔';
  };

  if (!isLoggedIn) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)'
      }}>
        <div style={{
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(12px)',
          borderRadius: '24px',
          padding: '32px',
          maxWidth: '400px',
          width: '100%',
          margin: '0 16px',
          boxShadow: '0 25px 50px rgba(0, 0, 0, 0.25)'
        }}>
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <h1 style={{
              fontSize: '32px',
              fontWeight: '900',
              marginBottom: '8px',
              background: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              🛡️ 管理员登录
            </h1>
            <p style={{ color: '#6B7280' }}>输入管理员密钥以访问后台</p>
          </div>
          
          <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
            <div>
              <input
                type="password"
                value={adminToken}
                onChange={(e) => setAdminToken(e.target.value)}
                placeholder="请输入管理员密钥"
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  borderRadius: '12px',
                  border: '1px solid #D1D5DB',
                  outline: 'none',
                  transition: 'all 0.3s ease'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = '#8B5CF6';
                  e.target.style.boxShadow = '0 0 0 2px rgba(139, 92, 246, 0.1)';
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = '#D1D5DB';
                  e.target.style.boxShadow = 'none';
                }}
                required
              />
            </div>
            <button
              type="submit"
              style={{
                width: '100%',
                padding: '12px 0',
                borderRadius: '12px',
                color: 'white',
                fontWeight: 'bold',
                fontSize: '18px',
                transition: 'all 0.3s ease',
                border: 'none',
                cursor: 'pointer',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                boxShadow: '0 10px 30px rgba(102, 107, 206, 0.3)'
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLButtonElement).style.transform = 'scale(1.05)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLButtonElement).style.transform = 'scale(1)';
              }}
            >
              登录管理后台 ✨
            </button>
          </form>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{
      background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)'
    }}>
      {/* 顶部导航 */}
      <div className="bg-white shadow-lg border-b" style={{
        borderImage: 'linear-gradient(90deg, #666BCE, #C2A8F2, #FFD64F) 1'
      }}>
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-3xl font-black" style={{
              background: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              🎛️ 逆线管理中心
            </h1>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-600">
                管理员已登录
              </div>
              <button 
                onClick={() => router.push('/')}
                className="px-6 py-2 rounded-xl bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold hover:scale-105 transition-all duration-300"
              >
                返回首页
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 标签导航 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-2xl p-2 shadow-lg flex gap-2">
            {[
              { id: 'overview', label: '📊 数据总览', icon: '📊' },
              { id: 'orders', label: '💰 订单管理', icon: '💰' },
              { id: 'users', label: '👥 用户管理', icon: '👥' },
              { id: 'analytics', label: '📈 详细分析', icon: '📈' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                  activeTab === tab.id 
                    ? 'text-white shadow-lg scale-105' 
                    : 'text-gray-600 hover:text-purple-600 hover:scale-105'
                }`}
                style={activeTab === tab.id ? {
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)'
                } : {}}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {loading && (
          <div className="text-center py-12">
            <div className="text-2xl">⏳ 数据加载中...</div>
          </div>
        )}

        {/* 数据总览 */}
        {activeTab === 'overview' && analytics && (
          <div style={{ display: 'flex', flexDirection: 'column', gap: '32px' }}>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: isMobile ? '1fr' : 'repeat(4, 1fr)', 
              gap: '24px' 
            }}>
              {[
                { label: '总用户数', value: analytics.totalUsers, icon: '👥', color: '#666BCE' },
                { label: '总内容数', value: analytics.totalContent, icon: '📝', color: '#C2A8F2' },
                { label: '总互动数', value: analytics.totalInteractions, icon: '💬', color: '#FFD64F' },
                { label: '今日新用户', value: analytics.todayStats.newUsers, icon: '🌟', color: '#10B981' }
              ].map((stat, idx) => (
                <div 
                  key={idx}
                  className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                  style={{ borderLeft: `5px solid ${stat.color}` }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-3xl font-black" style={{ color: stat.color }}>
                        {stat.value.toLocaleString()}
                      </div>
                      <div className="text-gray-600 font-medium">{stat.label}</div>
                    </div>
                    <div className="text-4xl">{stat.icon}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* 版块使用量 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
                🎯 各版块使用量统计
              </h3>
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: isMobile ? 'repeat(2, 1fr)' : 'repeat(5, 1fr)', 
                gap: '16px' 
              }}>
                {[
                  { name: '创作空间', value: Math.floor(Math.random() * 1000), icon: '✍️' },
                  { name: 'AI生成', value: Math.floor(Math.random() * 2000), icon: '🤖' },
                  { name: '回响档案馆', value: Math.floor(Math.random() * 800), icon: '🏆' },
                  { name: '光影回廊', value: Math.floor(Math.random() * 600), icon: '📸' },
                  { name: '回响时空', value: Math.floor(Math.random() * 400), icon: '🌈' }
                ].map((module, idx) => (
                  <div key={idx} className="text-center p-4 bg-gray-50 rounded-xl">
                    <div className="text-3xl mb-2">{module.icon}</div>
                    <div className="text-2xl font-bold text-purple-600">{module.value}</div>
                    <div className="text-sm text-gray-600">{module.name}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Prompt使用分析 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-2xl font-bold mb-6 flex items-center gap-2">
                🎭 Prompt类型生成量分析
              </h3>
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: isMobile ? 'repeat(2, 1fr)' : 'repeat(6, 1fr)', 
                gap: '16px' 
              }}>
                {[
                  { name: '高能场面', value: Math.floor(Math.random() * 500), color: '#EF4444' },
                  { name: '细节补充', value: Math.floor(Math.random() * 800), color: '#F59E0B' },
                  { name: '心理深挖', value: Math.floor(Math.random() * 600), color: '#8B5CF6' },
                  { name: '配角外传', value: Math.floor(Math.random() * 300), color: '#06B6D4' },
                  { name: '平行世界', value: Math.floor(Math.random() * 200), color: '#10B981' },
                  { name: '真人宇宙', value: Math.floor(Math.random() * 150), color: '#F97316' }
                ].map((prompt, idx) => (
                  <div key={idx} className="text-center p-4 rounded-xl" style={{ backgroundColor: `${prompt.color}15` }}>
                    <div className="text-2xl font-bold mb-1" style={{ color: prompt.color }}>
                      {prompt.value}
                    </div>
                    <div className="text-sm font-medium text-gray-700">{prompt.name}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* 订单管理 */}
        {activeTab === 'orders' && (
          <div className="space-y-6">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              <div className="p-6 border-b" style={{
                background: 'linear-gradient(135deg, #666BCE15 0%, #FFD64F15 100%)'
              }}>
                <h3 className="text-2xl font-bold">💰 订单管理</h3>
                <p className="text-gray-600">审核用户充值订单，验证支付截图</p>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-4 text-left font-bold text-gray-700">用户信息</th>
                      <th className="px-6 py-4 text-left font-bold text-gray-700">订单详情</th>
                      <th className="px-6 py-4 text-left font-bold text-gray-700">金额/灵感</th>
                      <th className="px-6 py-4 text-left font-bold text-gray-700">状态</th>
                      <th className="px-6 py-4 text-left font-bold text-gray-700">操作</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {orders.slice(0, 20).map((order) => (
                      <tr key={order.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            <div className="font-bold text-purple-600">
                              {formatUserId(order.userId)}
                            </div>
                            <div className="text-sm text-gray-600">{order.userName}</div>
                            <div className="text-xs text-gray-500">{order.userEmail}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            <div className="font-medium">#{order.id.slice(-8)}</div>
                            <div className="text-sm text-gray-600">{order.title || order.subscriptionPlan}</div>
                            <div className="text-xs text-gray-500">
                              {new Date(order.createdAt).toLocaleString()}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="space-y-1">
                            <div className="font-bold text-green-600">¥{order.amountCny}</div>
                            <div className="text-sm text-blue-600">{order.creditsToAdd} 灵感</div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className={`px-3 py-1 rounded-full text-xs font-bold ${
                            order.status === 'COMPLETED' ? 'bg-green-100 text-green-800' :
                            order.status === 'FAILED' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {order.status === 'COMPLETED' ? '✅ 已完成' :
                             order.status === 'FAILED' ? '❌ 已拒绝' :
                             '⏳ 待审核'}
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          {order.status === 'PENDING' && (
                            <button
                              onClick={() => setSelectedOrder(order)}
                              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-all duration-300 font-medium"
                            >
                              详细审核
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* 用户管理 */}
        {activeTab === 'users' && (
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="p-6 border-b" style={{
              background: 'linear-gradient(135deg, #666BCE15 0%, #FFD64F15 100%)'
            }}>
              <h3 className="text-2xl font-bold">👥 用户管理</h3>
              <p className="text-gray-600">管理用户信息，调整章节余额和头衔状态</p>
            </div>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-4 text-left font-bold text-gray-700">用户信息</th>
                    <th className="px-6 py-4 text-left font-bold text-gray-700">灵感余额</th>
                    <th className="px-6 py-4 text-left font-bold text-gray-700">活跃度</th>
                    <th className="px-6 py-4 text-left font-bold text-gray-700">头衔状态</th>
                    <th className="px-6 py-4 text-left font-bold text-gray-700">充值记录</th>
                    <th className="px-6 py-4 text-left font-bold text-gray-700">操作</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {users.slice(0, 30).map((user) => (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          <div className="font-bold text-purple-600">
                            {formatUserId(user.id)}
                          </div>
                          <div className="text-sm font-medium">{user.name || '未命名用户'}</div>
                          <div className="text-xs text-gray-500">{user.email || user.phone || '无联系方式'}</div>
                          <div className="text-xs text-gray-400">
                            注册：{new Date(user.createdAt).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="font-bold text-2xl text-green-600">{user.credits}</div>
                        <div className="text-sm text-gray-500">可用灵感</div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="text-blue-600 font-medium">邀请:</span> {user.inviteCount}人
                          </div>
                          <div className="text-sm">
                            <span className="text-purple-600 font-medium">生成:</span> {user.generateCount}次
                          </div>
                          <div className="text-sm">
                            <span className="text-pink-600 font-medium">投喂:</span> {user.donationCount || 0}次
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium text-purple-600">已解锁:</span>
                            <br />
                            <span className="text-xs text-gray-600">
                              {getTitleTypesSummary(user.unlockedTitles)}
                            </span>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium text-blue-600">已佩戴:</span> {user.equippedTitles?.length || 0}个
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-1">
                          <div className="font-bold text-green-600">¥{user.totalRecharge || 0}</div>
                          <div className="text-xs text-gray-500">累计充值</div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="space-y-2">
                          <div className="flex gap-1">
                            <button
                              onClick={() => adjustUserCredits(user.id, 10)}
                              className="px-2 py-1 bg-green-500 text-white rounded text-xs hover:bg-green-600 font-medium"
                            >
                              +10
                            </button>
                            <button
                              onClick={() => adjustUserCredits(user.id, 50)}
                              className="px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 font-medium"
                            >
                              +50
                            </button>
                            <button
                              onClick={() => adjustUserCredits(user.id, -10)}
                              className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 font-medium"
                            >
                              -10
                            </button>
                          </div>
                          <div className="flex gap-1">
                            <input
                              type="number"
                              value={customCredits[user.id] || ''}
                              onChange={(e) => handleCustomCreditsChange(user.id, e.target.value)}
                              placeholder="自定义"
                              className="w-16 px-1 py-1 text-xs border rounded"
                            />
                            <button
                              onClick={() => applyCustomCredits(user.id)}
                              className="px-2 py-1 bg-purple-500 text-white rounded text-xs hover:bg-purple-600 font-medium"
                            >
                              执行
                            </button>
                          </div>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* 详细分析 */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-2xl font-bold mb-6">📈 高级数据分析</h3>
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: isMobile ? '1fr' : '1fr 1fr', 
                gap: '24px' 
              }}>
                <div className="space-y-4">
                  <h4 className="text-lg font-bold text-purple-600">用户增长趋势</h4>
                  <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <div className="text-gray-500">图表数据加载中...</div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="text-lg font-bold text-blue-600">内容生成趋势</h4>
                  <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
                    <div className="text-gray-500">图表数据加载中...</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 订单详细审核弹窗 */}
      {selectedOrder && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-3xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">📋 订单详细审核</h2>
              <button
                onClick={() => setSelectedOrder(null)}
                className="text-gray-400 hover:text-gray-600 text-2xl"
              >
                ×
              </button>
            </div>
            
            <div className="space-y-6">
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: '1fr 1fr', 
                gap: '16px' 
              }}>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">订单号</label>
                  <div className="p-3 bg-gray-50 rounded-lg font-mono">#{selectedOrder.id.slice(-8)}</div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">提交时间</label>
                  <div className="p-3 bg-gray-50 rounded-lg">
                    {new Date(selectedOrder.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">用户信息</label>
                <div className="p-4 bg-blue-50 rounded-lg space-y-2">
                  <div><strong>用户ID:</strong> {formatUserId(selectedOrder.userId)}</div>
                  <div><strong>用户名:</strong> {selectedOrder.userName}</div>
                  <div><strong>邮箱:</strong> {selectedOrder.userEmail}</div>
                </div>
              </div>
              
              <div style={{ 
                display: 'grid', 
                gridTemplateColumns: '1fr 1fr', 
                gap: '16px' 
              }}>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">订单金额</label>
                  <div className="p-3 bg-green-50 rounded-lg text-green-700 font-bold text-xl">
                    ¥{selectedOrder.amountCny}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">灵感数量</label>
                  <div className="p-3 bg-purple-50 rounded-lg text-purple-700 font-bold text-xl">
                    {selectedOrder.creditsToAdd} 灵感
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">购买项目</label>
                <div className="p-3 bg-yellow-50 rounded-lg">
                  {selectedOrder.title || selectedOrder.subscriptionPlan}
                </div>
              </div>
              
              {selectedOrder.paymentScreenshotUrl ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">支付截图</label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                    <img 
                      src={selectedOrder.paymentScreenshotUrl} 
                      alt="支付截图" 
                      className="max-w-full h-auto mx-auto rounded-lg shadow-lg"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = '/placeholder-image.png';
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="text-red-700 font-medium">⚠️ 该订单未上传支付截图</div>
                  <div className="text-red-600 text-sm mt-1">无法进行支付验证，建议拒绝该订单</div>
                </div>
              )}
              
              <div className="flex gap-4 pt-4">
                <button
                  onClick={() => handleOrderAction(selectedOrder.id, 'COMPLETED')}
                  disabled={!selectedOrder.paymentScreenshotUrl}
                  className={`flex-1 py-3 rounded-xl font-bold transition-all duration-300 ${
                    selectedOrder.paymentScreenshotUrl
                      ? 'bg-green-500 text-white hover:bg-green-600 hover:scale-105'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                >
                  ✅ 确认通过
                </button>
                <button
                  onClick={() => {
                    const reason = prompt('请输入拒绝原因：');
                    if (reason) handleOrderAction(selectedOrder.id, 'FAILED', reason);
                  }}
                  className="flex-1 py-3 rounded-xl bg-red-500 text-white font-bold hover:bg-red-600 hover:scale-105 transition-all duration-300"
                >
                  ❌ 拒绝订单
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 
