'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function TestSystemPage() {
  const router = useRouter();
  const [user, setUser] = useState<any>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const log = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testSignin = async () => {
    if (!user?.id) {
      log('❌ 签到测试失败：用户未登录');
      return;
    }

    try {
      const response = await fetch('/api/auth/daily-signin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: user.id })
      });
      const data = await response.json();
      
      if (data.ok) {
        if (data.alreadySigned) {
          log('✅ 签到测试：今日已签到');
        } else if (data.fragmentReward) {
          log(`✅ 签到测试成功：${data.message}`);
          log(`   - 新的碎片余额：${data.newFragmentBalance}`);
          log(`   - 是否兑换章节：${data.chapterAwarded ? '是' : '否'}`);
        }
      } else {
        log(`❌ 签到测试失败：${data.error}`);
      }
    } catch (error) {
      log(`❌ 签到测试异常：${error}`);
    }
  };

  const testOrderCreation = async (price: number, credits: number, title: string) => {
    if (!user?.id) {
      log('❌ 订单测试失败：用户未登录');
      return;
    }

    try {
      const response = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amountCny: price,
          creditsToAdd: credits,
          userId: user.id
        })
      });
      const data = await response.json();
      
      if (data.ok) {
        log(`✅ 订单创建成功：${title}`);
        log(`   - 订单ID：${data.item.id}`);
        log(`   - 金额：¥${data.item.amountCny}`);
        log(`   - 章节数：${data.item.creditsToAdd}`);
      } else {
        log(`❌ 订单创建失败：${data.error}`);
        log(`   - 套餐：${title} (¥${price} -> ${credits}章节)`);
      }
    } catch (error) {
      log(`❌ 订单测试异常：${error}`);
    }
  };

  const testContentCreation = async () => {
    if (!user?.id) {
      log('❌ 内容创建测试失败：用户未登录');
      return;
    }

    try {
      const response = await fetch('/api/content', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: user.id,
          title: '测试作品：高能场面',
          content: '这是一个测试的高能场面内容，用于验证创作空间功能是否正常工作。内容包含了丰富的情节描述和角色对话。',
          intent: 'action',
          universe: 'novel',
          isPublic: true
        })
      });
      const data = await response.json();
      
      if (data.ok) {
        log('✅ 内容创建成功');
        log(`   - 内容ID：${data.item.id}`);
        log(`   - 标题：${data.item.title}`);
        log(`   - 摘要：${data.item.summary}`);
        log(`   - 标签：${data.item.tags.join(', ')}`);
      } else {
        log(`❌ 内容创建失败：${data.error}`);
      }
    } catch (error) {
      log(`❌ 内容创建异常：${error}`);
    }
  };

  const testUserTitles = async () => {
    if (!user?.id) {
      log('❌ 头衔测试失败：用户未登录');
      return;
    }

    try {
      const response = await fetch(`/api/user/titles?userId=${user.id}`);
      const data = await response.json();
      
      if (data.ok) {
        log('✅ 头衔系统测试成功');
        log(`   - 已解锁头衔：${data.unlockedTitles.length}个`);
        log(`   - 已佩戴头衔：${data.equippedTitles.length}个`);
        log(`   - 全部头衔：${data.allTitles.length}个`);
      } else {
        log(`❌ 头衔测试失败：${data.error}`);
      }
    } catch (error) {
      log(`❌ 头衔测试异常：${error}`);
    }
  };

  const testAllSystems = async () => {
    log('🚀 开始全系统测试...');
    
    // 测试签到系统
    await testSignin();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 测试订单系统
    await testOrderCreation(8, 7, '灵感加油包');
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 测试内容系统
    await testContentCreation();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 测试头衔系统
    await testUserTitles();
    
    log('✨ 全系统测试完成！');
  };

  const clearLogs = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-3xl font-bold text-gray-900">🔧 逆线系统测试中心</h1>
            <button
              onClick={() => router.push('/')}
              className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
            >
              返回首页
            </button>
          </div>

          {user ? (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <h3 className="font-bold text-green-800 mb-2">当前用户信息</h3>
              <div className="text-sm text-green-700">
                <p>用户名：{user.name}</p>
                <p>用户ID：{user.id}</p>
                <p>可用章节：{user.credits || 0}</p>
                <p>灵感碎片：{user.fragmentBalance || 0}</p>
                <p>邀请码：{user.inviteCode || '无'}</p>
              </div>
            </div>
          ) : (
            <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p className="text-yellow-800">请先登录以进行完整测试</p>
              <button
                onClick={() => router.push('/auth')}
                className="mt-2 px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
              >
                去登录
              </button>
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <button
              onClick={testSignin}
              className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              📅 测试签到系统
            </button>
            
            <button
              onClick={() => testOrderCreation(8, 7, '灵感加油包')}
              className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              💰 测试订单系统
            </button>
            
            <button
              onClick={testContentCreation}
              className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              📝 测试内容系统
            </button>
            
            <button
              onClick={testUserTitles}
              className="p-4 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors"
            >
              🏅 测试头衔系统
            </button>
          </div>

          <div className="flex gap-4 mb-6">
            <button
              onClick={testAllSystems}
              className="flex-1 p-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all font-semibold"
            >
              🚀 运行全系统测试
            </button>
            
            <button
              onClick={clearLogs}
              className="px-6 py-3 bg-gray-400 text-white rounded-lg hover:bg-gray-500 transition-colors"
            >
              清空日志
            </button>
          </div>

          <div className="bg-gray-900 text-green-400 rounded-lg p-4 h-96 overflow-y-auto font-mono text-sm">
            <div className="mb-2 text-white">测试日志:</div>
            {testResults.length === 0 ? (
              <div className="text-gray-500">等待测试开始...</div>
            ) : (
              testResults.map((result, index) => (
                <div key={index} className="mb-1">
                  {result}
                </div>
              ))
            )}
          </div>

          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-bold text-blue-800 mb-2">测试说明</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 签到系统：测试每日签到功能，验证灵感碎片累积和章节兑换</li>
              <li>• 订单系统：测试充值订单创建，验证价格与章节数映射</li>
              <li>• 内容系统：测试创作空间内容发布功能</li>
              <li>• 头衔系统：测试用户头衔获取和管理</li>
              <li>• 全系统测试：按顺序执行所有单项测试</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
} 