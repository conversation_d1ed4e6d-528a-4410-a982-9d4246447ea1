'use client';

import React from 'react';
import dynamic from 'next/dynamic';
import { useRouter, usePathname } from 'next/navigation';

// 动态导入所有组件，避免hydration问题
const CreationIsland = dynamic(() => import('@/components/CreationIsland'), { ssr: false });
const TopNavigation = dynamic(() => import('@/components/TopNavigation'), { ssr: false });
const FlightHint = dynamic(() => import('@/components/FlightHint'), { ssr: false });
const ScreenshotButton = dynamic(() => import('@/components/ScreenshotButton'), { ssr: false });
const ScreenshotWatermark = dynamic(() => import('@/components/ScreenshotWatermark'), { ssr: false });

const PARTICLE_COUNT = 20000;

export default function Home() {
  const [is3DMode, setIs3DMode] = React.useState(false); // 默认显示网站首页，3D宇宙为可选功能
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div 
      id="screenshot-root" 
      style={{
        position: 'relative',
        width: '100%',
        height: '100vh',
        overflow: 'hidden'
      }}
    >
      {/* 背景层 */}
      {is3DMode ? (
        // 3D宇宙模式：显示3D iframe，覆盖一切
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          zIndex: 0
        }}>
          <iframe
            src="/particle-3d.html"
            style={{
              width: '100%',
              height: '100%',
              border: 'none',
              position: 'absolute',
              top: 0,
              left: 0
            }}
            title="3D粒子宇宙 - 交互模式"
          />
        </div>
      ) : null}
      
      {/* 网站首页模式：CSS动画背景由GlobalParticleBackground提供，不需要iframe */}
      
      {/* 3D模式切换按钮 - 紧贴Navigation Controls上方 */}
      <button
        onClick={() => {
          // 简单切换：3D模式和CSS动画首页之间切换
          setIs3DMode(!is3DMode);
        }}
        style={{
          position: 'fixed',
          bottom: '320px', // Navigation Controls高度约249px + 20px间距 + 30px安全距离
          left: '20px', // 与Navigation Controls左对齐
          zIndex: 40,
          width: '260px', // 与Navigation Controls宽度一致
          padding: '12px',
          borderRadius: '12px',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(16px)',
          backgroundColor: 'rgba(0, 0, 0, 0.35)',
          color: 'rgba(255, 255, 255, 0.9)',
          fontSize: '14px',
          fontWeight: '600',
          cursor: 'pointer',
          transition: 'all 0.3s ease',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.35)',
          userSelect: 'none'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.45)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
          e.currentTarget.style.transform = 'translateY(0px)';
        }}
      >
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          gap: '8px'
        }}>
          {is3DMode ? (
            <>
              <span>🏠</span>
              <span>回到首页</span>
            </>
          ) : (
            <>
              <span>🌌</span>
              <span>激活3D宇宙</span>
            </>
          )}
        </div>
      </button>

      {/* 完整的网站UI */}
      <div style={{
        position: 'relative',
        zIndex: 10,
        transition: 'all 0.5s ease',
        opacity: is3DMode ? 0.1 : 1,  // 网站首页模式时完全显示，3D模式时最小化
        pointerEvents: is3DMode ? 'none' : 'auto'  // 网站首页模式时允许交互，3D模式时禁用
      }}
      onMouseEnter={() => is3DMode && (document.querySelector('[data-ui-layer]') as HTMLElement).style.opacity = '0.8'}
      onMouseLeave={() => is3DMode && (document.querySelector('[data-ui-layer]') as HTMLElement).style.opacity = '0.1'}
      data-ui-layer
      >
        <TopNavigation />
        <CreationIsland />
        <FlightHint particleCount={PARTICLE_COUNT} />
        <ScreenshotButton />
        <ScreenshotWatermark />
      </div>


    </div>
  );
}