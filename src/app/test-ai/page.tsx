'use client';

import { useState } from 'react';

export default function TestAIPage() {
  const [results, setResults] = useState<any[]>([]);
  const [testing, setTesting] = useState(false);

  const runTest = async () => {
    setTesting(true);
    setResults([]);

    const testCases = [
      {
        workTitle: '谢怜与花城',
        intent: 'au',
        userPrompt: '在现代校园相遇会怎样',
        char1: '谢怜',
        char2: '花城'
      },
      {
        workTitle: '谢怜与花城',
        intent: 'au',
        userPrompt: '如果他们是邻居会发生什么',
        char1: '谢怜',
        char2: '花城'
      },
      {
        workTitle: '谢怜与花城',
        intent: 'sequel',
        userPrompt: '谢怜第一次说我爱你',
        char1: '谢怜',
        char2: '花城'
      }
    ];

    try {
      const response = await fetch('/api/test-ai', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          testInputs: testCases
        }),
      });

      const data = await response.json();
      setResults(data.results || []);

    } catch (error) {
      console.error('测试失败:', error);
      setResults([{ error: '测试请求失败' }]);
    } finally {
      setTesting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">AI生成测试</h1>
        
        <button
          onClick={runTest}
          disabled={testing}
          className="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 disabled:opacity-50 mb-8"
        >
          {testing ? '测试中...' : '开始测试'}
        </button>

        {results.length > 0 && (
          <div className="space-y-8">
            <h2 className="text-2xl font-bold">测试结果</h2>
            {results.map((result, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-blue-600">
                    测试 #{index + 1}
                  </h3>
                  <p className="text-gray-600">
                    输入: <span className="italic">"{result.input}"</span>
                  </p>
                  {result.success && (
                    <p className="text-sm text-gray-500">
                      长度: {result.length}字符 | 耗时: {result.duration}ms
                    </p>
                  )}
                </div>
                
                {result.success ? (
                  <div className="border-l-4 border-green-500 pl-4">
                    <h4 className="font-medium text-green-700 mb-2">✅ 生成结果:</h4>
                    <div className="bg-gray-50 p-4 rounded whitespace-pre-line">
                      {result.output}
                    </div>
                  </div>
                ) : (
                  <div className="border-l-4 border-red-500 pl-4">
                    <h4 className="font-medium text-red-700 mb-2">❌ 生成失败:</h4>
                    <p className="text-red-600">{result.error}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}