'use client';

import { useRouter } from 'next/navigation';
import { ArrowLeft, BookOpen, Sparkles, Heart, Film, Zap } from 'lucide-react';

export default function NiAiPage() {
  const router = useRouter();

  const templates = [
    {
      title: '续写·夜半重逢',
      desc: '克制对话+动作细节，误会将解未解的张力',
      intent: 'sequel',
      prefill: '深夜重逢，一方不愿开灯，先从动作与停顿写起'
    },
    {
      title: 'AU·同居日常',
      desc: '生活流片段，少形容词，多动作眼神的情绪递进',
      intent: 'au',
      prefill: '现代同居的一个清晨，从做早餐到出门的默契与拉扯'
    },
    {
      title: '风格演绎·克制告白',
      desc: '对话推进为主，内心戏点到即止，留白收束',
      intent: 'style',
      prefill: '在停车场的短暂对话里完成一次含蓄的“告白”'
    },
    {
      title: '补完·未发出的短信',
      desc: '用短信片段嵌入回忆，形成情绪反差',
      intent: 'detail',
      prefill: '未发出的短信作为线索，补完一次争吵后的心理拉扯'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 to-orange-50">
      <nav className="border-b border-white/20 bg-white/80 backdrop-blur-md sticky top-0 z-50">
        <div className="max-w-6xl mx-auto px-4 py-4 flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <button 
              onClick={() => router.back()}
              className="flex items-center space-x-2 text-gray-600 hover:text-rose-600 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
              <span>返回</span>
            </button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-rose-500 to-orange-500 rounded-full flex items-center justify-center">
                <BookOpen className="w-4 h-4 text-white" />
              </div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-rose-600 to-orange-600 bg-clip-text text-transparent">
                逆爱 · 专题
              </h1>
            </div>
          </div>
        </div>
      </nav>

      <main className="max-w-6xl mx-auto px-4 py-10">
        {/* Hero */}
        <div className="rounded-3xl p-10 bg-gradient-to-r from-rose-100 to-orange-100 border shadow-sm mb-10">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
            <div>
              <div className="inline-flex items-center space-x-2 text-rose-600 mb-3">
                <Film className="w-5 h-5" />
                <span>热门·都市情感</span>
              </div>
              <h2 className="text-3xl md:text-4xl font-extrabold text-gray-800 mb-3">用克制写张力，用细节承情绪</h2>
              <p className="text-gray-600 mb-6">本站已内置《逆爱》世界设定与风格要点，确保不OOC与风格贴合。选择模板，一键创作。</p>
              <div className="flex gap-3">
                <button
                  onClick={() => router.push(`/intent?work=${encodeURIComponent('逆爱')}&default_intent=style&prefill=${encodeURIComponent('写一段在城市夜色里的对话场景，动作与停顿推动情绪')}`)}
                  className="bg-gradient-to-r from-rose-500 to-orange-500 text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all flex items-center space-x-2"
                >
                  <Zap className="w-4 h-4" />
                  <span>一键开始</span>
                </button>
                <button
                  onClick={() => router.push('/')} 
                  className="text-gray-600 hover:text-rose-600 transition-colors"
                >
                  返回首页
                </button>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4 w-full md:w-auto">
              <div className="bg-white rounded-2xl p-4 shadow">
                <div className="text-sm text-gray-500 mb-1">人物要点</div>
                <div className="text-gray-800">• 主角A：克制隐忍，外冷内热<br/>• 主角B：锋利外壳，内心柔软</div>
              </div>
              <div className="bg-white rounded-2xl p-4 shadow">
                <div className="text-sm text-gray-500 mb-1">风格要点</div>
                <div className="text-gray-800">对话密、句子短、留白多、用细节承载情绪</div>
              </div>
              <div className="bg-white rounded-2xl p-4 shadow">
                <div className="text-sm text-gray-500 mb-1">不OOC</div>
                <div className="text-gray-800">不降智、不突兀转性、尊重人物底线与成长</div>
              </div>
              <div className="bg-white rounded-2xl p-4 shadow">
                <div className="text-sm text-gray-500 mb-1">结局倾向</div>
                <div className="text-gray-800">HE/开放式，避免强行狗血</div>
              </div>
            </div>
          </div>
        </div>

        {/* 模板 */}
        <h3 className="text-xl font-bold mb-4 text-gray-800">创作模板</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {templates.map((t) => (
            <div key={t.title} className="bg-white rounded-2xl p-5 shadow hover:shadow-md transition">
              <div className="flex items-center justify-between mb-2">
                <div className="font-semibold text-gray-800">{t.title}</div>
                <Sparkles className="w-4 h-4 text-rose-500" />
              </div>
              <div className="text-gray-600 text-sm mb-4">{t.desc}</div>
              <button
                onClick={() => router.push(`/intent?work=${encodeURIComponent('逆爱')}&default_intent=${t.intent}&prefill=${encodeURIComponent(t.prefill)}`)}
                className="w-full text-sm bg-rose-50 text-rose-600 px-4 py-2 rounded-lg hover:bg-rose-100 transition"
              >
                使用模板 →
              </button>
            </div>
          ))}
        </div>

        {/* 说明 */}
        <div className="mt-10 text-gray-500 text-sm">
          注：页面素材不含受版权保护的长文本内容，仅使用公开信息进行风格抽象与要点提示。
        </div>
      </main>
    </div>
  );
} 