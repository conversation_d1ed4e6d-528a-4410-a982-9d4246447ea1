'use client';

import React from 'react';
import { Canvas } from '@react-three/fiber';

function TestCube() {
  return (
    <mesh>
      <boxGeometry args={[1, 1, 1]} />
      <meshBasicMaterial color="hotpink" />
    </mesh>
  );
}

export default function TestThreePage() {
  return (
    <div style={{ width: '100%', height: '100vh', backgroundColor: '#000' }}>
      <h1 style={{ color: 'white', position: 'absolute', zIndex: 10, padding: '20px' }}>
        Three.js 测试页面
      </h1>
      <Canvas>
        <TestCube />
      </Canvas>
    </div>
  );
}