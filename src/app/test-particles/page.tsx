'use client';

import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { BufferGeometry, BufferAttribute, Color } from 'three';
import * as THREE from 'three';

function SimpleParticles() {
  const pointsRef = useRef<THREE.Points>(null);
  
  const geometry = React.useMemo(() => {
    const geom = new BufferGeometry();
    const count = 1000; // 只用1000个粒子进行测试
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    
    // 使用你的CP应援色
    const phantomPurple = new Color('#666BCE');
    const amethyst = new Color('#C2A8F2');
    const lemonYellow = new Color('#FFD64F');
    
    for (let i = 0; i < count; i++) {
      // 简单的随机分布
      positions[i * 3] = (Math.random() - 0.5) * 100;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 100;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 100;
      
      // 简单的颜色分配
      let color: Color;
      const rand = Math.random();
      if (rand > 0.6) {
        color = lemonYellow;
      } else if (rand > 0.3) {
        color = amethyst;
      } else {
        color = phantomPurple;
      }
      
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
    }
    
    geom.setAttribute('position', new BufferAttribute(positions, 3));
    geom.setAttribute('color', new BufferAttribute(colors, 3));
    
    return geom;
  }, []);
  
  useFrame((state, delta) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.y += delta * 0.1;
    }
  });
  
  return (
    <points ref={pointsRef} geometry={geometry}>
      <pointsMaterial 
        size={5}
        vertexColors={true}
        transparent={true}
        opacity={0.8}
      />
    </points>
  );
}

export default function TestParticlesPage() {
  return (
    <div style={{ width: '100%', height: '100vh', backgroundColor: '#0a0a1a' }}>
      <div style={{ 
        color: 'white', 
        position: 'absolute', 
        zIndex: 10, 
        padding: '20px',
        fontSize: '18px',
        background: 'rgba(0,0,0,0.7)'
      }}>
        <h1>粒子系统测试</h1>
        <p>如果你能看到彩色粒子在旋转，说明Three.js工作正常</p>
        <p>颜色：紫色(#666BCE) + 紫晶色(#C2A8F2) + 柠檬黄(#FFD64F)</p>
      </div>
      <Canvas camera={{ position: [0, 0, 50] }}>
        <SimpleParticles />
      </Canvas>
    </div>
  );
}