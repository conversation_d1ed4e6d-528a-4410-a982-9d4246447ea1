'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import UnifiedLayout from '@/components/UnifiedLayout';
import CSSParticleUniverse from '@/components/CSSParticleUniverse';
import { 
  UnifiedButton, 
  UnifiedCard, 
  UnifiedLoading,
  cpColors 
} from '@/components/ui/UnifiedComponents';

type GalleryItem = {
  url: string;
  name?: string;
  likes?: number;
  likedBy?: string[];
};

export default function Gallery() {
  const router = useRouter();
  const [items, setItems] = useState<GalleryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);
  const [activeCategory, setActiveCategory] = useState('cp_main');
  
  const categories = [
    { id: 'cp_main', name: '🔥 池畏CP', description: '经典CP时刻' },
    { id: 'cp_sub', name: '💕 展丞CP', description: '甜蜜互动瞬间' },
    { id: 'group', name: '👥 群像合照', description: '所有角色同框' },
    { id: 'Portraits_cc', name: '🎭 池骋单人', description: '池骋个人写真' },
    { id: 'Portraits_wsw', name: '⭐ 吴所畏单人', description: '吴所畏个人写真' },
    { id: 'Portraits_gcy', name: '💫 郭城宇单人', description: '郭城宇个人写真' },
    { id: 'Portraits_jxs', name: '🌟 姜小帅单人', description: '姜小帅个人写真' }
  ];

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
    loadGallery();
  }, []);

  const loadGallery = async (category = activeCategory) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/gallery?category=${category}`);
      if (response.ok) {
        const data = await response.json();
        setItems(data.items || []);
      } else {
        console.error('Failed to load gallery:', response.status);
      }
    } catch (error) {
      console.error('Failed to load gallery:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    setActiveCategory(categoryId);
    loadGallery(categoryId);
  };

  const handleLike = async (itemUrl: string) => {
    if (!user) {
      alert('请先登录');
      return;
    }

    try {
      const response = await fetch('/api/gallery/like', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ url: itemUrl, userId: user.id })
      });

      if (response.ok) {
        const result = await response.json();
        setItems(prevItems =>
          prevItems.map(item =>
            item.url === itemUrl
              ? { ...item, likes: result.likes, likedBy: result.likedBy }
              : item
          )
        );
      } else {
        console.error('Failed to like image:', response.status);
      }
    } catch (error) {
      console.error('Failed to like image:', error);
    }
  };

  return (
    <div style={{
      position: 'relative',
      minHeight: '100vh'
    }}>
      <UnifiedLayout 
        title="🌟 光影回廊" 
        backgroundIntensity="subtle"
      >
      {/* 统计信息 */}
      <div style={{ 
        textAlign: 'center', 
        marginBottom: '40px',
        color: 'rgba(255, 255, 255, 0.8)'
      }}>
        共收录 {items.length} 张精彩作品
      </div>

      {/* 类别选择器 */}
      <UnifiedCard title="作品分类" style={{ marginBottom: '30px' }}>
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
            gap: '12px'
          }}
        >
          {categories.map((category) => (
            <UnifiedButton
              key={category.id}
              variant={activeCategory === category.id ? 'primary' : 'secondary'}
              onClick={() => handleCategoryChange(category.id)}
              style={{
                flexDirection: 'column',
                padding: '12px',
                height: 'auto'
              }}
            >
              <div style={{ fontSize: '14px', fontWeight: 'bold' }}>
                {category.name}
              </div>
              <div style={{ 
                fontSize: '11px', 
                opacity: 0.8,
                marginTop: '4px'
              }}>
                {category.description}
              </div>
            </UnifiedButton>
          ))}
        </div>
      </UnifiedCard>

      {/* 图片展示区域 */}
      {loading ? (
        <UnifiedCard>
          <div style={{ textAlign: 'center', padding: '60px' }}>
            <UnifiedLoading size={60} />
            <div style={{ 
              marginTop: '20px', 
              color: 'rgba(255, 255, 255, 0.7)',
              fontSize: '16px'
            }}>
              正在加载精彩作品...
            </div>
          </div>
        </UnifiedCard>
      ) : items.length === 0 ? (
        <UnifiedCard style={{ textAlign: 'center', padding: '80px' }}>
          <div style={{ fontSize: '64px', marginBottom: '24px' }}>🎨</div>
          <div style={{ 
            fontSize: '24px', 
            marginBottom: '12px',
            color: 'rgba(255, 255, 255, 0.9)',
            fontWeight: 'bold'
          }}>
            画廊空空如也
          </div>
          <div style={{ 
            fontSize: '16px', 
            color: 'rgba(255, 255, 255, 0.6)',
            marginBottom: '32px'
          }}>
            快去创作空间生成你的第一幅作品吧！
          </div>
          <UnifiedButton 
            variant="primary"
            size="large"
            onClick={() => router.push('/creative-space')}
          >
            开始创作 ✨
          </UnifiedButton>
        </UnifiedCard>
      ) : (
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(280px, 1fr))',
            gap: '24px'
          }}
        >
          {items.map((item, idx) => {
            const isLiked = user && item.likedBy?.includes(user.id);
            return (
              <UnifiedCard
                key={idx}
                hoverable
                style={{
                  padding: '0',
                  overflow: 'hidden',
                  position: 'relative'
                }}
              >
                <div style={{ position: 'relative', aspectRatio: '1' }}>
                  <img
                    src={item.url}
                    alt={item.name || '图片'}
                    loading={idx < 10 ? 'eager' : 'lazy'}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      backgroundColor: '#1a1a1a'
                    }}
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = 'data:image/svg+xml;base64,' + btoa(`
                        <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
                          <rect width="100%" height="100%" fill="#333"/>
                          <text x="50%" y="50%" font-size="18" fill="white" text-anchor="middle" dy=".3em">Image Error</text>
                        </svg>
                      `);
                    }}
                  />
                  
                  {/* 悬浮信息层 */}
                  <div
                    style={{
                      position: 'absolute',
                      bottom: '0',
                      left: '0',
                      right: '0',
                      background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.8))',
                      padding: '20px 16px 16px',
                      transform: 'translateY(100%)',
                      transition: 'transform 0.3s ease',
                      color: 'white'
                    }}
                    className="gallery-item-info"
                  >
                    {item.name && (
                      <div style={{ 
                        fontSize: '16px', 
                        fontWeight: 'bold', 
                        marginBottom: '8px',
                        color: 'rgba(255, 255, 255, 0.95)'
                      }}>
                        {item.name}
                      </div>
                    )}
                    
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between' 
                    }}>
                      <span style={{ 
                        fontSize: '12px', 
                        opacity: 0.7,
                        color: 'rgba(255, 255, 255, 0.7)'
                      }}>
                        {new Date().toLocaleDateString()}
                      </span>
                      
                      <UnifiedButton
                        size="small"
                        variant={isLiked ? 'primary' : 'secondary'}
                        onClick={() => handleLike(item.url)}
                        style={{ 
                          padding: '6px 12px',
                          fontSize: '12px'
                        }}
                      >
                        {isLiked ? '❤️' : '🤍'} {item.likes || 0}
                      </UnifiedButton>
                    </div>
                  </div>
                </div>
              </UnifiedCard>
            );
          })}
        </div>
      )}

      {/* 底部装饰 */}
      <div style={{ 
        textAlign: 'center', 
        padding: '60px 20px',
        marginTop: '40px'
      }}>
        <div style={{
          color: 'rgba(255, 255, 255, 0.5)',
          fontSize: '14px',
          marginBottom: '20px'
        }}>
          每一幅作品都是独特的艺术创作 ✨
        </div>
        
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: '8px'
        }}>
          {[...Array(5)].map((_, i) => (
            <div 
              key={i}
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                background: cpColors.gradient,
                animation: `pulse 2s ease-in-out infinite`,
                animationDelay: `${i * 0.3}s`
              }}
            />
          ))}
        </div>
      </div>

      {/* CSS动画样式 */}
      <style jsx global>{`
        .gallery-item-card:hover .gallery-item-info {
          transform: translateY(0) !important;
        }
        
        @keyframes pulse {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.3; }
        }
      `}</style>
      </UnifiedLayout>
    </div>
  );
}