'use client';

import React, { useState, useEffect } from 'react';

export default function DebugPage() {
  const [webglSupport, setWebglSupport] = useState<string>('检测中...');
  const [canvasTest, setCanvasTest] = useState<string>('检测中...');
  const [userAgent, setUserAgent] = useState<string>('检测中...');
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // 标记客户端已加载，避免hydration错误
    setIsClient(true);
    
    // 设置用户代理
    setUserAgent(navigator.userAgent);

    // 检测WebGL支持
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      if (gl && gl instanceof WebGLRenderingContext) {
        const renderer = gl.getParameter(gl.RENDERER);
        const version = gl.getParameter(gl.VERSION);
        setWebglSupport(`✅ 支持 - 渲染器: ${renderer}, 版本: ${version}`);
      } else {
        setWebglSupport('❌ 不支持 WebGL');
      }
    } catch (e) {
      setWebglSupport('❌ WebGL 检测出错: ' + String(e));
    }

    // 测试基本Canvas
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      if (ctx) {
        setCanvasTest('✅ Canvas 2D 支持');
      } else {
        setCanvasTest('❌ Canvas 2D 不支持');
      }
    } catch (e) {
      setCanvasTest('❌ Canvas 2D 出错: ' + String(e));
    }
  }, []);

  // 在客户端加载完成前显示加载状态
  if (!isClient) {
    return (
      <div style={{ 
        padding: '20px', 
        fontFamily: 'monospace', 
        backgroundColor: '#0a0a1a', 
        color: 'white', 
        minHeight: '100vh' 
      }}>
        <h1>🔍 系统诊断页面</h1>
        <p>正在加载检测工具...</p>
      </div>
    );
  }

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#0a0a1a', 
      color: 'white', 
      minHeight: '100vh' 
    }}>
      <h1>🔍 系统诊断页面</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <h2>WebGL 支持检测：</h2>
        <p>{webglSupport}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>Canvas 支持检测：</h2>
        <p>{canvasTest}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>用户代理：</h2>
        <p style={{ wordBreak: 'break-all', fontSize: '12px' }}>{userAgent}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>简单Canvas测试：</h2>
        <canvas 
          width="400" 
          height="200" 
          style={{ border: '1px solid white', backgroundColor: '#333' }}
          ref={(canvas) => {
            if (canvas && isClient) {
              const ctx = canvas.getContext('2d');
              if (ctx) {
                // 绘制一些简单的图形
                ctx.fillStyle = '#666BCE';
                ctx.fillRect(50, 50, 100, 100);
                
                ctx.fillStyle = '#C2A8F2';
                ctx.beginPath();
                ctx.arc(250, 100, 50, 0, 2 * Math.PI);
                ctx.fill();
                
                ctx.fillStyle = '#FFD64F';
                ctx.fillText('Canvas 正常工作！', 100, 30);
              }
            }
          }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h2>🎯 下一步测试：</h2>
        <p>1. ✅ Hydration问题已修复</p>
        <p>2. 如果你能看到上面的彩色图形，说明Canvas工作正常</p>
        <p>3. 如果WebGL支持显示❌，那就是Three.js无法工作的原因</p>
        <p>4. 如果都正常，问题可能在React Three Fiber配置</p>
      </div>
    </div>
  );
}