'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import UnifiedLayout from '@/components/UnifiedLayout';
import { 
  UnifiedButton, 
  UnifiedCard, 
  UnifiedTextarea,
  UnifiedBadge,
  cpColors 
} from '@/components/ui/UnifiedComponents';

const SECTIONS = [
  { id: 'action', title: '⚡ 高能场面', subtitle: '心跳风暴', description: '爆燃动作与激情时刻' },
  { id: 'detail', title: '🔍 细节补完', subtitle: '时光回廊', description: '深度解读与细节探索' },
  { id: 'psych', title: '🧠 心理深挖', subtitle: '灵魂审讯室', description: '角色内心与心理分析' },
  { id: 'side', title: '🌟 配角外传', subtitle: '边缘星轨', description: '配角故事与支线剧情' },
  { id: 'au', title: '🌍 平行世界', subtitle: '命运拐点', description: 'AU设定与另类可能' },
  { id: 'real_person', title: '👥 真人宇宙', subtitle: '第十维度', description: '真人向创作与现实联想' },
  { id: 'custom', title: '✨ 自定义', subtitle: '灵感造物', description: '自由创作与个性表达' },
  { id: 'chat', title: '💬 交流区', subtitle: '茶话会', description: '社区讨论与互动交流' }
];

interface User {
  id: string;
  name: string;
  equippedTitles?: string[];
  credits?: number;
}

export default function CreativeSpace() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [activeSection, setActiveSection] = useState('action');
  const [prompt, setPrompt] = useState('');
  const [generating, setGenerating] = useState(false);
  const [contents, setContents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
    loadContents();
  }, [activeSection]);

  const loadContents = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/contents?section=${activeSection}`);
      if (response.ok) {
        const data = await response.json();
        setContents(data.contents || []);
      }
    } catch (error) {
      console.error('Failed to load contents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGenerate = async () => {
    if (!prompt.trim() || !user || generating) return;

    setGenerating(true);
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt: prompt.trim(),
          section: activeSection,
          sectionName: SECTIONS.find(s => s.id === activeSection)?.title
        })
      });

      const result = await response.json();
      if (result.success) {
        alert('🎉 创作成功！作品已发布到创作空间');
        setPrompt('');
        loadContents();
      } else {
        alert(result.error || '生成失败，请重试');
      }
    } catch (error) {
      alert('网络错误，请重试');
    } finally {
      setGenerating(false);
    }
  };

  const handleLike = async (contentId: string) => {
    if (!user) {
      alert('请先登录');
      return;
    }

    try {
      const response = await fetch('/api/contents/like', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ contentId, userId: user.id })
      });

      if (response.ok) {
        loadContents();
      }
    } catch (error) {
      console.error('Failed to like content:', error);
    }
  };

  return (
    <UnifiedLayout 
      title="🎨 创作空间" 
      backgroundIntensity="strong"
    >
      {/* 用户信息卡片 */}
      {user && (
        <UnifiedCard style={{ marginBottom: '24px' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            flexWrap: 'wrap',
            gap: '16px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px'
            }}>
              <div style={{
                width: '48px',
                height: '48px',
                borderRadius: '50%',
                background: cpColors.gradient,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '20px',
                fontWeight: 'bold',
                color: '#000'
              }}>
                {user.name[0]}
              </div>
              
              <div>
                <div style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'rgba(255, 255, 255, 0.9)'
                }}>
                  {user.name}
                </div>
                <div style={{
                  display: 'flex',
                  gap: '6px',
                  flexWrap: 'wrap',
                  marginTop: '4px'
                }}>
                  {user.equippedTitles?.slice(0, 3).map((title, idx) => (
                    <UnifiedBadge key={idx} size="small">
                      {title}
                    </UnifiedBadge>
                  ))}
                </div>
              </div>
            </div>

            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px'
            }}>
              <UnifiedBadge variant="info">
                💰 {user.credits || 0} 逆线币
              </UnifiedBadge>
              
              <UnifiedButton
                variant="secondary"
                size="small"
                onClick={() => router.push('/recharge')}
              >
                充值
              </UnifiedButton>
            </div>
          </div>
        </UnifiedCard>
      )}

      {/* 版块选择 */}
      <UnifiedCard title="选择创作版块" style={{ marginBottom: '24px' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px'
        }}>
          {SECTIONS.map((section) => (
            <UnifiedButton
              key={section.id}
              variant={activeSection === section.id ? 'primary' : 'secondary'}
              onClick={() => setActiveSection(section.id)}
              style={{
                flexDirection: 'column',
                padding: '16px',
                height: 'auto',
                textAlign: 'left'
              }}
            >
              <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                {section.title}
              </div>
              <div style={{ 
                fontSize: '12px', 
                opacity: 0.8,
                marginBottom: '4px'
              }}>
                {section.subtitle}
              </div>
              <div style={{ 
                fontSize: '11px', 
                opacity: 0.6,
                lineHeight: 1.3
              }}>
                {section.description}
              </div>
            </UnifiedButton>
          ))}
        </div>
      </UnifiedCard>

      {/* 创作输入区域 */}
      <UnifiedCard 
        title={`在 ${SECTIONS.find(s => s.id === activeSection)?.title} 版块创作`}
        subtitle="发挥你的想象力，创造独特的内容"
        style={{ marginBottom: '24px' }}
      >
        <div style={{ marginBottom: '16px' }}>
          <UnifiedTextarea
            placeholder={`请输入你的创作提示词，例如：\n- 详细描述池骋和吴所畏的对话场景\n- 探讨角色的内心变化\n- 想象一个平行世界的故事\n- 或者任何你想创作的内容...`}
            value={prompt}
            onChange={setPrompt}
            rows={6}
            maxLength={1000}
            disabled={!user}
          />
          <div style={{
            fontSize: '12px',
            color: 'rgba(255, 255, 255, 0.5)',
            marginTop: '8px',
            display: 'flex',
            justifyContent: 'space-between'
          }}>
            <span>{prompt.length}/1000</span>
            {!user && <span>请先登录后进行创作</span>}
          </div>
        </div>

        <div style={{
          display: 'flex',
          gap: '12px',
          alignItems: 'center',
          flexWrap: 'wrap'
        }}>
          <UnifiedButton
            variant="primary"
            size="large"
            onClick={handleGenerate}
            disabled={!user || !prompt.trim() || generating}
            loading={generating}
          >
            {generating ? '创作中...' : '🚀 立即创作'}
          </UnifiedButton>

          {!user && (
            <UnifiedButton
              variant="secondary"
              onClick={() => router.push('/auth')}
            >
              登录创作
            </UnifiedButton>
          )}

          <div style={{
            fontSize: '12px',
            color: 'rgba(255, 255, 255, 0.6)',
            marginLeft: 'auto'
          }}>
            💡 每次创作消耗 10 逆线币
          </div>
        </div>
      </UnifiedCard>

      {/* 内容展示区域 */}
      <UnifiedCard 
        title={`${SECTIONS.find(s => s.id === activeSection)?.title} 最新作品`}
        subtitle="社区精彩创作，共同探索无限可能"
      >
        {loading ? (
          <div style={{ textAlign: 'center', padding: '60px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>🎨</div>
            <div style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              正在加载精彩作品...
            </div>
          </div>
        ) : contents.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '80px 20px' }}>
            <div style={{ fontSize: '64px', marginBottom: '24px' }}>✨</div>
            <div style={{ 
              fontSize: '20px', 
              marginBottom: '12px',
              color: 'rgba(255, 255, 255, 0.9)'
            }}>
              还没有作品
            </div>
            <div style={{ 
              fontSize: '14px', 
              color: 'rgba(255, 255, 255, 0.6)',
              marginBottom: '24px'
            }}>
              成为第一个在这个版块创作的用户吧！
            </div>
            <UnifiedButton
              variant="primary"
              onClick={() => {
                if (!user) {
                  router.push('/auth');
                } else {
                  document.querySelector('textarea')?.focus();
                }
              }}
            >
              {!user ? '登录创作' : '开始创作'}
            </UnifiedButton>
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
            gap: '20px'
          }}>
            {contents.map((content, idx) => (
              <div
                key={content.id || idx}
                style={{
                  padding: '20px',
                  borderRadius: '12px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  transition: 'all 0.3s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                  e.currentTarget.style.transform = 'translateY(0)';
                }}
              >
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '12px',
                  marginBottom: '12px'
                }}>
                  <div style={{
                    width: '32px',
                    height: '32px',
                    borderRadius: '50%',
                    background: cpColors.gradient,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    color: '#000'
                  }}>
                    {(content.author?.name || '匿名')[0]}
                  </div>
                  
                  <div style={{ flex: 1 }}>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: 'bold',
                      color: 'rgba(255, 255, 255, 0.9)'
                    }}>
                      {content.author?.name || '匿名用户'}
                    </div>
                    <div style={{
                      fontSize: '11px',
                      color: 'rgba(255, 255, 255, 0.5)'
                    }}>
                      {content.createdAt ? new Date(content.createdAt).toLocaleString() : '刚刚'}
                    </div>
                  </div>

                  {content.author?.equippedTitles?.slice(0, 1).map((title: string, titleIdx: number) => (
                    <UnifiedBadge key={titleIdx} size="small">
                      {title}
                    </UnifiedBadge>
                  ))}
                </div>

                <div style={{
                  fontSize: '16px',
                  fontWeight: 'bold',
                  marginBottom: '8px',
                  color: 'rgba(255, 255, 255, 0.95)',
                  lineHeight: 1.4
                }}>
                  {content.title || '精彩创作'}
                </div>

                <div style={{
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.8)',
                  lineHeight: 1.5,
                  marginBottom: '16px',
                  display: '-webkit-box',
                  WebkitLineClamp: 4,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden'
                }}>
                  {content.content || content.summary || '这是一个精彩的创作内容...'}
                </div>

                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}>
                  <div style={{
                    display: 'flex',
                    gap: '16px'
                  }}>
                    <UnifiedButton
                      variant="tertiary"
                      size="small"
                      onClick={() => handleLike(content.id)}
                      style={{
                        padding: '4px 8px',
                        fontSize: '12px'
                      }}
                    >
                      ❤️ {content.likes || 0}
                    </UnifiedButton>
                    
                    <UnifiedButton
                      variant="tertiary"
                      size="small"
                      style={{
                        padding: '4px 8px',
                        fontSize: '12px'
                      }}
                    >
                      👁️ {content.views || 0}
                    </UnifiedButton>
                  </div>

                  {content.tags?.slice(0, 2).map((tag: string, tagIdx: number) => (
                    <UnifiedBadge key={tagIdx} size="small" variant="info">
                      {tag}
                    </UnifiedBadge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </UnifiedCard>
    </UnifiedLayout>
  );
}