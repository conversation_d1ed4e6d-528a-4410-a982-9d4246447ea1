import { NextRequest, NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const FILE = 'shortlinks.json';

type ShortLink = { id: string; url: string; userId?: string; createdAt: string; hits: number };

export async function POST(req: NextRequest) {
  try{
    const body = await req.json();
    const url = String(body?.url||'').trim();
    const userId = (req.headers.get('x-user-id')||'').trim() || undefined;
    if (!url || !/^https?:\/\//i.test(url)) return NextResponse.json({ ok:false, error:'invalid url' }, { status: 400 });
    const list = await readJson<ShortLink[]>(FILE, []);
    const id = Math.random().toString(36).slice(2,8);
    const item: ShortLink = { id, url, userId, createdAt: new Date().toISOString(), hits: 0 };
    list.push(item);
    await writeJson(FILE, list);
    const origin = req.nextUrl.origin;
    return NextResponse.json({ ok:true, id, shortUrl: `${origin}/s/${id}` });
  }catch(e:any){
    const msg = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ ok:false, error: msg }, { status:500 });
  }
} 