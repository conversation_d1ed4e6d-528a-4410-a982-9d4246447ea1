import { NextResponse } from 'next/server';
import { getUserById, ensureInviteCode } from '@/lib/auth/users';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function GET(req: Request) {
  const userId = (req.headers as any).get?.('x-user-id') || '';
  if (!userId) return NextResponse.json({ ok: false, error: 'missing user id' }, { status: 400, headers: { 'Cache-Control': 'no-store' } });
  const u = await ensureInviteCode(userId) || await getUserById(userId);
  if (!u) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404, headers: { 'Cache-Control': 'no-store' } });
  return NextResponse.json({ ok: true, user: u }, { headers: { 'Cache-Control': 'no-store' } });
} 