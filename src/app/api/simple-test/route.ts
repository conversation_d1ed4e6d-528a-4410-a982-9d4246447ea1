import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json();
    return NextResponse.json({ success: true, echo: (text || '').slice(0, 100) });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    const stack = (error && typeof error === 'object' && 'stack' in error) ? (error as any).stack : undefined;
    return NextResponse.json({ success: false, error: message, stack });
  }
}