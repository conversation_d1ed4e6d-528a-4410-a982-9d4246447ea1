import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const OTP_FILE = 'otp.json';

type OtpEntry = { code:string; expiresAt:number };

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const phone = String(body?.phone || '').trim();
    const code = String(body?.code || '').trim();
    const name = body?.name ? String(body.name).trim() : '';
    if (!/^1\d{10}$/.test(phone) || !/^\d{6}$/.test(code)) return NextResponse.json({ success: false, error: '参数格式错误' }, { status: 400 });

    const store = await readJson<Record<string, OtpEntry>>(OTP_FILE, {});
    const rec = store[phone];
    if (!rec) return NextResponse.json({ success: false, error: '请先获取验证码' }, { status: 400 });
    if (Date.now() > rec.expiresAt) return NextResponse.json({ success: false, error: '验证码已过期' }, { status: 400 });
    if (rec.code !== code) return NextResponse.json({ success: false, error: '验证码不正确' }, { status: 400 });

    delete store[phone];
    await writeJson(OTP_FILE, store);

    const user = { id: `u_${phone}`, name: name || `用户${phone.slice(-4)}`, credits: 60 };
    return NextResponse.json({ success: true, message: '验证成功', user });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ success: false, error: message }, { status: 500 });
  }
} 