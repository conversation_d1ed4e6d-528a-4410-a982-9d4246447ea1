import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { sendEmailOtp } from '@/lib/email/mailer';

const OTP_FILE = 'email_otp.json';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const email = String(body?.email || '').trim().toLowerCase();
    
    // 验证邮箱格式
    if (!/^[a-zA-Z0-9._%+-]+@qq\.com$/.test(email)) {
      return NextResponse.json({ success: false, error: '请输入有效的QQ邮箱地址' }, { status: 400 });
    }

    // 生成6位数字验证码
    const code = String(Math.floor(100000 + Math.random() * 900000));

    // 保存验证码（5分钟有效期）
    const store = await readJson<Record<string, { code: string; expiresAt: number }>>(OTP_FILE, {});
    store[email] = {
      code,
      expiresAt: Date.now() + 5 * 60 * 1000 // 5分钟后过期
    };
    await writeJson(OTP_FILE, store);

    // 发送邮件
    const result = await sendEmailOtp(email, code);
    
    if (result.ok) {
      return NextResponse.json({ success: true, message: '验证码已发送至您的邮箱' });
    } else {
      return NextResponse.json({ success: false, error: result.message || '邮件发送失败' }, { status: 500 });
    }
  } catch (e: any) {
    const message = (e && typeof e === 'object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ success: false, error: message }, { status: 500 });
  }
}