import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { sendEmailOtp } from '@/lib/email/mailer';

const RESET_OTP_FILE = 'email_reset_otp.json';

type OtpEntry = { code: string; expiresAt: number; cooldownUntil?: number };

export async function POST(req: Request){
  try{
    const body = await req.json();
    const email = String(body?.email||'').trim().toLowerCase();
    if (!email.endsWith('@qq.com')) return NextResponse.json({ success:false, error: '暂时仅支持 QQ 邮箱' }, { status: 400 });

    const store = await readJson<Record<string, OtpEntry>>(RESET_OTP_FILE, {});
    const now = Date.now();
    const existing = store[email];
    if (existing?.cooldownUntil && now < existing.cooldownUntil){
      const remainSec = Math.ceil((existing.cooldownUntil - now)/1000);
      return NextResponse.json({ success:false, error:`请稍后再试（${remainSec}s）` }, { status: 429 });
    }

    const code = Math.floor(100000 + Math.random()*900000).toString();
    const expiresAt = now + 5*60*1000;
    const cooldownUntil = now + 60*1000;
    store[email] = { code, expiresAt, cooldownUntil };
    await writeJson(RESET_OTP_FILE, store);

    const expose = process.env.EXPOSE_DEV_CODE === '1';
    const sent = await sendEmailOtp(email, code);
    if (!sent.ok && !expose) return NextResponse.json({ success:false, error: sent.message||'发送失败', details: sent }, { status: 500 });

    return NextResponse.json({ success:true, message:'验证码已发送', cooldown:60, devCode: expose ? code : undefined });
  }catch(e:any){
    return NextResponse.json({ success:false, error: e?.message||String(e) }, { status: 500 });
  }
} 