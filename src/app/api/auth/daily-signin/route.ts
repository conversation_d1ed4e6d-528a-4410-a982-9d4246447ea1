import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { addCredits, UserRecord } from '@/lib/auth/users';

const FILE = 'signins.json';
const USERS_FILE = 'users.json';

type Signin = { userId: string; date: string };

function formatDate(date = new Date()): string {
  const y = date.getFullYear();
  const m = String(date.getMonth()+1).padStart(2,'0');
  const d = String(date.getDate()).padStart(2,'0');
  return `${y}-${m}-${d}`;
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const userId = String(body?.userId || '');
    if (!userId) return NextResponse.json({ ok:false, error:'missing userId' }, { status: 400 });

    const today = formatDate();
    const list = await readJson<Signin[]>(FILE, []);
    if (list.some(s => s.userId === userId && s.date === today)) {
      return NextResponse.json({ ok:true, message:'今日已签到', alreadySigned: true });
    }

    // 获取用户信息
    const users = await readJson<UserRecord[]>(USERS_FILE, []);
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return NextResponse.json({ ok:false, error:'用户不存在' }, { status: 404 });
    }

    const user = users[userIndex];
    const yesterday = formatDate(new Date(Date.now() - 24 * 60 * 60 * 1000));
    
    // 检查是否是连续签到
    let isConsecutive = false;
    if (user.lastCheckinDate === yesterday) {
      isConsecutive = true;
      user.checkinStreak = (user.checkinStreak || 0) + 1;
    } else {
      user.checkinStreak = 1; // 重新开始计算连续天数
    }
    
    // 更新用户签到信息
    user.lastCheckinDate = today;
    user.fragmentBalance = (user.fragmentBalance || 0) + 1;
    user.fragmentCount = (user.fragmentCount || 0) + 1; // 累计碎片总数
    
    // 检查是否可以兑换章节（每3个碎片兑换10灵感）
    let chapterAwarded = false;
    if (user.fragmentBalance >= 3) {
      user.fragmentBalance -= 3;
      user.credits = (user.credits || 0) + 10; // 3个碎片兑换10灵感
      chapterAwarded = true;
    }
    
    // 每连续签到7天获得1张星轨召唤券
    if (user.checkinStreak % 7 === 0) {
      user.gachaTickets = (user.gachaTickets || 0) + 1;
    }
    
    // 检查是否达到解锁"大宝天天见"头衔的条件
    if (user.fragmentCount >= 100) {
      // 添加"大宝天天见"头衔
      if (!user.unlockedTitles) user.unlockedTitles = [];
      const dailyTitleId = 'sr_2'; // "大宝"天天见头衔ID
      if (!user.unlockedTitles.includes(dailyTitleId)) {
        user.unlockedTitles.push(dailyTitleId);
      }
    }
    
    // 保存用户信息
    users[userIndex] = user;
    await writeJson(USERS_FILE, users);
    
    // 记录签到
    list.push({ userId, date: today });
    await writeJson(FILE, list);

    if (chapterAwarded) {
      return NextResponse.json({ 
        ok: true, 
        message: '签到成功，获取1枚灵感碎片，3枚碎片已自动兑换为 10 灵感！', 
        fragmentReward: true,
        chapterAwarded: true,
        newFragmentBalance: user.fragmentBalance,
        gachaTickets: user.gachaTickets
      });
    } else {
      const remainingDays = 3 - (user.fragmentBalance || 0);
      return NextResponse.json({ 
        ok: true, 
        message: `签到成功，获得 1 枚灵感碎片！再签到${remainingDays}天即可免费续写一章！`, 
        fragmentReward: true,
        chapterAwarded: false,
        newFragmentBalance: user.fragmentBalance,
        gachaTickets: user.gachaTickets
      });
    }
  } catch (e:any) {
    const message = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ ok:false, error: message }, { status: 500 });
  }
}