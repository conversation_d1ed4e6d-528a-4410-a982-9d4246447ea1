import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { verifyPassword, findUserByContact } from '@/lib/auth/users';

const OTP_FILE = 'otp.json';

type Body = { mode: 'otp'|'password'; phone: string; code?: string; password?: string };

type OtpEntry = { code:string; expiresAt:number };

export async function POST(req: Request){
  try{
    const body: Body = await req.json();
    const { mode } = body;
    const phone = String(body?.phone||'').trim();
    if (!/^1\d{10}$/.test(phone)) return NextResponse.json({ success:false, error:'请输入11位中国大陆手机号' }, { status: 400 });

    if (mode === 'otp') {
      const code = String(body?.code||'');
      if (!/^\d{6}$/.test(code)) return NextResponse.json({ success:false, error:'验证码格式错误' }, { status: 400 });
      const store = await readJson<Record<string,OtpEntry>>(OTP_FILE, {});
      const rec = store[phone];
      if (!rec) return NextResponse.json({ success:false, error:'请先获取验证码' }, { status: 400 });
      if (Date.now()>rec.expiresAt) return NextResponse.json({ success:false, error:'验证码已过期' }, { status: 400 });
      if (rec.code !== code) return NextResponse.json({ success:false, error:'验证码不正确' }, { status: 400 });
      const user = await findUserByContact('phone', phone);
      if (!user) return NextResponse.json({ success:false, error:'用户未注册' }, { status: 404 });
      delete store[phone];
      await writeJson(OTP_FILE, store);
      return NextResponse.json({ success:true, user, message:'登录成功' });
    }

    if (mode === 'password') {
      const password = String(body?.password||'');
      const user = await verifyPassword('phone', phone, password);
      if (!user) return NextResponse.json({ success:false, error:'账号或密码错误' }, { status: 401 });
      return NextResponse.json({ success:true, user, message:'登录成功' });
    }

    return NextResponse.json({ success:false, error:'不支持的登录方式' }, { status: 400 });
  }catch(e:any){
    const message = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ success:false, error: message }, { status: 500 });
  }
}