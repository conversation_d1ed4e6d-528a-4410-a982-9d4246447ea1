import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { findUserByContact, setPassword } from '@/lib/auth/users';

const RESET_OTP_FILE = 'email_reset_otp.json';

type OtpEntry = { code: string; expiresAt: number };

type Body = { email: string; code: string; newPassword: string };

export async function POST(req: Request){
  try{
    const body: Body = await req.json();
    const email = String(body?.email||'').trim().toLowerCase();
    const code = String(body?.code||'').trim();
    const newPassword = String(body?.newPassword||'');

    if (!email.endsWith('@qq.com')) return NextResponse.json({ success:false, error:'暂时仅支持 QQ 邮箱' }, { status: 400 });
    if (!/^\d{6}$/.test(code)) return NextResponse.json({ success:false, error:'验证码格式错误' }, { status: 400 });
    if (newPassword.length < 6) return NextResponse.json({ success:false, error:'新密码至少6位' }, { status: 400 });

    const store = await readJson<Record<string, OtpEntry>>(RESET_OTP_FILE, {});
    const rec = store[email];
    if (!rec) return NextResponse.json({ success:false, error:'请先获取验证码' }, { status: 400 });
    if (Date.now() > rec.expiresAt) return NextResponse.json({ success:false, error:'验证码已过期' }, { status: 400 });
    if (rec.code !== code) return NextResponse.json({ success:false, error:'验证码不正确' }, { status: 400 });

    const user = await findUserByContact('email', email);
    if (!user) return NextResponse.json({ success:false, error:'用户未注册' }, { status: 404 });

    await setPassword('email', email, newPassword);
    delete store[email];
    await writeJson(RESET_OTP_FILE, store);

    return NextResponse.json({ success:true, message:'密码已重置' });
  }catch(e:any){
    return NextResponse.json({ success:false, error: e?.message||String(e) }, { status: 500 });
  }
} 