import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { findUserByContact, verifyPassword, setLastLogin, formatUserForFrontend } from '@/lib/auth/users';

const EMAIL_OTP_FILE = 'email_otp.json';

type OtpEntry = { code: string; expiresAt: number };

type Body = { email: string; mode: 'otp'|'password'; code?: string; password?: string };

export async function POST(req: Request){
  try{
    const body: Body = await req.json();
    const email = String(body?.email||'').trim().toLowerCase();
    const mode = (body?.mode||'otp') as 'otp'|'password';

    if (!email.endsWith('@qq.com')) return NextResponse.json({ success:false, error:'暂时仅支持 QQ 邮箱' }, { status: 400 });

    if (mode === 'password'){
      const pw = String(body?.password||'');
      const u = await verifyPassword('email', email, pw);
      if (!u) return NextResponse.json({ success:false, error:'邮箱或密码不正确' }, { status: 400 });
      await setLastLogin(u.id);
      return NextResponse.json({ success:true, user: { ...u, lastLoginAt: new Date().toISOString() }, message:'登录成功' });
    }

    if (mode === 'otp'){
      const code = String(body?.code||'');
      if (!/^\d{6}$/.test(code)) return NextResponse.json({ success:false, error:'验证码格式错误' }, { status: 400 });
      
      // Test codes bypass for login
      const testCodes: Record<string, string> = {
        '<EMAIL>': '123456',
        '<EMAIL>': '234567',
        '<EMAIL>': '345678'
      };
      
      if (testCodes[email] && code === testCodes[email]) {
        const user = await findUserByContact('email', email);
        if (!user) return NextResponse.json({ success:false, error:'用户未注册' }, { status: 404 });
        await setLastLogin(user.id);
        return NextResponse.json({ success:true, user: formatUserForFrontend({ ...user, lastLoginAt: new Date().toISOString() }), message:'登录成功' });
      }
      
      const store = await readJson<Record<string, OtpEntry>>(EMAIL_OTP_FILE, {});
      const rec = store[email];
      if (!rec) return NextResponse.json({ success:false, error:'请先获取验证码' }, { status: 400 });
      if (Date.now()>rec.expiresAt) return NextResponse.json({ success:false, error:'验证码已过期' }, { status: 400 });
      if (rec.code !== code) return NextResponse.json({ success:false, error:'验证码不正确' }, { status: 400 });
      const user = await findUserByContact('email', email);
      if (!user) return NextResponse.json({ success:false, error:'用户未注册' }, { status: 404 });
      delete store[email];
      await writeJson(EMAIL_OTP_FILE, store);
      await setLastLogin(user.id);
      return NextResponse.json({ success:true, user: formatUserForFrontend({ ...user, lastLoginAt: new Date().toISOString() }), message:'登录成功' });
    }

    return NextResponse.json({ success:false, error:'invalid mode' }, { status: 400 });
  }catch(e:any){
    return NextResponse.json({ success:false, error: e?.message||String(e) }, { status: 500 });
  }
} 