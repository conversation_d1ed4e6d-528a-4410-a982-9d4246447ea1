import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { getSmsProvider } from '@/lib/sms/provider';

const OTP_FILE = 'otp.json';

type OtpEntry = { code: string; expiresAt: number; cooldownUntil?: number };

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const phone = String(body?.phone || '').trim();
    if (!/^1\d{10}$/.test(phone)) return NextResponse.json({ success: false, error: '请输入11位中国大陆手机号' }, { status: 400 });

    const store = await readJson<Record<string, OtpEntry>>(OTP_FILE, {});
    const existing = store[phone];
    const now = Date.now();
    if (existing?.cooldownUntil && now < existing.cooldownUntil) {
      const remainSec = Math.ceil((existing.cooldownUntil - now) / 1000);
      return NextResponse.json({ success: false, error: `请稍后再试（${remainSec}s）` }, { status: 429 });
    }

    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const expiresAt = now + 5 * 60 * 1000; // 5分钟
    const cooldownUntil = now + 60 * 1000; // 60秒冷却
    store[phone] = { code, expiresAt, cooldownUntil };
    await writeJson(OTP_FILE, store);

    const provider = getSmsProvider();
    const result = await provider.sendVerificationCode(phone, code);
    if (!result.ok) return NextResponse.json({ success: false, error: result.message || '短信发送失败' }, { status: 500 });

    const expose = process.env.EXPOSE_DEV_CODE === '1' || process.env.NODE_ENV !== 'production';
    return NextResponse.json({ success: true, message: result.message || '验证码已发送', devCode: expose ? (result.devCode || code) : undefined, cooldown: 60 });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ success: false, error: message }, { status: 500 });
  }
} 