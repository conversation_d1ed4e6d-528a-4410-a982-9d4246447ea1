import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { createUser, setPassword, addCredits, getUserById, getUserByInviteCode } from '@/lib/auth/users';

const OTP_FILE = 'otp.json';
const INVITES_FILE = 'invites.json';

type Body = { 
  contact: string; 
  code: string; 
  password: string; 
  name?: string; 
  inviteCode?: string;
  contactType: 'phone' | 'email';
};

type OtpEntry = { code:string; expiresAt:number };

type Invite = { inviterId: string; inviteeId: string; createdAt: string };

export async function POST(req: Request){
  try{
    const body: Body = await req.json();
    const contact = String(body?.contact||'').trim();
    const code = String(body?.code||'');
    const password = String(body?.password||'');
    const name = String(body?.name||'');
    const inviteCode = body?.inviteCode ? String(body.inviteCode) : '';
    const contactType = body?.contactType || 'phone';

    // 验证联系方式类型
    if (contactType === 'phone') {
      if (!/^1\d{10}$/.test(contact)) return NextResponse.json({ success:false, error:'请输入11位中国大陆手机号' }, { status: 400 });
    } else if (contactType === 'email') {
      // 仅支持QQ邮箱
      if (!/^[a-zA-Z0-9._%+-]+@qq\.com$/.test(contact)) return NextResponse.json({ success:false, error:'目前仅支持QQ邮箱注册' }, { status: 400 });
    } else {
      return NextResponse.json({ success:false, error:'无效的联系方式类型' }, { status: 400 });
    }

    if (!/^\d{6}$/.test(code)) return NextResponse.json({ success:false, error:'验证码格式错误' }, { status: 400 });
    if (password.length < 6) return NextResponse.json({ success:false, error:'密码至少6位' }, { status: 400 });

    const store = await readJson<Record<string, OtpEntry>>(OTP_FILE, {});
    const rec = store[contact];
    if (!rec) return NextResponse.json({ success:false, error:'请先获取验证码' }, { status: 400 });
    if (Date.now()>rec.expiresAt) return NextResponse.json({ success:false, error:'验证码已过期' }, { status: 400 });
    if (rec.code !== code) return NextResponse.json({ success:false, error:'验证码不正确' }, { status: 400 });
    delete store[contact];
    await writeJson(OTP_FILE, store);

    const user = await createUser(contactType, contact, name || '', password);
    await setPassword(contactType, contact, password);

    // 邀请奖励：邀请1人，双方各得30灵感
    if (inviteCode) {
      const inviter = (await getUserById(inviteCode)) || (await getUserByInviteCode(inviteCode));
      if (inviter && inviter.id !== user.id) {
        const invites = await readJson<Invite[]>(INVITES_FILE, []);
        const alreadyInvited = invites.some(i => i.inviteeId === user.id);
        if (!alreadyInvited) {
          invites.push({ inviterId: inviter.id, inviteeId: user.id, createdAt: new Date().toISOString() });
          await writeJson(INVITES_FILE, invites);

          // 双方各得30灵感
          await addCredits(user.id, 30);
          await addCredits(inviter.id, 30);
        }
      }
    }

    const fresh = await getUserById(user.id);
    return NextResponse.json({ success:true, user: fresh || user, message:'注册成功' });
  }catch(e:any){
    const message = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ success:false, error: message }, { status: 500 });
  }
}