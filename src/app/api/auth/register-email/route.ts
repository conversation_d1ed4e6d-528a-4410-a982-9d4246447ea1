import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { createUser, setPassword, addCredits, getUserById, getUserByInviteCode, formatUserForFrontend } from '@/lib/auth/users';

const EMAIL_OTP_FILE = 'email_otp.json';
const INVITES_FILE = 'invites.json';

type OtpEntry = { code: string; expiresAt: number };

type Invite = { inviterId: string; inviteeId: string; createdAt: string };

export async function POST(req: Request){
  try{
    const body = await req.json();
    const email = String(body?.email||'').trim().toLowerCase();
    const code = String(body?.code||'').trim();
    const password = String(body?.password||'');
    const name = String(body?.name||'');
    const inviteCode = body?.inviteCode ? String(body.inviteCode) : '';

    if (!email.endsWith('@qq.com')) return NextResponse.json({ success:false, error:'暂时仅支持 QQ 邮箱' }, { status: 400 });
    if (password.length < 6) return NextResponse.json({ success:false, error:'密码至少6位' }, { status: 400 });

    const expose = process.env.EXPOSE_DEV_CODE === '1';
    const devMap: Record<string, string> = (()=>{
      const raw = process.env.DEV_TEST_EMAILS || '';
      const map: Record<string,string> = {};
      raw.split(',').map(s=>s.trim()).filter(Boolean).forEach(pair=>{ const [em,pw] = pair.split(':'); if (em && pw) map[em.toLowerCase()] = pw; });
      if (Object.keys(map).length===0){
        map['<EMAIL>']='alpha123';
        map['<EMAIL>']='beta123';
        map['<EMAIL>']='gamma123';
        map['<EMAIL>']='delta123';
        map['<EMAIL>']='omega123';
      }
      return map;
    })();

    async function applyInviteRewards(newUserId: string){
      if (!inviteCode) return;
      const now = new Date();
      const cutoff = new Date('2025-08-19T23:59:59.999+08:00');
      if (now > cutoff) return;
      const inviter = (await getUserById(inviteCode)) || (await getUserByInviteCode(inviteCode));
      if (inviter && inviter.id !== newUserId) {
        const invites = await readJson<Invite[]>(INVITES_FILE, []);
        const alreadyInvited = invites.some(i => i.inviteeId === newUserId);
        if (!alreadyInvited){
          invites.push({ inviterId: inviter.id, inviteeId: newUserId, createdAt: new Date().toISOString() });
          await writeJson(INVITES_FILE, invites);
          await addCredits(newUserId, 1);
          await addCredits(inviter.id, 1);
        }
      }
    }

    // Test accounts bypass - direct registration without OTP
    const testAccounts: Record<string, { password: string; credits: number }> = {
      '<EMAIL>': { password: 'test123', credits: 1000 },
      '<EMAIL>': { password: 'test234', credits: 500 },
      '<EMAIL>': { password: 'test345', credits: 2000 }
    };
    
    if (testAccounts[email]) {
      const testAccount = testAccounts[email];
      const user = await createUser('email', email, name || email.split('@')[0], testAccount.password);
      await setPassword('email', email, testAccount.password);
      user.credits = testAccount.credits;
      await applyInviteRewards(user.id);
      const fresh = await getUserById(user.id);
      return NextResponse.json({ success:true, user: formatUserForFrontend(fresh || user), message:'注册成功（测试账号）' });
    }

    // Dev test bypass: allow fixed emails/passwords without OTP
    if (expose && devMap[email] && password === devMap[email]){
      const user = await createUser('email', email, name || email.split('@')[0], password);
      await setPassword('email', email, password);
      await applyInviteRewards(user.id);
      const fresh = await getUserById(user.id);
      return NextResponse.json({ success:true, user: formatUserForFrontend(fresh || user), message:'注册成功（测试账号）' });
    }

    // Normal OTP flow
    if (!/^\d{6}$/.test(code)) return NextResponse.json({ success:false, error:'验证码格式错误' }, { status: 400 });

    // Test codes bypass
    const testCodes: Record<string, string> = {
      '<EMAIL>': '123456',
      '<EMAIL>': '234567', 
      '<EMAIL>': '345678'
    };
    
    if (testCodes[email] && code === testCodes[email]) {
      const user = await createUser('email', email, name || email.split('@')[0], password);
      await setPassword('email', email, password);
      const testCredits: Record<string, number> = { '<EMAIL>': 1000, '<EMAIL>': 500, '<EMAIL>': 2000 };
      if (testCredits[email]) {
        user.credits = testCredits[email];
      }
      await applyInviteRewards(user.id);
      const fresh = await getUserById(user.id);
      return NextResponse.json({ success:true, user: formatUserForFrontend(fresh || user), message:'注册成功（测试账号）' });
    }

    const store = await readJson<Record<string, OtpEntry>>(EMAIL_OTP_FILE, {});
    const rec = store[email];
    if (!rec) return NextResponse.json({ success:false, error:'请先获取验证码' }, { status: 400 });
    if (Date.now() > rec.expiresAt) return NextResponse.json({ success:false, error:'验证码已过期' }, { status: 400 });
    if (rec.code !== code) return NextResponse.json({ success:false, error:'验证码不正确' }, { status: 400 });

    delete store[email];
    await writeJson(EMAIL_OTP_FILE, store);

    const user = await createUser('email', email, name || email.split('@')[0], password);
    await setPassword('email', email, password);

    await applyInviteRewards(user.id);

    const fresh = await getUserById(user.id);
    return NextResponse.json({ success:true, user: formatUserForFrontend(fresh || user), message:'注册成功（暂时仅支持QQ邮箱）' });
  }catch(e:any){
    return NextResponse.json({ success:false, error: e?.message||String(e) }, { status: 500 });
  }
} 