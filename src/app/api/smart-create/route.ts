import { NextRequest, NextResponse } from 'next/server';
import { semanticSearch } from '@/lib/novel-search-service';

export async function POST(request: NextRequest) {
  console.log('🧪 === 智能创作测试API ===');
  
  try {
    const body = await request.json();
    const { 
      userInput, 
      mode = 'smart_search', // 'smart_search' | 'style_mimic' | 'auto'
      authorStyle,
      referenceText,
      apiKey 
    } = body;

    if (!userInput) {
      return NextResponse.json({
        error: '请提供创作描述',
        usage: {
          userInput: '两人在校园相遇的故事',
          mode: 'smart_search | style_mimic | auto',
          authorStyle: '墨香铜臭 | priest | 天下归元',
          referenceText: '可选：提供参考文本片段'
        }
      }, { status: 400 });
    }

    console.log('📝 用户输入:', userInput);
    console.log('🎯 生成模式:', mode);
    
    let result;

    switch (mode) {
      case 'smart_search':
        console.log('🔍 执行智能检索模式...');
        
        const searchResults = semanticSearch(userInput);
        
        if (searchResults.results.length > 0) {
          result = {
            mode: 'smart_search',
            searchResults: searchResults.results.map(r => ({
              title: r.novel.title,
              author: r.novel.author,
              genre: r.novel.genre,
              score: r.score,
              matchReasons: r.matchReasons,
              preview: r.novel.sampleText.substring(0, 200) + '...'
            })),
            suggestions: searchResults.suggestions,
            recommendedAuthors: <AUTHORS>
            message: `基于您的输入"${userInput}"，找到了 ${searchResults.results.length} 个相关作品作为创作参考`
          };
        } else {
          result = {
            mode: 'smart_search',
            searchResults: [],
            suggestions: ['试试"古风相遇"', '试试"现代校园"', '试试"悬疑推理"'],
            message: '未找到完全匹配的参考作品，建议尝试其他关键词'
          };
        }
        break;

      case 'style_mimic':
        console.log('✨ 执行风格模仿模式...');
        
        if (!authorStyle && !referenceText) {
          return NextResponse.json({
            error: '风格模仿模式需要提供作者风格或参考文本',
            availableAuthors: <AUTHORS>
          }, { status: 400 });
        }

        // 如果提供了API密钥，尝试实际生成
        if (apiKey) {
          try {
            // 临时设置API密钥用于测试
            process.env.KIMI_API_KEY = apiKey;
            
            // 注意：generateHighQualityContent函数已被移除
            // const generationResult = await generateHighQualityContent(userInput, {
            //   authorStyle,
            //   referenceText,
            //   aiProvider: 'kimi'
            // });

            result = {
              mode: 'style_mimic',
              generated: false,
              error: 'generateHighQualityContent功能暂时不可用',
              fallback: `展示风格分析功能...
              
【选定作者风格】${authorStyle || '基于参考文本'}
【创作主题】${userInput}
【风格特色】模仿所选作者的叙事风格、对话特点和文字韵味
【预期效果】生成1500-2000字的高质量文学作品

要查看实际生成效果，请提供有效的API密钥。`
            };
          } catch (error) {
            const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
            result = {
              mode: 'style_mimic',
              generated: false,
              error: `生成失败: ${message}`,
              fallback: `展示风格分析功能...
              
【选定作者风格】${authorStyle || '基于参考文本'}
【创作主题】${userInput}
【风格特色】模仿所选作者的叙事风格、对话特点和文字韵味
【预期效果】生成1500-2000字的高质量文学作品

要查看实际生成效果，请提供有效的API密钥。`
            };
          }
        } else {
          result = {
            mode: 'style_mimic',
            generated: false,
            demo: true,
            selectedAuthor: authorStyle,
            stylePreview: authorStyle ? ((): any => {
              const map: Record<string, { 特色: string; 适合题材: string; 代表作品: string; }> = {
                '墨香铜臭': {
                  特色: '现代白话，对话生动，情感表达直接',
                  适合题材: '现代AU、校园、都市情感',
                  代表作品: '天官赐福、魔道祖师'
                },
                'priest': {
                  特色: '文笔沉稳，心理描写深刻，氛围营造出色',
                  适合题材: '悬疑推理、现实题材、心理分析',
                  代表作品: '默读、破云'
                },
                '天下归元': {
                  特色: '古风典雅，文采斐然，善用典故',
                  适合题材: '古风仙侠、宫廷权谋、历史架空',
                  代表作品: '凤于九天、帝王业'
                }
              };
              return map[String(authorStyle)] || null;
            })() : null,
            message: '提供API密钥可查看实际生成效果'
          };
        }
        break;

      case 'auto':
        console.log('🤖 执行自动智能模式...');
        
        // 首先进行搜索
        const autoSearchResults = semanticSearch(userInput);
        
        result = {
          mode: 'auto',
          step1_search: {
            found: autoSearchResults.results.length,
            results: autoSearchResults.results.slice(0, 2).map(r => ({
              title: r.novel.title,
              author: r.novel.author,
              score: r.score
            }))
          },
          step2_recommendation: autoSearchResults.results.length > 0 ? {
            recommended: '智能检索模式',
            reason: `找到 ${autoSearchResults.results.length} 个相关参考作品`,
            bestMatch: autoSearchResults.results[0].novel.title,
            suggestedAuthor: autoSearchResults.results[0].novel.author
          } : {
            recommended: '通用创作模式',
            reason: '未找到特别匹配的参考作品，建议使用通用创作',
            suggestions: ['尝试更具体的描述', '指定特定的作者风格', '提供参考文本']
          },
          nextSteps: [
            '使用 mode: "smart_search" 查看详细匹配结果',
            '使用 mode: "style_mimic" 指定特定作者风格',
            '提供 apiKey 获取实际生成内容'
          ]
        };
        break;

      default:
        return NextResponse.json({
          error: '无效的模式',
          validModes: ['smart_search', 'style_mimic', 'auto']
        }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      userInput,
      result,
      timestamp: new Date().toISOString(),
      tips: {
        forBetterResults: [
          '使用更具体的描述（如：谢怜和花城在现代校园相遇）',
          '指定喜欢的作者风格',
          '提供API密钥获取实际生成内容'
        ],
        availableFeatures: [
          '🔍 智能检索：基于输入找到相似作品',
          '✨ 风格模仿：模仿知名作者的写作风格',
          '🤖 自动模式：智能分析并推荐最佳生成方式'
        ]
      }
    });

  } catch (error) {
    console.error('❌ 测试API错误:', error);
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({
      success: false,
      error: `生成失败: ${message}`,
      stack: process.env.NODE_ENV === 'development' ? message : undefined
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    return NextResponse.json({ ok: true });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ success: false, error: message });
  }
}