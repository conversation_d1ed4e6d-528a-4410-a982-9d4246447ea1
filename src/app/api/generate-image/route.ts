import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const content: string = (body?.content || '').toString();
    const userId: string = (body?.userId || 'guest').toString();
    const multi: boolean = !!body?.multi;
    const mode: 'fixed'|'pixel' = (body?.mode === 'pixel' ? 'pixel' : 'fixed'); // 默认 fixed
    const fixedChars: number = Number.isFinite(body?.fixedChars) ? Math.max(8, Math.min(40, Math.floor(body.fixedChars))) : 27;
    const debug: boolean = !!body?.debug;
    const format: 'png'|'svg' = (body?.format === 'svg' ? 'svg' : 'png');

    const sharp = (await import('sharp')).default;

    // Canvas 1242x2208（9:16 更宽）
    const width = 1242;
    const height = 2208;

    // Margins and layout（左右对称）
    const M = { left: 80, right: 80, top: 200, bottom: 180 };
    const availH = height - M.top - M.bottom;

    // Brand gradient background
    const svgBg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="g" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#666BCE"/>
      <stop offset="60%" stop-color="#C2A8F2"/>
      <stop offset="100%" stop-color="#FFD64F"/>
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#g)"/>
</svg>`;

    const bg = await sharp(Buffer.from(svgBg)).png().toBuffer();

    // Typography
    const baseFont = 40; // px
    const minFont = 30;

    const textColor = '#1F2547';
    const metaColor = '#4B4E8F';

    // wrap: fixed-chars-per-line
    function wrapByFixed(text: string, perLine: number): string[] {
      const out: string[] = [];
      const norm = text.replace(/\r/g, '');
      for (const raw of norm.split('\n')) {
        if (raw.length === 0) { out.push(''); continue; }
        let i = 0;
        while (i < raw.length) {
          const seg = raw.slice(i, i + perLine);
          out.push(seg);
          i += perLine;
        }
      }
      return out;
    }

    // wrap: pixel-width based (previous implementation, more conservative widths)
    const safeWidth = (width - M.left - M.right);
    function measureCharWidth(ch: string, fs: number): number {
      const full = fs * 0.62;   // CJK conservative
      const half = fs * 0.36;  // ASCII conservative
      return /[\x00-\x7F]/.test(ch) ? half : full;
    }
    function wrapByWidth(text: string, fs: number, maxWidth: number): string[] {
      const lines: string[] = [];
      const norm = text.replace(/\r/g, '');
      for (const raw of norm.split('\n')) {
        let buf = '';
        let curW = 0;
        const limit = Math.max(10, Math.floor(maxWidth));
        for (let i = 0; i < raw.length; i++) {
          const ch = raw[i];
          const cw = measureCharWidth(ch, fs);
          if (curW + cw > limit) {
            if (buf.length > 0) {
              lines.push(buf);
              buf = ch;
              curW = cw;
            } else {
              lines.push(ch);
              buf = '';
              curW = 0;
            }
          } else {
            buf += ch;
            curW += cw;
          }
        }
        if (buf) lines.push(buf);
      }
      return lines.map(s => s.trimEnd());
    }

    let fs = baseFont;
    let lineGap = Math.round(fs * 1.55);
    let allLines: string[] = [];

    if (mode === 'fixed') {
      allLines = wrapByFixed(content, fixedChars);
    } else {
      // pixel mode with extra guard
      const guard = Math.max(12, Math.round(fs * 0.3));
      const effectiveWidth = safeWidth - guard - 4;
      allLines = wrapByWidth(content, fs, effectiveWidth);
    }

    // 单页模式下降低字号以适配高度（仅像素模式需要收缩；固定字数无需因宽度调整）
    const maxLinesPerPageAt = (fontSize: number) => Math.max(8, Math.floor(availH / Math.round(fontSize * 1.55)));
    while (!multi && mode === 'pixel' && allLines.length > maxLinesPerPageAt(fs) && fs > minFont) {
      fs -= 2;
      const guard = Math.max(12, Math.round(fs * 0.3));
      const effectiveWidth = safeWidth - guard - 4;
      lineGap = Math.round(fs * 1.48);
      allLines = wrapByWidth(content, fs, effectiveWidth);
    }

    function buildSvgPage(lines: string[], pageIdx: number, total: number): string {
      const blockH = (lines.length - 1) * lineGap + fs;
      const startY = M.top + Math.max(0, Math.floor((availH - blockH) / 2)) + fs;

      const texts = lines.map((l,i)=>{
        const y = startY + i * lineGap;
        return `<text x="${M.left}" y="${y}" class="t">${escapeXml(l)}</text>`;
      }).join('');

      // 使用系统常见中文字体族，浏览器端渲染可正确回退；服务器端若无CJK字体，建议选择SVG格式由浏览器渲染
      const svgText = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
  <style>
    .t { font-family: 'Noto Sans SC','Noto Serif SC','Source Han Serif SC','Source Han Sans SC','PingFang SC','Microsoft YaHei','SimSun',sans-serif; fill: ${textColor}; font-size: ${fs}px; font-weight: 600; }
    .m { font-family: 'Noto Sans SC','PingFang SC','Microsoft YaHei','SimSun',sans-serif; fill: ${metaColor}; font-size: 26px; }
    .wm { font-family: 'Noto Sans SC','PingFang SC','Microsoft YaHei','SimSun',sans-serif; fill: rgba(50,50,90,0.08); font-size: 46px; font-weight: 700; }
  </style>
  ${texts}
  <text x="${M.left}" y="${height - M.bottom + 44}" class="m">User: ${escapeXml(userId)} · Page ${pageIdx+1}/${total}</text>
  <text x="${M.left}" y="${height - M.bottom + 86}" class="m">https://nixian.top</text>
  <g transform="translate(${width/2},${height/2}) rotate(-30)">
    <text x="0" y="0" text-anchor="middle" class="wm">nixian.top · ${escapeXml(userId)}</text>
  </g>
</svg>`;
      return svgText;
    }

    async function renderPngPage(lines: string[], pageIdx: number, total: number) {
      const svg = buildSvgPage(lines, pageIdx, total);
      const txt = await sharp(Buffer.from(svg)).png().toBuffer();
      return await sharp(bg).composite([{ input: txt }]).png().toBuffer();
    }

    if (multi) {
      const maxLinesPerPage = Math.max(8, Math.floor(availH / lineGap));
      if (format === 'svg') {
        const pagesSvg: string[] = [];
        for (let i = 0; i < allLines.length; i += maxLinesPerPage) {
          const chunk = allLines.slice(i, i + maxLinesPerPage);
          const svg = buildSvgPage(chunk, Math.floor(i / maxLinesPerPage), Math.ceil(allLines.length / maxLinesPerPage));
          pagesSvg.push(`data:image/svg+xml;base64,${Buffer.from(svg).toString('base64')}`);
        }
        if (debug) {
          const pagesText = [] as string[][];
          for (let i = 0; i < allLines.length; i += maxLinesPerPage) pagesText.push(allLines.slice(i, i + maxLinesPerPage));
          return NextResponse.json({ ok: true, pages: pagesSvg, wrap: { mode, fixedChars, lineGap, fontSize: fs, pagesText }, format });
        }
        return NextResponse.json({ ok: true, pages: pagesSvg, format });
      } else {
        const pages: Buffer[] = [];
      for (let i = 0; i < allLines.length; i += maxLinesPerPage) {
        const chunk = allLines.slice(i, i + maxLinesPerPage);
          const pageBuf = await renderPngPage(chunk, Math.floor(i / maxLinesPerPage), Math.ceil(allLines.length / maxLinesPerPage));
        pages.push(pageBuf);
      }
      const base64Pages = pages.map(b => `data:image/png;base64,${b.toString('base64')}`);
      if (debug) {
        const pagesText = [] as string[][];
        for (let i = 0; i < allLines.length; i += maxLinesPerPage) pagesText.push(allLines.slice(i, i + maxLinesPerPage));
          return NextResponse.json({ ok: true, pages: base64Pages, wrap: { mode, fixedChars, lineGap, fontSize: fs, pagesText }, format });
      }
        return NextResponse.json({ ok: true, pages: base64Pages, format });
      }
    }

    const singleLines = allLines.slice(0, Math.max(8, Math.floor(availH / lineGap)));
    if (format === 'svg') {
      const svg = buildSvgPage(singleLines, 0, 1);
      return new NextResponse(svg, { headers: { 'Content-Type': 'image/svg+xml; charset=utf-8' } });
    } else {
      const out = await renderPngPage(singleLines, 0, 1);
    const ab = out.buffer.slice(out.byteOffset, out.byteOffset + out.byteLength);
    return new NextResponse(ab as any, { headers: { 'Content-Type': 'image/png' } });
    }
  } catch (e) {
    const msg = (e && typeof e === 'object' && 'message' in e) ? (e as any).message : String(e);
    if (msg && msg.includes("Cannot find module 'sharp'")) {
      return NextResponse.json({ error: '缺少 sharp 依赖，请先安装：npm i sharp' }, { status: 501 });
    }
    return NextResponse.json({ error: '生成失败', detail: msg }, { status: 500 });
  }
}

function escapeXml(s: string) {
  return s.replace(/[&<>"']/g, (c) => ({ '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&apos;' } as any)[c] || c);
}
