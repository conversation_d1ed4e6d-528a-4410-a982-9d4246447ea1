import { NextRequest, NextResponse } from 'next/server';
import { ingestBookIndex, loadBookText, loadIngestIndex } from '@/lib/ingest/text-ingest';
import { computeStyleMetricsFromText, saveStyleMetrics } from '@/lib/knowledge/style-metrics';
import { extractKnowledge } from '@/lib/knowledge/extractors';
import { getServiceSupabaseClient } from '@/lib/db/supabase';
import { embedTextsGoogle } from '@/lib/embedding';

async function embedBatch(texts: string[]): Promise<number[][]> {
	return await embedTextsGoogle(texts);
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));
    const workTitle = body.workTitle || '逆袭';
    const filePath = body.filePath as string | undefined; // 可选：指定路径
    const size = typeof body.size === 'number' ? body.size : 800;
    const overlap = typeof body.overlap === 'number' ? body.overlap : 150;
    const pushToSupabase = !!body.pushToSupabase;

    const { text } = loadBookText(filePath);

    const index = ingestBookIndex(workTitle, filePath, { size, overlap });

    const metrics = computeStyleMetricsFromText(text.substring(0, Math.min(200000, text.length)));
    saveStyleMetrics(workTitle, metrics);

    const knowledge = extractKnowledge(workTitle);

    if (pushToSupabase) {
      const svc = getServiceSupabaseClient();
      if (!svc) {
        console.warn('⚠️ 未配置 Supabase service key，跳过入库');
      } else {
        console.log('📤 推送 chunks 至 Supabase...');
        const chunks = index.chunks;
        const vectors = await embedBatch(chunks.map(c => c.text));
        const rows = chunks.map((c, i) => {
          const v = vectors?.[i];
          const emb = Array.isArray(v) && v.length > 0 ? v : null;
          return {
            work_title: workTitle,
            chap_index: c.chapIndex,
            chapter_no: c.chapterNo || null,
            chapter_title: c.chapterTitle || null,
            start: c.start,
            end: c.end,
            chunk_text: c.text,
            embedding: emb,
          } as any;
        });
        try {
          const withEmb = rows.filter(r => !!r.embedding).length;
          const withoutEmb = rows.length - withEmb;
          console.log(`🧮 待写入: ${rows.length} 行，其中含向量: ${withEmb} 行，空向量: ${withoutEmb} 行`);
          const { error } = await svc.from('work_chunks').insert(rows);
          if (error) console.error('Supabase insert error:', error.message);
          else console.log('✅ Supabase 入库完成:', rows.length);
        } catch (e:any) {
          console.error('❌ Supabase insert 异常:', e?.message || String(e));
        }
      }
    }

    return NextResponse.json({ success: true, workTitle, totalChunks: index.chunks.length, totalLength: index.totalLength, styleMetrics: metrics, knowledge, pushed: pushToSupabase });
  } catch (e) {
    const err = e as Error;
    return NextResponse.json({ success: false, error: err.message }, { status: 400 });
  }
}

export async function GET() {
	try {
		const idx = loadIngestIndex('逆袭');
		if (!idx) return NextResponse.json({ ok: false, message: '未找到 data/逆袭-index.json' }, { status: 404 });
		const sample = idx.chunks.slice(0, 3).map(c => ({ chapIndex: c.chapIndex, chapterNo: c.chapterNo, chapterTitle: c.chapterTitle, start: c.start, end: c.end, textPreview: (c.text||'').slice(0, 120) }));
		return NextResponse.json({ ok: true, workTitle: idx.workTitle, sourceFile: idx.sourceFile, totalLength: idx.totalLength, chunkSize: idx.chunkSize, overlap: idx.overlap, createdAt: idx.createdAt, chapters: (idx.chapters||[]).slice(0,5), sample });
	} catch (e:any) {
		return NextResponse.json({ ok: false, error: e?.message||String(e) }, { status: 500 });
  }
} 