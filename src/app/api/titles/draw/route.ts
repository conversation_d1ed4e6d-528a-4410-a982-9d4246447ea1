import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { R_RANDOM_TITLES, SR_RANDOM_TITLES, SSR_RANDOM_TITLES, getTitleById } from '@/lib/titles/titles';

const USERS_FILE = 'users.json';

export async function POST(req: Request) {
  try {
    const { userId, rarity, cost } = await req.json();
    
    if (!userId || !rarity) {
      return NextResponse.json({ ok: false, error: '参数错误' }, { status: 400 });
    }

    // 获取用户数据
    const users = await readJson<any[]>(USERS_FILE, []);
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return NextResponse.json({ ok: false, error: '用户不存在' }, { status: 404 });
    }

    const user = users[userIndex];
    const currentFragments = user.fragmentBalance || 0;
    
    // 检查碎片是否足够 (R级通过其他方式免费获得)
    if (rarity !== 'R' && currentFragments < cost) {
      return NextResponse.json({ ok: false, error: '灵感碎片不足' }, { status: 400 });
    }

    // 根据稀有度选择卡池
    let titlePool;
    switch (rarity) {
      case 'R':
        titlePool = R_RANDOM_TITLES;
        break;
      case 'SR':
        titlePool = SR_RANDOM_TITLES;
        break;
      case 'SSR':
        titlePool = SSR_RANDOM_TITLES;
        break;
      default:
        return NextResponse.json({ ok: false, error: '无效的稀有度' }, { status: 400 });
    }

    // 过滤已解锁的头衔
    const unlockedTitles = user.unlockedTitles || [];
    const availableTitles = titlePool.filter(title => !unlockedTitles.includes(title.id));
    
    if (availableTitles.length === 0) {
      return NextResponse.json({ ok: false, error: '该卡池所有头衔已解锁' }, { status: 400 });
    }

    // 随机选择头衔
    const randomIndex = Math.floor(Math.random() * availableTitles.length);
    const selectedTitle = availableTitles[randomIndex];

    // 更新用户数据
    if (rarity !== 'R') {
      user.fragmentBalance = currentFragments - cost;
    }
    user.unlockedTitles = [...unlockedTitles, selectedTitle.id];

    // 保存更新
    users[userIndex] = user;
    await writeJson(USERS_FILE, users);

    return NextResponse.json({
      ok: true,
      title: selectedTitle,
      newFragmentBalance: user.fragmentBalance,
      newUnlockedTitles: user.unlockedTitles
    });

  } catch (error) {
    console.error('Draw API error:', error);
    return NextResponse.json({ ok: false, error: '服务器错误' }, { status: 500 });
  }
} 