import { NextRequest, NextResponse } from 'next/server';
import { generateContent } from '@/lib/ai-service';

export async function POST(request: NextRequest) {
  try {
    const { text } = await request.json();
    return NextResponse.json({ success: true, echo: (text || '').slice(0, 50) });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ success: false, error: message });
  }
}

export async function GET() {
  try {
    return NextResponse.json({ ok: true });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ error: '测试失败', details: message });
  }
}