import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  console.log('🧪 === 硬编码API测试（跳过环境变量）===');
  
  try {
    const { apiKey } = await request.json();
    
    if (!apiKey) {
      return NextResponse.json({
        error: '请提供API密钥进行测试',
        usage: 'POST { "apiKey": "your-kimi-api-key" }'
      });
    }
    
    console.log('📡 使用硬编码API密钥测试Kimi...');
    
    const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify({
        model: 'moonshot-v1-8k',
        messages: [{
          role: 'user',
          content: '请写一个简短的故事：谢怜与花城在校园相遇'
        }],
        max_tokens: 200,
        temperature: 0.8
      }),
    });
    
    console.log('📡 响应状态:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API调用失败:', errorText);
      return NextResponse.json({
        success: false,
        error: 'API调用失败',
        status: response.status,
        details: errorText
      });
    }
    
    const data = await response.json();
    console.log('📄 API响应:', data);
    
    if (data.choices && data.choices[0]) {
      const result = data.choices[0].message.content;
      console.log('✅ 生成成功! 内容长度:', result.length);
      
      return NextResponse.json({
        success: true,
        result,
        message: 'AI生成成功 - 证明API和代码都正常工作',
        length: result.length
      });
    }
    
    return NextResponse.json({
      success: false,
      error: 'API响应格式异常',
      data
    });
    
  } catch (error) {
    console.error('❌ 错误:', error);
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    const stack = (error && typeof error === 'object' && 'stack' in error) ? (error as any).stack : undefined;
    return NextResponse.json({
      success: false,
      error: message,
      stack
    });
  }
}