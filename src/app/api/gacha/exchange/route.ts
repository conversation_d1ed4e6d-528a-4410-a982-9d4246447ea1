import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { UserRecord } from '@/lib/auth/users';

const USERS_FILE = 'users.json';

export async function POST(req: Request) {
  try {
    const { userId } = await req.json();
    
    if (!userId) {
      return NextResponse.json({ ok: false, error: '参数错误' }, { status: 400 });
    }

    // 获取用户数据
    const users = await readJson<UserRecord[]>(USERS_FILE, []);
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return NextResponse.json({ ok: false, error: '用户不存在' }, { status: 404 });
    }

    const user = users[userIndex];
    
    // 检查是否有足够的灵感（100灵感 = 1张券）
    const fragmentCount = user.fragmentCount || 0;
    if (fragmentCount < 100) {
      return NextResponse.json({ ok: false, error: '灵感不足，需要累计100枚灵感碎片才能兑换1张星轨召唤券' }, { status: 400 });
    }

    // 兑换星轨召唤券
    user.fragmentCount = fragmentCount - 100;
    user.gachaTickets = (user.gachaTickets || 0) + 1;

    // 保存更新
    users[userIndex] = user;
    await writeJson(USERS_FILE, users);

    return NextResponse.json({
      ok: true,
      newFragmentCount: user.fragmentCount,
      newGachaTickets: user.gachaTickets
    });

  } catch (error) {
    console.error('Exchange API error:', error);
    return NextResponse.json({ ok: false, error: '服务器错误' }, { status: 500 });
  }
}