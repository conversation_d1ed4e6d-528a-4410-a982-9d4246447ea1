import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { R_RANDOM_TITLES, SR_RANDOM_TITLES, SSR_RANDOM_TITLES, getTitleById } from '@/lib/titles/titles';

const USERS_FILE = 'users.json';

// 抽卡概率配置
const GACHA_RATES = {
  R: { R: 80, SR: 18, SSR: 2 },
  SR: { R: 50, SR: 45, SSR: 5 },
  SSR: { R: 30, SR: 60, SSR: 10 }
};

export async function POST(req: Request) {
  try {
    const { userId, pool } = await req.json();
    
    if (!userId || !pool) {
      return NextResponse.json({ ok: false, error: '参数错误' }, { status: 400 });
    }

    // 验证卡池类型
    if (!['R', 'SR', 'SSR'].includes(pool)) {
      return NextResponse.json({ ok: false, error: '无效的卡池类型' }, { status: 400 });
    }

    // 获取用户数据
    const users = await readJson<any[]>(USERS_FILE, []);
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex === -1) {
      return NextResponse.json({ ok: false, error: '用户不存在' }, { status: 404 });
    }

    const user = users[userIndex];
    
    // 检查是否有足够的星轨召唤券
    const gachaTickets = user.gachaTickets || 0;
    if (gachaTickets < 1) {
      return NextResponse.json({ ok: false, error: '星轨召唤券不足' }, { status: 400 });
    }

    // 检查保底机制
    const gachaHistory = user.gachaHistory || [];
    const poolHistory = gachaHistory.filter((record: any) => record.pool === pool);
    const recentDraws = poolHistory.slice(-10); // 最近10次抽卡记录
    const hasSROrAbove = recentDraws.some((record: any) => record.rarity === 'SR' || record.rarity === 'SSR');
    
    let selectedRarity;
    if (!hasSROrAbove && recentDraws.length === 9) {
      // 保底机制：在同一个卡池，每累计召唤10次，若未获得SR或以上头衔，则第10次必出SR头衔
      selectedRarity = 'SR';
    } else {
      // 正常抽卡概率
      const rates = GACHA_RATES[pool as 'R' | 'SR' | 'SSR'];
      const rand = Math.random() * 100;
      if (rand < rates.R) {
        selectedRarity = 'R';
      } else if (rand < rates.R + rates.SR) {
        selectedRarity = 'SR';
      } else {
        selectedRarity = 'SSR';
      }
    }

    // 根据稀有度选择卡池
    let titlePool;
    switch (selectedRarity) {
      case 'R':
        titlePool = R_RANDOM_TITLES;
        break;
      case 'SR':
        titlePool = SR_RANDOM_TITLES;
        break;
      case 'SSR':
        titlePool = SSR_RANDOM_TITLES;
        break;
      default:
        return NextResponse.json({ ok: false, error: '无效的稀有度' }, { status: 400 });
    }

    // 过滤已解锁的头衔
    const unlockedTitles = user.unlockedTitles || [];
    const availableTitles = titlePool.filter(title => !unlockedTitles.includes(title.id));
    
    if (availableTitles.length === 0) {
      return NextResponse.json({ ok: false, error: '该卡池所有头衔已解锁' }, { status: 400 });
    }

    // 随机选择头衔
    const randomIndex = Math.floor(Math.random() * availableTitles.length);
    const selectedTitle = availableTitles[randomIndex];

    // 更新用户数据
    user.gachaTickets = gachaTickets - 1; // 消耗1张券
    user.unlockedTitles = [...unlockedTitles, selectedTitle.id];
    
    // 记录抽卡历史
    const newRecord = {
      pool,
      rarity: selectedRarity,
      titleId: selectedTitle.id,
      timestamp: new Date().toISOString()
    };
    user.gachaHistory = [...gachaHistory, newRecord];

    // 保存更新
    users[userIndex] = user;
    await writeJson(USERS_FILE, users);

    return NextResponse.json({
      ok: true,
      title: selectedTitle,
      rarity: selectedRarity,
      newGachaTickets: user.gachaTickets,
      newUnlockedTitles: user.unlockedTitles
    });

  } catch (error) {
    console.error('Gacha API error:', error);
    return NextResponse.json({ ok: false, error: '服务器错误' }, { status: 500 });
  }
}