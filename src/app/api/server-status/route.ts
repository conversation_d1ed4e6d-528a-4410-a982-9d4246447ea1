import { NextRequest, NextResponse } from 'next/server';
import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';

const DATA_DIR = join(process.cwd(), 'data');
const ORDERS_FILE = join(DATA_DIR, 'recharge_orders.json');

// 每月服务器续命目标（元）
const MONTHLY_GOAL = 30000;

interface RechargeOrder {
  id: string;
  userId: string;
  amount: number;
  amountCny: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  createdAt: string;
  completedAt?: string;
}

function loadOrders(): RechargeOrder[] {
  if (!existsSync(ORDERS_FILE)) {
    return [];
  }
  try {
    const data = readFileSync(ORDERS_FILE, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error loading orders:', error);
    return [];
  }
}

function getCurrentMonthRevenue(): number {
  const orders = loadOrders();
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth(); // 0-11
  
  // 计算当前月份的第一天和下个月的第一天
  const monthStart = new Date(currentYear, currentMonth, 1);
  const nextMonthStart = new Date(currentYear, currentMonth + 1, 1);
  
  let totalRevenue = 0;
  
  for (const order of orders) {
    if (order.status === 'COMPLETED' && order.completedAt) {
      const orderDate = new Date(order.completedAt);
      if (orderDate >= monthStart && orderDate < nextMonthStart) {
        totalRevenue += order.amountCny || order.amount || 0;
      }
    }
  }
  
  return totalRevenue;
}

export async function GET(request: NextRequest) {
  try {
    const currentRevenue = getCurrentMonthRevenue();
    const progressPercentage = Math.min(100, (currentRevenue / MONTHLY_GOAL) * 100);
    
    return NextResponse.json({
      currentRevenue: Math.round(currentRevenue * 100) / 100, // 保留两位小数
      monthlyGoal: MONTHLY_GOAL,
      progressPercentage: Math.round(progressPercentage * 10) / 10 // 保留一位小数
    });
  } catch (error) {
    console.error('Error in server-status API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}