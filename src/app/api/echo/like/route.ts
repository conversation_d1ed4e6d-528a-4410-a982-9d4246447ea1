import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const FILE = 'echo.json';
const LIKED_FILE = 'echo-liked-users.json';

type EchoItem = { id: string; type: 'idea'|'bug'; content: string; author?: string; createdAt: string; likes: number, imageUrl?: string };

export async function POST(req: Request){
  try{
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id')||'';
    const uid = req.headers.get('x-user-id') || 'guest';
    if (!id) return NextResponse.json({ ok:false, error:'missing id' }, { status: 400 });
    const items = await readJson<EchoItem[]>(FILE, []);
    const idx = items.findIndex(i=>i.id===id);
    if (idx<0) return NextResponse.json({ ok:false, error:'not found' }, { status: 404 });
    const liked = await readJson<Record<string,string[]>>(LIKED_FILE, {});
    const set = new Set(liked[id]||[]);
    if (set.has(uid)) return NextResponse.json({ ok:true, liked: true, item: items[idx] });
    set.add(uid);
    liked[id] = Array.from(set);
    items[idx].likes = (items[idx].likes||0)+1;
    await writeJson(LIKED_FILE, liked);
    await writeJson(FILE, items);
    return NextResponse.json({ ok:true, liked: true, item: items[idx] });
  }catch(e){
    return NextResponse.json({ ok:false, error:'server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request){
  try{
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id')||'';
    const uid = req.headers.get('x-user-id') || 'guest';
    if (!id) return NextResponse.json({ ok:false, error:'missing id' }, { status: 400 });
    const items = await readJson<EchoItem[]>(FILE, []);
    const idx = items.findIndex(i=>i.id===id);
    if (idx<0) return NextResponse.json({ ok:false, error:'not found' }, { status: 404 });
    const liked = await readJson<Record<string,string[]>>(LIKED_FILE, {});
    const set = new Set(liked[id]||[]);
    if (!set.has(uid)) return NextResponse.json({ ok:true, liked: false, item: items[idx] });
    set.delete(uid);
    liked[id] = Array.from(set);
    items[idx].likes = Math.max(0, (items[idx].likes||0)-1);
    await writeJson(LIKED_FILE, liked);
    await writeJson(FILE, items);
    return NextResponse.json({ ok:true, liked: false, item: items[idx] });
  }catch(e){
    return NextResponse.json({ ok:false, error:'server error' }, { status: 500 });
  }
} 