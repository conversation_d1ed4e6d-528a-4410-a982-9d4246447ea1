import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const FILE = 'echo.json';

type EchoItem = { id: string; type: 'idea'|'bug'; content: string; author?: string; createdAt: string; likes: number; imageUrl?: string };

export async function GET(req: Request){
  const { searchParams } = new URL(req.url);
  const type = (searchParams.get('type') as 'idea'|'bug'|null) || null;
  const items = await readJson<EchoItem[]>(FILE, []);
  const list = type ? items.filter(i=>i.type===type) : items;
  const sorted = list.slice().sort((a,b)=> new Date(b.createdAt).getTime()-new Date(a.createdAt).getTime());
  return NextResponse.json({ ok: true, items: sorted });
}

export async function POST(req: Request){
  try{
    const body = await req.json();
    const type = (body?.type==='bug'?'bug':'idea') as 'idea'|'bug';
    const content = String(body?.content||'').trim();
    const imageUrl = body?.imageUrl ? String(body.imageUrl).trim() : '';
    const author = body?.author ? String(body.author) : undefined;
    if (!content && !imageUrl) return NextResponse.json({ ok:false, error:'content or imageUrl required' }, { status: 400 });
    const items = await readJson<EchoItem[]>(FILE, []);
    const id = Date.now().toString(36)+Math.random().toString(36).slice(2,6);
    const it: EchoItem = { id, type, content, imageUrl: imageUrl || undefined, author, createdAt: new Date().toISOString(), likes: 0 };
    items.push(it);
    await writeJson(FILE, items);
    return NextResponse.json({ ok:true, item: it });
  }catch(e){
    return NextResponse.json({ ok:false, error:'server error' }, { status: 500 });
  }
}

export async function DELETE(req: Request){
  const token = process.env.ADMIN_TOKEN || '';
  const provided = req.headers.get('x-admin-token') || '';
  if (!token || provided!==token) return NextResponse.json({ ok:false, error:'unauthorized' }, { status: 401 });
  const { searchParams } = new URL(req.url);
  const id = searchParams.get('id')||'';
  if (!id) return NextResponse.json({ ok:false, error:'missing id' }, { status: 400 });
  const items = await readJson<EchoItem[]>(FILE, []);
  const left = items.filter(i=>i.id!==id);
  await writeJson(FILE, left);
  return NextResponse.json({ ok:true, removed: items.length-left.length });
} 