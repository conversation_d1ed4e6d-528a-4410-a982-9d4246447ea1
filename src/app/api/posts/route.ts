import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const POSTS_FILE = 'posts.json';

type Comment = { id: string; content: string; author?: string; createdAt: string; likes: number };

type Post = {
  id: string;
  content: string;
  imageUrl?: string;
  author?: string;
  createdAt: string;
  likes: number;
  comments?: Comment[];
};

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const sort = searchParams.get('sort') || 'time';
  const posts = await readJson<Post[]>(POSTS_FILE, []);
  const safe = posts.map(p => ({ ...p, comments: p.comments || [] }));
  const items = safe.sort((a: Post, b: Post) => {
    if (sort === 'likes') return (b.likes || 0) - (a.likes || 0);
    return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
  });
  return NextResponse.json({ ok: true, items });
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const headers = (req as any).headers as Headers | undefined;
    const userId = headers?.get?.('x-user-id') || 'guest';
    const content: string = (body?.content || '').toString().trim();
    const author: string | undefined = body?.author ? String(body.author) : userId;
    const imageUrl: string | undefined = body?.imageUrl ? String(body.imageUrl) : undefined;
    if (!content && !imageUrl) return NextResponse.json({ ok: false, error: 'content or imageUrl required' }, { status: 400 });

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const post: Post = {
      id: Date.now().toString(36) + Math.random().toString(36).slice(2,8),
      content,
      imageUrl,
      author,
      createdAt: new Date().toISOString(),
      likes: 0,
      comments: [],
    };
    posts.push(post);
    await writeJson(POSTS_FILE, posts);
    return NextResponse.json({ ok: true, item: post });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 