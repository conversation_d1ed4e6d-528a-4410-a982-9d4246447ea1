import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const POSTS_FILE = 'posts.json';

type Post = { id: string; content: string; imageUrl?: string; author?: string; createdAt: string; likes: number; comments?: any[] };

export async function DELETE(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const userId = req.headers.get('x-user-id') || 'guest';
    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const idx = posts.findIndex(p => p.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    const owner = posts[idx].author || 'guest';
    if (owner !== userId) return NextResponse.json({ ok: false, error: 'forbidden' }, { status: 403 });

    posts.splice(idx, 1);
    await writeJson(POSTS_FILE, posts);
    return NextResponse.json({ ok: true });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 