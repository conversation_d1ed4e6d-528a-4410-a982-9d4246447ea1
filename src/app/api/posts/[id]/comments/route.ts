import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const POSTS_FILE = 'posts.json';

type Comment = { id: string; content: string; author?: string; createdAt: string; likes: number };

type Post = { id: string; content: string; imageUrl?: string; author?: string; createdAt: string; likes: number; comments?: Comment[] };

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const body = await req.json();
    const headers = (req as any).headers as Headers | undefined;
    const userId = headers?.get?.('x-user-id') || 'guest';
    const content: string = (body?.content || '').toString().trim();
    const author: string | undefined = body?.author ? String(body.author) : userId;
    if (!content) return NextResponse.json({ ok: false, error: 'content required' }, { status: 400 });

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const idx = posts.findIndex(p => p.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    const c: Comment = { id: Date.now().toString(36) + Math.random().toString(36).slice(2,8), content, author, createdAt: new Date().toISOString(), likes: 0 };
    posts[idx].comments = posts[idx].comments || [];
    posts[idx].comments!.push(c);
    await writeJson(POSTS_FILE, posts);
    return NextResponse.json({ ok: true, item: c });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(req.url);
    const cid = searchParams.get('cid') || '';
    const userId = req.headers.get('x-user-id') || 'guest';
    if (!cid) return NextResponse.json({ ok: false, error: 'missing cid' }, { status: 400 });

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const idx = posts.findIndex(p => p.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'post not found' }, { status: 404 });

    const comments = posts[idx].comments || [];
    const cidx = comments.findIndex(c => c.id === cid);
    if (cidx === -1) return NextResponse.json({ ok: false, error: 'comment not found' }, { status: 404 });

    const comment = comments[cidx];
    const owner = comment.author || 'guest';
    if (owner !== userId) return NextResponse.json({ ok: false, error: 'forbidden' }, { status: 403 });

    comments.splice(cidx, 1);
    posts[idx].comments = comments;
    await writeJson(POSTS_FILE, posts);
    return NextResponse.json({ ok: true });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 