import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const POSTS_FILE = 'posts.json';
const LIKED_USERS_FILE = 'comments-liked-users.json';

type Comment = { id: string; content: string; author?: string; createdAt: string; likes: number };

type Post = { id: string; content: string; imageUrl?: string; author?: string; createdAt: string; likes: number; comments?: Comment[] };

export async function POST(req: Request, { params }: { params: Promise<{ id: string, cid: string }> }) {
  try {
    const { id, cid } = await params;
    const userId = req.headers.get('x-user-id') || 'guest';

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const pIdx = posts.findIndex(p => p.id === id);
    if (pIdx === -1) return NextResponse.json({ ok: false, error: 'post not found' }, { status: 404 });
    const cIdx = (posts[pIdx].comments || []).findIndex(c => c.id === cid);
    if (cIdx === -1) return NextResponse.json({ ok: false, error: 'comment not found' }, { status: 404 });

    const key = `${id}:${cid}`;
    const likedUsers = await readJson<Record<string, string[]>>(LIKED_USERS_FILE, {});
    const users = new Set(likedUsers[key] || []);
    if (users.has(userId)) {
      return NextResponse.json({ ok: true, item: posts[pIdx].comments![cIdx] });
    }

    users.add(userId);
    likedUsers[key] = Array.from(users);
    posts[pIdx].comments![cIdx].likes = (posts[pIdx].comments![cIdx].likes || 0) + 1;

    await writeJson(LIKED_USERS_FILE, likedUsers);
    await writeJson(POSTS_FILE, posts);

    return NextResponse.json({ ok: true, item: posts[pIdx].comments![cIdx] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 