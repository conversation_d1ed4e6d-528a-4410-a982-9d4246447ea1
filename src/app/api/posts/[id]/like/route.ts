import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const POSTS_FILE = 'posts.json';
const LIKED_USERS_FILE = 'posts-liked-users.json';

type Post = { id: string; content: string; imageUrl?: string; author?: string; createdAt: string; likes: number; comments?: any[] };

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const userId = req.headers.get('x-user-id') || 'guest';

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const idx = posts.findIndex((p: Post) => p.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    const likedUsers = await readJson<Record<string, string[]>>(LIKED_USERS_FILE, {});
    const users = new Set(likedUsers[id] || []);
    if (users.has(userId)) {
      return NextResponse.json({ ok: true, liked: true, item: posts[idx] });
    }

    users.add(userId);
    likedUsers[id] = Array.from(users);
    posts[idx].likes = (posts[idx].likes || 0) + 1;

    await writeJson(LIKED_USERS_FILE, likedUsers);
    await writeJson(POSTS_FILE, posts);

    return NextResponse.json({ ok: true, liked: true, item: posts[idx] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
}

export async function DELETE(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const userId = req.headers.get('x-user-id') || 'guest';

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const idx = posts.findIndex((p: Post) => p.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    const likedUsers = await readJson<Record<string, string[]>>(LIKED_USERS_FILE, {});
    const users = new Set(likedUsers[id] || []);
    if (!users.has(userId)) {
      return NextResponse.json({ ok: true, liked: false, item: posts[idx] });
    }

    users.delete(userId);
    likedUsers[id] = Array.from(users);
    posts[idx].likes = Math.max(0, (posts[idx].likes || 0) - 1);

    await writeJson(LIKED_USERS_FILE, likedUsers);
    await writeJson(POSTS_FILE, posts);

    return NextResponse.json({ ok: true, liked: false, item: posts[idx] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 