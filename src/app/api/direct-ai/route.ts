import { NextRequest, NextResponse } from 'next/server';
import { callAI } from '@/lib/ai-service';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

export async function POST(request: NextRequest) {
  console.log('🔧 === 直接测试AI调用 ===');
  
  try {
    const { prompt, provider = 'gemini' } = await request.json();
    
    console.log('📝 测试Prompt:', (prompt || '').substring(0, 200) + '...');
    console.log('🤖 使用AI服务:', provider);
    
    const startTime = Date.now();
    const result = await callAI(prompt, provider);
    const endTime = Date.now();
    
    console.log('✅ AI调用成功');
    console.log('📄 生成内容长度:', result.length);
    console.log('⏱️ 耗时:', endTime - startTime, 'ms');
    
    return NextResponse.json({
      success: true,
      result,
      length: result.length,
      duration: endTime - startTime,
      provider
    });
    
  } catch (error) {
    console.error('❌ AI调用失败:', error);
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    const stack = (error && typeof error === 'object' && 'stack' in error) ? (error as any).stack : undefined;
    return NextResponse.json({
      success: false,
      error: message,
      stack
    }, { status: 500 });
  }
}