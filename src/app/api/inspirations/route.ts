import { NextResponse } from 'next/server';
import { INSPIRATIONS, InspirationCategoryId } from '../../../lib/inspirations';

export async function GET(req: Request) {
	try{
		const { searchParams } = new URL(req.url);
		const cat = (searchParams.get('cat')||'') as InspirationCategoryId;
		if (!cat || !(cat in INSPIRATIONS)){
			return NextResponse.json({ ok: true, data: { categories: Object.keys(INSPIRATIONS), count: 0, items: [] } });
		}
		const items = INSPIRATIONS[cat];
		return NextResponse.json({ ok: true, data: { category: cat, count: items.length, items } });
	}catch(err:any){
		return NextResponse.json({ ok:false, error: err?.message||'internal error' }, { status: 500 });
	}
}