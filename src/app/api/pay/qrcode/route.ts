import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export async function GET() {
  try {
    const filePath = path.join(process.cwd(), 'zhifubao', 'alibaba_pay_2.jpg');
    const data = await fs.readFile(filePath);
    const ab = data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
    return new NextResponse(ab as any, { headers: { 'Content-Type': 'image/jpeg', 'Cache-Control': 'no-store, no-cache, must-revalidate, max-age=0' } });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message, hint: 'Ensure file exists at zhifubao/alibaba_pay_2.jpg' }, { status: 404 });
  }
} 