import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  console.log('🔍 === 直接检查环境变量状态 ===');
  
  const rawEnv = {
    GEMINI_API_KEY: process.env.GEMINI_API_KEY,
    KIMI_API_KEY: process.env.KIMI_API_KEY,
    DEEPSEEK_API_KEY: process.env.DEEPSEEK_API_KEY,
    NODE_ENV: process.env.NODE_ENV
  };
  
  console.log('原始环境变量:', rawEnv);
  
  const hasAnyKey = !!(process.env.GEMINI_API_KEY || process.env.KIMI_API_KEY || process.env.DEEPSEEK_API_KEY);
  
  console.log('是否有任何API密钥:', hasAnyKey);
  
  if (!hasAnyKey) {
    console.error('🚨 致命问题：运行时没有读取到任何API密钥！');
    return NextResponse.json({
      error: '环境变量未加载',
      problem: 'Next.js运行时无法读取.env.local文件',
      solution: '需要重启开发服务器或检查.env.local文件位置',
      rawEnv,
      hasAnyKey: false
    });
  }
  
  // 如果有API密钥，尝试最简单的调用
  if (process.env.KIMI_API_KEY) {
    console.log('🎯 尝试直接调用Kimi API...');
    
    try {
      const response = await fetch('https://api.moonshot.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.KIMI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'moonshot-v1-8k',
          messages: [{
            role: 'user',
            content: '写一句话：今天天气很好'
          }],
          max_tokens: 50,
        }),
      });
      
      if (response.ok) {
        const data = await response.json();
        const result = data.choices[0]?.message?.content || '无响应内容';
        
        console.log('✅ API调用成功！结果:', result);
        
        return NextResponse.json({
          success: true,
          message: 'API调用正常工作',
          result,
          hasAnyKey: true,
          apiKeyStatus: 'valid'
        });
      } else {
        const errorText = await response.text();
        console.error('❌ API调用失败:', response.status, errorText);
        
        return NextResponse.json({
          success: false,
          error: 'API调用失败',
          status: response.status,
          details: errorText,
          hasAnyKey: true,
          apiKeyStatus: 'invalid_or_quota_exceeded'
        });
      }
      
    } catch (error) {
      console.error('❌ 网络错误:', error);
      const details = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
      return NextResponse.json({
        success: false,
        error: '网络错误',
        details,
        hasAnyKey: true,
        apiKeyStatus: 'network_error'
      });
    }
  }
  
  return NextResponse.json({
    success: false,
    error: '没有配置Kimi API密钥',
    rawEnv,
    hasAnyKey
  });
}