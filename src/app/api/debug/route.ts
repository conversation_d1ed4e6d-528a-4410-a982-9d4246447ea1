import { NextRequest, NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

export async function GET(req: NextRequest) {
  const isProd = process.env.NODE_ENV === 'production';
  if (isProd) {
    const adminToken = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!adminToken || provided !== adminToken) {
      return NextResponse.json({ ok:false, error:'unauthorized' }, { status:401 });
    }
  }

  console.log('🔍 === 环境变量详细检查 ===');
  
  // 检查Node.js环境变量
  const envVars = {
    NODE_ENV: process.env.NODE_ENV,
    GEMINI_API_KEY: !!process.env.GEMINI_API_KEY,
    KIMI_API_KEY: !!process.env.KIMI_API_KEY,
    DEEPSEEK_API_KEY: !!process.env.DEEPSEEK_API_KEY,
  } as any;
  
  console.log('环境变量详情:', envVars);
  
  // 测试简单的API调用
  const testResults: any[] = [];
  
  // 测试Kimi API（仅非生产环境）
  if (process.env.KIMI_API_KEY && process.env.NODE_ENV !== 'production') {
    try {
      console.log('🎯 测试Kimi API...');
      const kimiResponse = await fetch('https://api.moonshot.cn/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.KIMI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'moonshot-v1-8k',
          messages: [{ role: 'user', content: '请简单回复：测试成功' }],
          temperature: 0.7,
          max_tokens: 50,
        }),
      });
      const kimiData = await kimiResponse.json();
      console.log('Kimi响应(仅非生产环境调用):', kimiData);
      testResults.push({ provider: 'kimi', success: kimiResponse.ok, status: kimiResponse.status });
    } catch (error) {
      console.error('Kimi测试失败:', error);
      const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
      testResults.push({ provider: 'kimi', success: false, error: message });
    }
  }
  
  return NextResponse.json({
    environment: envVars,
    hasValidKeys: {
      gemini: !!process.env.GEMINI_API_KEY,
      kimi: !!process.env.KIMI_API_KEY,
      deepseek: !!process.env.DEEPSEEK_API_KEY,
    },
    testResults,
    timestamp: new Date().toISOString()
  });
}

export async function POST(req: NextRequest){
  try{
    const body = await req.json().catch(()=>({}));
    const log = {
      id: Date.now().toString(36)+Math.random().toString(36).slice(2,8),
      when: new Date().toISOString(),
      ua: req.headers.get('user-agent')||'',
      ip: req.headers.get('x-forwarded-for')||'',
      href: body?.href || '',
      type: body?.type || 'unknown',
      message: body?.message || body?.reason || '',
      stack: body?.stack || '',
    };
    const items = await readJson<any[]>('client-errors.json', []);
    items.push(log);
    await writeJson('client-errors.json', items);
    console.warn('📨 ClientError:', log);
    return NextResponse.json({ ok:true });
  }catch(e:any){
    return NextResponse.json({ ok:false, error: e?.message||String(e) }, { status: 500 });
  }
}