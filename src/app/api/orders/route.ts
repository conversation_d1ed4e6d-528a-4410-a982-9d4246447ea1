import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { grantSubscription, planConfig } from '@/lib/auth/users';

const ORDERS_FILE = 'recharge_orders.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  payAmountCny?: number; // 建议支付金额（含尾数校验码）
  orderCode?: string;    // 校验码（备注建议）
  creditsToAdd: number; // 非订阅订单使用
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  // 新增：订阅订单
  subscriptionPlan?: 'writer'|'patron';
};

function allowedCreditsForAmount(amount: number): number[] {
  // 根据充值页面的实际套餐更新映射
  const map: Record<number, number[]> = {
    12: [0],    // 产粮·执笔者: 12元 -> 订阅制，0章节
    30: [0],    // 产粮·太太: 30元 -> 订阅制，0章节
    6: [0],     // 限时尝鲜价: 6元 -> 投喂，0章节
  };
  
  // 支持自定义金额投喂（大于0的任意金额）
  if (amount > 0) {
    return [0];
  }
  
  return map[amount] || [];
}

export async function GET() {
  const orders = await readJson<Order[]>(ORDERS_FILE, []);
  return NextResponse.json({ ok: true, items: orders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) });
}

function makeOrderCode(id: string): string {
  // 取 id 后3位 base36 转十进制再取 3 位，确保 000-999
  const tail = parseInt(id.slice(-3), 36);
  const n = (tail % 1000).toString().padStart(3, '0');
  return n;
}

function withTailAmount(base: number, id: string): number {
  // 尾数 0.10 - 0.99 基于 id 稳定映射
  const t = (parseInt(id.slice(-2), 36) % 90) + 10; // 10..99
  return Math.round((base + t / 100) * 100) / 100;
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const userId: string = String(body?.userId || 'guest');
    const amountCny = Number(body?.amountCny || 0);
    const creditsToAdd = Number(body?.creditsToAdd || 0);
    const subscriptionPlan = body?.subscriptionPlan as ('writer'|'patron'|undefined);

    if (!amountCny) return NextResponse.json({ ok: false, error: 'invalid amount' }, { status: 400 });

    const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const id = Date.now().toString(36) + Math.random().toString(36).slice(2,8);
    const orderCode = makeOrderCode(id);

    // 验证订单类型
    const allowed = allowedCreditsForAmount(amountCny);
    if (allowed.length === 0) {
      return NextResponse.json({ ok: false, error: 'unsupported amount' }, { status: 400 });
    }
    
    // 订阅卡 (creditsToAdd = 0)
    if (subscriptionPlan) {
      if (creditsToAdd !== 0) return NextResponse.json({ ok: false, error: 'subscription should have 0 credits' }, { status: 400 });
    } 
    // 投喂 (creditsToAdd = 0) 
    else if (allowed.includes(0)) {
      if (creditsToAdd !== 0) return NextResponse.json({ ok: false, error: 'donation should have 0 credits' }, { status: 400 });
    }
    // 常规充值 (creditsToAdd > 0)
    else {
      if (creditsToAdd <= 0) return NextResponse.json({ ok: false, error: 'invalid credits' }, { status: 400 });
      if (!allowed.includes(creditsToAdd)) {
        return NextResponse.json({ ok: false, error: 'amount-credits mismatch', expected: allowed }, { status: 400 });
      }
    }

    if (subscriptionPlan) {
      // 校验金额是否与订阅方案匹配（支持限时折扣价格）
      const priceMap: Record<'writer'|'patron', { original: number; discount: number }> = { 
        writer: { original: 12, discount: 6 },
        patron: { original: 30, discount: 30 }
      };
      const planPrices = priceMap[subscriptionPlan];
      if (planPrices && amountCny !== planPrices.original && amountCny !== planPrices.discount) {
        return NextResponse.json({ ok: false, error: 'amount-plan mismatch', expected: [planPrices.original, planPrices.discount] }, { status: 400 });
      }
      const order: Order = { id, userId, amountCny, payAmountCny: withTailAmount(amountCny, id), orderCode, creditsToAdd: 0, status: 'PENDING', createdAt: new Date().toISOString(), subscriptionPlan };
      orders.push(order);
      await writeJson(ORDERS_FILE, orders);
      return NextResponse.json({ ok: true, item: order });
    }

    // 常规充值或投喂
    const order: Order = { id, userId, amountCny, payAmountCny: withTailAmount(amountCny, id), orderCode, creditsToAdd, status: 'PENDING', createdAt: new Date().toISOString() };
    orders.push(order);
    await writeJson(ORDERS_FILE, orders);

    return NextResponse.json({ ok: true, item: order });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
}