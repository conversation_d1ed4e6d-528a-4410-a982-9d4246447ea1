import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const ORDERS_FILE = 'recharge_orders.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  payAmountCny?: number;
  orderCode?: string;
  creditsToAdd: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  subscriptionPlan?: 'month'|'quarter'|'year';
};

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const adminToken = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!adminToken || provided !== adminToken) {
      return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    }

    const p = await params;
    const id = p?.id || '';
    if (!id) return NextResponse.json({ ok: false, error: 'missing id' }, { status: 400 });

    const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const idx = orders.findIndex(o => o.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    orders[idx].status = 'FAILED';
    await writeJson(ORDERS_FILE, orders);

    return NextResponse.json({ ok: true, item: orders[idx] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 