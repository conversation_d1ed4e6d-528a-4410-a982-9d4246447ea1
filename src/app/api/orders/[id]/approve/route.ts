import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { addCredits, grantSubscription } from '@/lib/auth/users';

const ORDERS_FILE = 'recharge_orders.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  creditsToAdd: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  subscriptionPlan?: 'writer'|'patron';
  payAmountCny?: number;
  orderCode?: string;
  orderNumber?: string;
};

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const adminToken = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!adminToken || provided !== adminToken) {
      return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    }

    const p = await params;
    const id = p?.id || '';
    if (!id) return NextResponse.json({ ok: false, error: 'missing id' }, { status: 400 });

    const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const idx = orders.findIndex(o => o.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    const ord = orders[idx];
    if (!ord.paymentScreenshotUrl || !ord.orderNumber){
      return NextResponse.json({ ok:false, error:'需要同时提交“支付截图”和“订单号”后才可审核通过' }, { status: 400 });
    }

    orders[idx].status = 'COMPLETED';
    await writeJson(ORDERS_FILE, orders);

    const order = orders[idx];

    if (order.subscriptionPlan) {
      const updated = await grantSubscription(order.userId, order.subscriptionPlan);
      return NextResponse.json({ ok: true, item: order, subscription: order.subscriptionPlan, user: updated });
    }

    const updated = await addCredits(order.userId, order.creditsToAdd);
    return NextResponse.json({ ok: true, item: order, creditsAdded: order.creditsToAdd, user: updated });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 