import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const ORDERS_FILE = 'recharge_orders.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  creditsToAdd: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  payAmountCny?: number;
  orderCode?: string;
  orderNumber?: string;
};

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const p = await params;
    const id = p.id;
    const body = await req.json();
    const url: string = body?.url ? String(body.url) : '';
    const orderNumber: string = body?.orderNumber ? String(body.orderNumber) : '';
    if (!url && !orderNumber) return NextResponse.json({ ok: false, error: 'missing url or orderNumber' }, { status: 400 });

    const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const idx = orders.findIndex(o => o.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });

    if (url) orders[idx].paymentScreenshotUrl = url;
    if (orderNumber) orders[idx].orderNumber = orderNumber;
    await writeJson(ORDERS_FILE, orders);

    return NextResponse.json({ ok: true, item: orders[idx] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 