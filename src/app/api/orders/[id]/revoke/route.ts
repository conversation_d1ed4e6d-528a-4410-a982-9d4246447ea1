import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { getUserById, saveUsers } from '@/lib/auth/users';

const ORDERS_FILE = 'recharge_orders.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  payAmountCny?: number;
  orderCode?: string;
  creditsToAdd: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  subscriptionPlan?: 'month'|'quarter'|'year';
};

export async function POST(req: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const adminToken = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!adminToken || provided !== adminToken) {
      return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    }

    const p = await params;
    const id = p?.id || '';
    if (!id) return NextResponse.json({ ok: false, error: 'missing id' }, { status: 400 });

    const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const idx = orders.findIndex(o => o.id === id);
    if (idx === -1) return NextResponse.json({ ok: false, error: 'not found' }, { status: 404 });
    const order = orders[idx];
    if (order.status !== 'COMPLETED') return NextResponse.json({ ok: false, error: 'not completed' }, { status: 400 });

    // 回退用户额度/订阅
    const users = await (await import('@/lib/auth/users')).loadUsers();
    const ui = users.findIndex(u=>u.id===order.userId);
    if (ui === -1) return NextResponse.json({ ok: false, error: 'user not found' }, { status: 404 });

    const u = users[ui];
    if (order.subscriptionPlan) {
      // 仅回退尚未消耗的订阅 walletCredits，避免负数
      const wallet = u.subscription?.walletCredits || 0;
      if (wallet > 0) {
        const refund = Math.min(wallet, u.credits || 0);
        u.subscription!.walletCredits = Math.max(0, wallet - refund);
        u.credits = Math.max(0, (u.credits || 0) - refund);
      }
      // 同时强制结束订阅
      u.subscription = undefined;
    } else {
      // 普通充值：直接扣回未消费额度（尽量扣，不做负数）
      const refund = Math.min(order.creditsToAdd, u.credits || 0);
      u.credits = Math.max(0, (u.credits || 0) - refund);
    }

    // 更新订单状态
    orders[idx].status = 'FAILED';
    await writeJson(ORDERS_FILE, orders);
    await saveUsers(users);

    return NextResponse.json({ ok: true, item: orders[idx], user: u });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 