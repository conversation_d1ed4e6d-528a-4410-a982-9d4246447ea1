import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { ALL_TITLES } from '@/lib/titles/titles';

const USERS_FILE = 'users.json';

// GET - 获取用户头衔信息
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json({ ok: false, error: 'missing userId' }, { status: 400 });
    }
    
    const users = await readJson<any[]>(USERS_FILE, []);
    const user = users.find(u => u.id === userId);
    
    if (!user) {
      return NextResponse.json({ ok: false, error: '用户不存在' }, { status: 404 });
    }
    
    const unlockedTitles = user.unlockedTitles || [];
    const equippedTitles = user.equippedTitles || [];
    
    // 获取解锁的头衔详细信息
    const unlockedTitleDetails = ALL_TITLES.filter(title => 
      unlockedTitles.includes(title.id)
    );
    
    // 获取佩戴的头衔详细信息
    const equippedTitleDetails = ALL_TITLES.filter(title => 
      equippedTitles.includes(title.id)
    );
    
    return NextResponse.json({
      ok: true,
      unlockedTitles: unlockedTitleDetails,
      equippedTitles: equippedTitleDetails,
      allTitles: ALL_TITLES
    });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? 
      (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
}

// POST - 设置佩戴的头衔
export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { userId, equippedTitleIds } = body;
    
    if (!userId || !Array.isArray(equippedTitleIds)) {
      return NextResponse.json({ ok: false, error: 'invalid parameters' }, { status: 400 });
    }
    
    // 限制最多佩戴3个头衔
    if (equippedTitleIds.length > 3) {
      return NextResponse.json({ ok: false, error: '最多只能佩戴3个头衔' }, { status: 400 });
    }
    
    const users = await readJson<any[]>(USERS_FILE, []);
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) {
      return NextResponse.json({ ok: false, error: '用户不存在' }, { status: 404 });
    }
    
    const user = users[userIndex];
    const unlockedTitles = user.unlockedTitles || [];
    
    // 验证用户是否拥有这些头衔
    const invalidTitles = equippedTitleIds.filter(id => !unlockedTitles.includes(id));
    if (invalidTitles.length > 0) {
      return NextResponse.json({ 
        ok: false, 
        error: '包含未解锁的头衔', 
        invalidTitles 
      }, { status: 400 });
    }
    
    // 更新用户佩戴的头衔
    user.equippedTitles = equippedTitleIds;
    users[userIndex] = user;
    await writeJson(USERS_FILE, users);
    
    return NextResponse.json({ ok: true, message: '头衔设置成功' });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? 
      (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 