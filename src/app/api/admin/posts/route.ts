import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import fs from 'fs/promises';
import path from 'path';

const POSTS_FILE = 'posts.json';
const POSTS_LIKED_FILE = 'posts-liked-users.json';
const COMMENTS_LIKED_FILE = 'comments-liked-users.json';

interface Post { id: string; content: string; imageUrl?: string; author?: string; createdAt: string; likes: number; comments?: Array<{ id: string }>; }

function parseUploadNameFromUrl(url?: string): string | null {
  if (!url) return null;
  try {
    const u = new URL(url);
    if (u.pathname.endsWith('/api/uploads/file')) {
      const name = u.searchParams.get('name');
      return name || null;
    }
  } catch {}
  return null;
}

export async function DELETE(req: Request) {
  try {
    const token = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!token || provided !== token) {
      return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const authorFilter = searchParams.get('author') || '';

    const posts = await readJson<Post[]>(POSTS_FILE, []);
    const toDelete = authorFilter ? posts.filter(p => (p.author || 'guest') === authorFilter) : posts.slice();
    const keep = authorFilter ? posts.filter(p => (p.author || 'guest') !== authorFilter) : [];

    // Delete uploaded files referenced by posts to delete
    for (const p of toDelete) {
      const name = parseUploadNameFromUrl(p.imageUrl);
      if (name) {
        const fp = path.join(process.cwd(), 'data', 'uploads', name);
        try { await fs.unlink(fp); } catch {}
      }
    }

    // Clean likes maps for deleted posts/comments
    const postsLiked = await readJson<Record<string, string[]>>(POSTS_LIKED_FILE, {});
    const commentsLiked = await readJson<Record<string, string[]>>(COMMENTS_LIKED_FILE, {});

    for (const p of toDelete) {
      delete postsLiked[p.id];
      // remove all comment like entries starting with `${postId}:`
      Object.keys(commentsLiked).forEach(k => { if (k.startsWith(p.id + ':')) delete (commentsLiked as any)[k]; });
    }

    await writeJson(POSTS_LIKED_FILE, postsLiked);
    await writeJson(COMMENTS_LIKED_FILE, commentsLiked);

    // Write remaining posts
    await writeJson(POSTS_FILE, keep);

    return NextResponse.json({ ok: true, removed: toDelete.length, left: keep.length });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 