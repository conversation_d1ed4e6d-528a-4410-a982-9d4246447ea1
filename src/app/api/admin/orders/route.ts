import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import { addCredits } from '@/lib/auth/users';
import { checkAndGrantTitlesOnOrderComplete } from '@/lib/titles/auto-grant';

const ORDERS_FILE = 'recharge_orders.json';
const USERS_FILE = 'users.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  payAmountCny?: number;
  orderCode?: string;
  creditsToAdd: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  paymentScreenshotUrl?: string;
  createdAt: string;
  updatedAt?: string;
  subscriptionPlan?: 'week'|'month'|'quarter'|'year';
  title?: string;
  rejectReason?: string;
};

// 验证管理员权限
function isAdmin(req: Request): boolean {
  const authHeader = req.headers.get('authorization');
  const token = req.headers.get('x-admin-token') || new URL(req.url).searchParams.get('token');
  const adminToken = process.env.ADMIN_TOKEN || '';
  return Boolean(adminToken && (token === adminToken || authHeader === `Bearer ${adminToken}`));
}

// GET - 获取所有订单
export async function GET(req: Request) {
  if (!isAdmin(req)) {
    return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
  }
  
  try {
  const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const users = await readJson<any[]>(USERS_FILE, []);
    
    // enriched orders with user info
    const enrichedOrders = orders.map(order => {
      const user = users.find(u => u.id === order.userId);
      return {
        ...order,
        userName: user?.name || '未知用户',
        userEmail: user?.email || user?.phone || '未知联系方式'
      };
    });
    
    const items = enrichedOrders.sort((a,b)=> new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  return NextResponse.json({ ok: true, items });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
}

// PUT - 更新订单状态 (管理员确认/拒绝订单)
export async function PUT(req: Request) {
  if (!isAdmin(req)) {
    return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await req.json();
    const { orderId, status, rejectReason } = body;
    
    if (!orderId || !status || !['COMPLETED', 'FAILED'].includes(status)) {
      return NextResponse.json({ ok: false, error: '参数错误' }, { status: 400 });
    }
    
    const orders = await readJson<Order[]>(ORDERS_FILE, []);
    const orderIndex = orders.findIndex(o => o.id === orderId);
    
    if (orderIndex === -1) {
      return NextResponse.json({ ok: false, error: '订单不存在' }, { status: 404 });
    }
    
    const order = orders[orderIndex];
    
    if (order.status !== 'PENDING') {
      return NextResponse.json({ ok: false, error: '订单已处理' }, { status: 400 });
    }
    
    // 更新订单状态
    order.status = status;
    order.updatedAt = new Date().toISOString();
    if (rejectReason) order.rejectReason = rejectReason;
    
    // 如果订单确认成功，添加章节并授予头衔
    if (status === 'COMPLETED') {
      // 添加章节
      if (order.creditsToAdd > 0) {
        await addCredits(order.userId, order.creditsToAdd);
      }
      
      // 更新用户充值统计
      const users = await readJson<any[]>(USERS_FILE, []);
      const userIndex = users.findIndex(u => u.id === order.userId);
      if (userIndex !== -1) {
        users[userIndex].totalRecharge = (users[userIndex].totalRecharge || 0) + order.amountCny;
        if (order.title && order.title.includes('投喂')) {
          users[userIndex].donationCount = (users[userIndex].donationCount || 0) + 1;
        }
        await writeJson(USERS_FILE, users);
      }
      
      // 自动授予头衔
      await checkAndGrantTitlesOnOrderComplete(order.userId, {
        subscriptionPlan: order.subscriptionPlan,
        amountCny: order.amountCny,
        creditsToAdd: order.creditsToAdd
      });
    }
    
    orders[orderIndex] = order;
    await writeJson(ORDERS_FILE, orders);
    
    return NextResponse.json({ 
      ok: true, 
      message: status === 'COMPLETED' ? '订单确认成功，已发放奖励' : '订单已拒绝' 
    });
    
  } catch (error) {
    console.error('订单处理失败:', error);
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
} 