import { NextResponse } from 'next/server';
import { readJson } from '@/lib/persistence/jsondb';

const USERS_FILE = 'users.json';
const ORDERS_FILE = 'recharge_orders.json';

type Order = {
  id: string;
  userId: string;
  amountCny: number;
  payAmountCny?: number;
  status: 'PENDING'|'COMPLETED'|'FAILED';
  createdAt: string;
};

type User = { id:string; contactType:'phone'|'email'; createdAt:string; lastLoginAt?:string } & Record<string, any>;

export async function GET(req: Request){
  const adminToken = process.env.ADMIN_TOKEN || '';
  const provided = req.headers instanceof Headers ? req.headers.get('x-admin-token') : '';
  if (!adminToken || provided !== adminToken) {
    return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
  }
  const users = await readJson<User[]>(USERS_FILE, []);
  const orders = await readJson<Order[]>(ORDERS_FILE, []);

  const totalUsers = users.length;
  const emailUsers = users.filter(u=>u.contactType==='email').length;
  const phoneUsers = users.filter(u=>u.contactType==='phone').length;

  const now = Date.now();
  const sevenDaysAgo = now - 7*24*60*60*1000;
  const recent7dUsers = users.filter(u=> new Date(u.createdAt).getTime() >= sevenDaysAgo).length;
  const active7dUsers = users.filter(u=> {
    const t = u.lastLoginAt ? new Date(u.lastLoginAt).getTime() : 0;
    return t >= sevenDaysAgo;
  }).length;

  const totalOrders = orders.length;
  const pendingOrders = orders.filter(o => o.status === 'PENDING').length;
  const completedOrders = orders.filter(o => o.status === 'COMPLETED').length;
  const failedOrders = orders.filter(o => o.status === 'FAILED').length;
  const totalReceivedCny = orders
    .filter(o => o.status === 'COMPLETED')
    .reduce((sum, o) => {
      const val = Number.isFinite(o.payAmountCny as any) ? Number(o.payAmountCny) : Number(o.amountCny||0);
      return sum + (Number.isFinite(val) ? val : 0);
    }, 0);

  return NextResponse.json({ ok: true, data: { totalUsers, emailUsers, phoneUsers, recent7dUsers, active7dUsers, totalOrders, pendingOrders, completedOrders, failedOrders, totalReceivedCny } });
} 