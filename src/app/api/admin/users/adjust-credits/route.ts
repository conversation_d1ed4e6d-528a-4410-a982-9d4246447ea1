import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const USERS_FILE = 'users.json';

// 验证管理员权限
function isAdmin(req: Request): boolean {
  const authHeader = req.headers.get('authorization');
  const token = req.headers.get('x-admin-token') || new URL(req.url).searchParams.get('token');
  const adminToken = process.env.ADMIN_TOKEN || '';
  return Boolean(adminToken && (token === adminToken || authHeader === `Bearer ${adminToken}`));
}

// POST - 调整用户章节余额
export async function POST(req: Request) {
  if (!isAdmin(req)) {
    return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
  }
  
  try {
    const body = await req.json();
    const { userId, creditsChange } = body;
    
    if (!userId || creditsChange === undefined || typeof creditsChange !== 'number') {
      return NextResponse.json({ ok: false, error: '参数错误' }, { status: 400 });
    }
    
    const users = await readJson<any[]>(USERS_FILE, []);
    const userIndex = users.findIndex(u => u.id === userId);
    
    if (userIndex === -1) {
      return NextResponse.json({ ok: false, error: '用户不存在' }, { status: 404 });
    }
    
    const user = users[userIndex];
    const oldCredits = user.credits || 0;
    const newCredits = Math.max(0, oldCredits + creditsChange); // 不能为负数
    
    user.credits = newCredits;
    user.updatedAt = new Date().toISOString();
    
    users[userIndex] = user;
    await writeJson(USERS_FILE, users);
    
    return NextResponse.json({ 
      ok: true, 
      message: `章节余额从 ${oldCredits} 调整为 ${newCredits}`,
      oldCredits,
      newCredits
    });
    
  } catch (error) {
    console.error('调整章节失败:', error);
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
} 