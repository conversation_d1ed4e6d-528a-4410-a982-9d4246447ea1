import { NextResponse } from 'next/server';
import { adminAdjustCredits } from '@/lib/auth/users';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const LOG_FILE = 'audit-logs.json';

type AuditLog = {
  id: string;
  userId: string;
  delta: number;
  remark: string;
  createdAt: string;
};

export async function POST(req: Request) {
  try{
    const adminToken = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!adminToken || provided !== adminToken) {
      return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    }
    const body = await req.json();
    const userId = String(body?.userId||'');
    const delta = Number(body?.delta||0);
    const allowNegative = body?.allowNegative!==false; // default true
    const remark = String(body?.remark||'').trim();
    if (!userId || !Number.isFinite(delta)){
      return NextResponse.json({ ok: false, error: 'invalid params' }, { status: 400 });
    }
    if (!remark) return NextResponse.json({ ok:false, error:'remark required' }, { status: 400 });

    const u = await adminAdjustCredits(userId, delta, allowNegative);
    if (!u) return NextResponse.json({ ok: false, error: 'user not found' }, { status: 404 });

    const logs = await readJson<AuditLog[]>(LOG_FILE, []);
    logs.push({ id: Date.now().toString(36)+Math.random().toString(36).slice(2,8), userId, delta, remark, createdAt: new Date().toISOString() });
    await writeJson(LOG_FILE, logs);

    return NextResponse.json({ ok: true, user: u });
  } catch (e) {
    const msg = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ ok: false, error: msg }, { status: 500 });
  }
} 