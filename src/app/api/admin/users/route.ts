import { NextResponse } from 'next/server';
import { readJson } from '@/lib/persistence/jsondb';

const USERS_FILE = 'users.json';

// 验证管理员权限
function isAdmin(req: Request): boolean {
  const authHeader = req.headers.get('authorization');
  const token = req.headers.get('x-admin-token') || new URL(req.url).searchParams.get('token');
  const adminToken = 'admin-3f7c2a1e-2025'; // 使用固定令牌
  return Boolean(adminToken && (token === adminToken || authHeader === `Bearer ${adminToken}`));
}

// GET - 获取所有用户
export async function GET(req: Request) {
  if (!isAdmin(req)) {
    return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
  }
  
  try {
    const usersData = await readJson<Record<string, any>>(USERS_FILE, {});
    const users = Object.values(usersData);
    
    // 读取邀请数据统计每个用户的邀请人数
    const invitesData = await readJson<any[]>('invites.json', []);
    const inviteStats = invitesData.reduce((acc, invite) => {
      acc[invite.inviterId] = (acc[invite.inviterId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 增强用户数据
    const enhancedUsers = users.map(user => ({
      ...user,
      displayId: user.name ? `${user.name}_${user.id?.slice(-10) || 'guest'}` : user.id?.slice(-10) || 'guest',
      contact: user.email || user.phone || '未设置',
      contactType: user.email ? '邮箱' : user.phone ? '手机' : '未知',
      inviteCount: inviteStats[user.id] || 0,
      membershipType: user.membershipType || 'free',
      membershipExpiry: user.membershipExpiry
    }));
    
    // 按注册时间倒序排列
    const sortedUsers = enhancedUsers.sort((a, b) => 
      new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime()
    );
    
    return NextResponse.json({ ok: true, items: sortedUsers });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
} 