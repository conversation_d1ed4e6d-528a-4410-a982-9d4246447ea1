import { NextResponse } from 'next/server';
import { readJson } from '@/lib/persistence/jsondb';

const LOG_FILE = 'audit-logs.json';

type AuditLog = {
  id: string;
  userId: string;
  delta: number;
  remark: string;
  createdAt: string;
};

export async function GET(req: Request){
  const adminToken = process.env.ADMIN_TOKEN || '';
  const provided = req.headers instanceof Headers ? req.headers.get('x-admin-token') : '';
  if (!adminToken || provided !== adminToken) {
    return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
  }
  const { searchParams } = new URL(req.url);
  const userId = (searchParams.get('userId')||'').trim();
  const logs = await readJson<AuditLog[]>(LOG_FILE, []);
  const items = logs
    .filter(l => userId ? l.userId === userId : true)
    .sort((a,b)=> new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  return NextResponse.json({ ok: true, items });
} 