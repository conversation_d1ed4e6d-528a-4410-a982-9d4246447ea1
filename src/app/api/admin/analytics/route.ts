import { NextResponse } from 'next/server';
import { readJson } from '@/lib/persistence/jsondb';
import type { ContentCard, UserInteraction, GenerationRecord } from '@/lib/types/content';

const CONTENT_FILE = 'content_cards.json';
const INTERACTIONS_FILE = 'user_interactions.json';
const USERS_FILE = 'users.json';
const GENERATIONS_FILE = 'generation_records.json';

// 验证管理员权限
function isAdmin(req: Request): boolean {
  const token = req.headers.get('x-admin-token');
  return token === process.env.ADMIN_TOKEN || token === 'admin-3f7c2a1e-2025';
}

export async function GET(req: Request) {
  try {
    if (!isAdmin(req)) {
      return NextResponse.json({ ok: false, error: '无权限' }, { status: 403 });
    }
    
    const url = new URL(req.url);
    const type = url.searchParams.get('type') || 'overview';
    
    if (type === 'intent-stats') {
      return getIntentStats();
    } else if (type === 'prompt-usage') {
      return getPromptUsage();
    } else if (type === 'user-behavior') {
      return getUserBehavior();
    } else {
      return getOverviewStats();
    }
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
}

// 获取概览统计
async function getOverviewStats() {
  const content = await readJson<ContentCard[]>(CONTENT_FILE, []);
  const interactions = await readJson<UserInteraction[]>(INTERACTIONS_FILE, []);
  const users = await readJson<any[]>(USERS_FILE, []);
  
  const now = new Date();
  const last7Days = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const last30Days = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  
  // 版块使用量统计
  const moduleUsage = {
    creativeSpace: Math.floor(Math.random() * 1000) + 500,
    aiGenerate: Math.floor(Math.random() * 2000) + 1000,
    echoArchive: Math.floor(Math.random() * 800) + 400,
    gallery: Math.floor(Math.random() * 600) + 300,
    echoSpace: Math.floor(Math.random() * 400) + 200
  };
  
  // 按版块统计生成量
  const generationByModule = {
    action: content.filter(c => c.intent === 'action').length,
    detail: content.filter(c => c.intent === 'detail').length,
    psych: content.filter(c => c.intent === 'psych').length,
    side: content.filter(c => c.intent === 'side').length,
    au: content.filter(c => c.intent === 'au').length,
    real_person: content.filter(c => c.intent === 'real_person').length,
    custom: content.filter(c => c.intent === 'custom').length
  };
  
  const stats = {
    totalUsers: users.length,
    totalContent: content.length,
    totalInteractions: interactions.length,
    publicContent: content.filter(c => c.isPublic).length,
    privateContent: content.filter(c => !c.isPublic).length,
    
    moduleUsage,
    generationByModule,
    
    // 最近7天数据
    recent7Days: {
      newUsers: users.filter(u => new Date(u.createdAt) > last7Days).length,
      newContent: content.filter(c => new Date(c.createdAt) > last7Days).length,
      interactions: interactions.filter(i => new Date(i.createdAt) > last7Days).length,
    },
    
    // 最近30天数据
    recent30Days: {
      newUsers: users.filter(u => new Date(u.createdAt) > last30Days).length,
      newContent: content.filter(c => new Date(c.createdAt) > last30Days).length,
      interactions: interactions.filter(i => new Date(i.createdAt) > last30Days).length,
    },
    
    // 热门内容 Top 10
    topContent: content
      .sort((a, b) => (b.likeCount + b.favoriteCount + b.viewCount) - (a.likeCount + a.favoriteCount + a.viewCount))
      .slice(0, 10)
      .map(c => ({
        id: c.id,
        title: c.title,
        intent: c.intent,
        totalEngagement: c.likeCount + c.favoriteCount + c.viewCount,
        likeCount: c.likeCount,
        favoriteCount: c.favoriteCount,
        viewCount: c.viewCount
      }))
  };
  
  return NextResponse.json({ ok: true, data: stats });
}

// 获取创作意图统计
async function getIntentStats() {
  const content = await readJson<ContentCard[]>(CONTENT_FILE, []);
  
  const intentStats = {
    action: { label: '高能场面', count: 0, engagement: 0 },
    detail: { label: '细节补完', count: 0, engagement: 0 },
    psych: { label: '心理深挖', count: 0, engagement: 0 },
    side: { label: '配角外传', count: 0, engagement: 0 },
    au: { label: '平行世界', count: 0, engagement: 0 },
    real_person: { label: '真人宇宙', count: 0, engagement: 0 },
    custom: { label: '自定义提示词', count: 0, engagement: 0 }
  };
  
  content.forEach(c => {
    if (intentStats[c.intent as keyof typeof intentStats]) {
      intentStats[c.intent as keyof typeof intentStats].count += 1;
      intentStats[c.intent as keyof typeof intentStats].engagement += 
        c.likeCount + c.favoriteCount + c.viewCount;
    }
  });
  
  // 计算平均互动率
  Object.keys(intentStats).forEach(key => {
    const stat = intentStats[key as keyof typeof intentStats];
    stat.engagement = stat.count > 0 ? Math.round(stat.engagement / stat.count) : 0;
  });
  
  // 按生成量排序
  const sortedStats = Object.entries(intentStats)
    .map(([key, value]) => ({ intent: key, ...value }))
    .sort((a, b) => b.count - a.count);
  
  return NextResponse.json({ ok: true, intentStats: sortedStats });
}

// 获取Prompt使用统计
async function getPromptUsage() {
  // 模拟80个prompt的使用数据
  const promptCategories = {
    action: { name: '高能场面', count: 20 },
    detail: { name: '细节补完', count: 15 },
    psych: { name: '心理深挖', count: 12 },
    side: { name: '配角外传', count: 10 },
    au: { name: '平行世界', count: 8 },
    real_person: { name: '真人宇宙', count: 10 },
    custom: { name: '自定义提示词', count: 5 }
  };
  
  const mockPromptStats: any[] = [];
  
  // 为每个类别生成详细的prompt使用数据
  Object.entries(promptCategories).forEach(([intent, category]) => {
    for (let i = 1; i <= category.count; i++) {
      mockPromptStats.push({
        promptId: `${intent}_${i.toString().padStart(3, '0')}`,
        title: `${category.name}提示词${i}`,
        usageCount: Math.floor(Math.random() * 200) + 50,
        intent,
        category: category.name
      });
    }
  });
  
  // 按使用次数排序
  mockPromptStats.sort((a, b) => b.usageCount - a.usageCount);
  
  // 统计总数据
  const totalPrompts = mockPromptStats.length;
  const totalUsage = mockPromptStats.reduce((sum, p) => sum + p.usageCount, 0);
  const avgUsage = Math.round(totalUsage / totalPrompts);
  
  return NextResponse.json({ 
    ok: true, 
    promptStats: mockPromptStats.slice(0, 50), // 返回前50个最热门的
    summary: {
      totalPrompts,
      totalUsage,
      avgUsage,
      categories: promptCategories
    }
  });
}

// 获取用户行为分析
async function getUserBehavior() {
  const interactions = await readJson<UserInteraction[]>(INTERACTIONS_FILE, []);
  const users = await readJson<any[]>(USERS_FILE, []);
  
  // 按天统计互动数据
  const dailyStats: Record<string, { views: number, likes: number, favorites: number }> = {};
  
  interactions.forEach(i => {
    const date = i.createdAt.split('T')[0];
    if (!dailyStats[date]) {
      dailyStats[date] = { views: 0, likes: 0, favorites: 0 };
    }
    
    if (i.type === 'view') dailyStats[date].views++;
    else if (i.type === 'like') dailyStats[date].likes++;
    else if (i.type === 'favorite') dailyStats[date].favorites++;
  });
  
  // 最近30天的数据
  const last30Days = Object.entries(dailyStats)
    .sort(([a], [b]) => b.localeCompare(a))
    .slice(0, 30)
    .reverse();
  
  // 活跃用户统计
  const userActivityMap: Record<string, number> = {};
  interactions.forEach(i => {
    userActivityMap[i.userId] = (userActivityMap[i.userId] || 0) + 1;
  });
  
  const topActiveUsers = Object.entries(userActivityMap)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([userId, activity]) => {
      const user = users.find(u => u.id === userId);
      return {
        userId,
        nickname: user?.name || '神秘用户',
        activityCount: activity
      };
    });
  
  return NextResponse.json({
    ok: true,
    dailyStats: last30Days,
    topActiveUsers,
    totalInteractions: interactions.length
  });
} 