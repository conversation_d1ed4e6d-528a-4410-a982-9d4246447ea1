import { NextResponse } from 'next/server';

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { token } = body;
    
    // 验证管理员令牌
    const validToken = 'admin-3f7c2a1e-2025';
    
    if (token !== validToken) {
      return NextResponse.json({ 
        success: false, 
        error: '管理员令牌无效' 
      }, { status: 401 });
    }
    
    // 生成会话令牌
    const sessionToken = 'admin-session-' + Date.now();
    
    return NextResponse.json({
      success: true,
      sessionToken,
      message: '管理员登录成功'
    });
    
  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json({ 
      success: false, 
      error: '服务器错误' 
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json({ 
    message: 'Admin login API', 
    method: 'POST',
    required: ['token']
  });
} 