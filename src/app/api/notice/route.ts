import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const FILE = 'notices.json';

type Notice = { id: string; title: string; content: string; createdAt: string; pinned?: boolean };

export async function GET() {
  const items = await readJson<Notice[]>(FILE, []);
  const sorted = items.slice().sort((a,b) => (b.pinned?1:0) - (a.pinned?1:0) || new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  return NextResponse.json({ ok: true, items: sorted });
}

export async function POST(req: Request) {
  try {
    const token = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!token || provided !== token) return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    const body = await req.json();
    const title = String(body?.title || '').trim();
    const content = String(body?.content || '').trim();
    const pinned = !!body?.pinned;
    if (!title || !content) return NextResponse.json({ ok: false, error: 'invalid' }, { status: 400 });
    const items = await readJson<Notice[]>(FILE, []);
    const id = Date.now().toString(36) + Math.random().toString(36).slice(2,6);
    const n: Notice = { id, title, content, pinned, createdAt: new Date().toISOString() };
    items.push(n);
    await writeJson(FILE, items);
    return NextResponse.json({ ok: true, item: n });
  } catch (e) {
    return NextResponse.json({ ok: false, error: 'server error' }, { status: 500 });
  }
}

export async function PUT(req: Request) {
  try{
    const token = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!token || provided !== token) return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    const body = await req.json();
    const id = String(body?.id||'');
    const title = String(body?.title||'').trim();
    const content = String(body?.content||'').trim();
    const pinned = !!body?.pinned;
    if (!id) return NextResponse.json({ ok:false, error:'missing id' }, { status:400 });
    const items = await readJson<Notice[]>(FILE, []);
    const idx = items.findIndex(i=>i.id===id);
    if (idx<0) return NextResponse.json({ ok:false, error:'not found' }, { status:404 });
    if (title) items[idx].title = title;
    if (content) items[idx].content = content;
    items[idx].pinned = pinned;
    await writeJson(FILE, items);
    return NextResponse.json({ ok:true, item: items[idx] });
  }catch(e){
    return NextResponse.json({ ok:false, error:'server error' }, { status:500 });
  }
}

export async function DELETE(req: Request) {
  try{
    const token = process.env.ADMIN_TOKEN || '';
    const provided = req.headers.get('x-admin-token') || '';
    if (!token || provided !== token) return NextResponse.json({ ok: false, error: 'unauthorized' }, { status: 401 });
    const items: Notice[] = [];
    await writeJson(FILE, items);
    return NextResponse.json({ ok:true });
  }catch(e){
    return NextResponse.json({ ok:false, error:'server error' }, { status:500 });
  }
} 