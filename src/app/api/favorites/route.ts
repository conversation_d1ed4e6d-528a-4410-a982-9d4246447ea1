import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const FILE = 'favorites.json';

type Folder = { id: string; name: string; createdAt: string };
type FavItem = { id: string; folderId: string; title: string; content: string; createdAt: string };

type Store = Record<string, { folders: Folder[]; items: FavItem[] }>; // key: userId

async function load(): Promise<Store> { return readJson<Store>(FILE, {}); }
async function save(data: Store) { await writeJson(FILE, data); }

export async function GET(req: Request) {
  const userId = req.headers.get('x-user-id') || '';
  if (!userId) return NextResponse.json({ ok:false, error:'missing user id' }, { status:400 });
  const db = await load();
  if (!db[userId]) db[userId] = { folders: [], items: [] };
  // ensure default folder exists for visibility on first visit
  if (db[userId].folders.length === 0) {
    db[userId].folders.push({ id: 'f_def_'+Date.now().toString(36), name: '默认', createdAt: new Date().toISOString() });
    await save(db);
  }
  const u = db[userId];
  return NextResponse.json({ ok:true, folders: u.folders, items: u.items });
}

export async function POST(req: Request) {
  try{
    const userId = req.headers.get('x-user-id') || '';
    if (!userId) return NextResponse.json({ ok:false, error:'missing user id' }, { status:400 });
    const body = await req.json();
    const kind = String(body?.type||'');
    const db = await load();
    if (!db[userId]) db[userId] = { folders: [], items: [] };

    if (kind === 'folder') {
      const name = String(body?.name||'').trim() || '未命名';
      const id = 'f_' + Date.now().toString(36) + Math.random().toString(36).slice(2,6);
      const folder: Folder = { id, name, createdAt: new Date().toISOString() };
      db[userId].folders.push(folder);
      await save(db);
      return NextResponse.json({ ok:true, folder });
    }
    if (kind === 'item') {
      const title = String(body?.title||'').trim().slice(0,60) || '未命名收藏';
      const content = String(body?.content||'');
      let folderId = String(body?.folderId||'');
      if (!folderId) {
        // ensure default folder
        let def = db[userId].folders.find(f => f.name === '默认');
        if (!def) {
          def = { id: 'f_def_'+Date.now().toString(36), name: '默认', createdAt: new Date().toISOString() };
          db[userId].folders.push(def);
        }
        folderId = def.id;
      }
      const id = 'i_' + Date.now().toString(36) + Math.random().toString(36).slice(2,6);
      const item: FavItem = { id, folderId, title, content, createdAt: new Date().toISOString() };
      db[userId].items.push(item);
      await save(db);
      return NextResponse.json({ ok:true, item });
    }
    return NextResponse.json({ ok:false, error:'invalid type' }, { status:400 });
  }catch(e:any){
    const msg = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ ok:false, error: msg }, { status:500 });
  }
}

export async function PUT(req: Request) {
  try{
    const userId = req.headers.get('x-user-id') || '';
    if (!userId) return NextResponse.json({ ok:false, error:'missing user id' }, { status:400 });
    const body = await req.json();
    const kind = String(body?.type||'');
    const db = await load();
    if (!db[userId]) db[userId] = { folders: [], items: [] };
    if (kind === 'folder') {
      const id = String(body?.id||'');
      const name = String(body?.name||'').trim();
      const f = db[userId].folders.find(x=>x.id===id);
      if (!f) return NextResponse.json({ ok:false, error:'not found' }, { status:404 });
      if (name) f.name = name;
      await save(db);
      return NextResponse.json({ ok:true, folder: f });
    }
    if (kind === 'item') {
      const id = String(body?.id||'');
      const folderId = String(body?.folderId||'');
      const it = db[userId].items.find(x=>x.id===id);
      if (!it) return NextResponse.json({ ok:false, error:'not found' }, { status:404 });
      if (folderId) it.folderId = folderId;
      await save(db);
      return NextResponse.json({ ok:true, item: it });
    }
    return NextResponse.json({ ok:false, error:'invalid type' }, { status:400 });
  }catch(e:any){
    const msg = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ ok:false, error: msg }, { status:500 });
  }
}

export async function DELETE(req: Request) {
  try{
    const userId = req.headers.get('x-user-id') || '';
    if (!userId) return NextResponse.json({ ok:false, error:'missing user id' }, { status:400 });
    const body = await req.json();
    const id = String(body?.id||'');
    const kind = String(body?.type||'');
    const db = await load();
    if (!db[userId]) db[userId] = { folders: [], items: [] };
    if (kind === 'folder') {
      db[userId].folders = db[userId].folders.filter(f => f.id !== id);
      db[userId].items = db[userId].items.filter(it => it.folderId !== id);
      await save(db);
      return NextResponse.json({ ok:true });
    }
    if (kind === 'item') {
      db[userId].items = db[userId].items.filter(it => it.id !== id);
      await save(db);
      return NextResponse.json({ ok:true });
    }
    return NextResponse.json({ ok:false, error:'invalid type' }, { status:400 });
  }catch(e:any){
    const msg = (e && typeof e==='object' && 'message' in e) ? (e as any).message : String(e);
    return NextResponse.json({ ok:false, error: msg }, { status:500 });
  }
} 