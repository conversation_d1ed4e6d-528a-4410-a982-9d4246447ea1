import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

function getContentType(name: string) {
  const ext = name.toLowerCase().slice(name.lastIndexOf('.'));
  if (ext === '.png') return 'image/png';
  if (ext === '.jpg' || ext === '.jpeg') return 'image/jpeg';
  if (ext === '.webp') return 'image/webp';
  if (ext === '.gif') return 'image/gif';
  return 'application/octet-stream';
}

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const name = searchParams.get('name');
    if (!name) return NextResponse.json({ ok: false, error: 'missing name' }, { status: 400 });
    const filePath = path.join(process.cwd(), 'data', 'uploads', name);
    const data = await fs.readFile(filePath);
    const ab = data.buffer.slice(data.byteOffset, data.byteOffset + data.byteLength);
    return new NextResponse(ab as any, { headers: { 'Content-Type': getContentType(name), 'Cache-Control': 'no-store, no-cache, must-revalidate' } });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 404 });
  }
}

export async function POST(req: Request) {
  try {
    const form = await req.formData();
    const file = form.get('file') as File | null;
    if (!file) return NextResponse.json({ ok: false, error: 'missing file' }, { status: 400 });

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const uploadDir = path.join(process.cwd(), 'data', 'uploads');
    await fs.mkdir(uploadDir, { recursive: true });

    const origName = (file as any).name || 'upload';
    const ext = (() => {
      const lower = String(origName || '').toLowerCase();
      const dot = lower.lastIndexOf('.');
      if (dot >= 0) return lower.slice(dot);
      const type = (file as any).type || '';
      if (type.includes('png')) return '.png';
      if (type.includes('jpeg') || type.includes('jpg')) return '.jpg';
      if (type.includes('webp')) return '.webp';
      if (type.includes('gif')) return '.gif';
      return '.bin';
    })();

    const basename = `${Date.now()}_${Math.random().toString(36).slice(2, 8)}${ext}`;
    const savePath = path.join(uploadDir, basename);
    await fs.writeFile(savePath, buffer);

    const fileUrl = `/api/uploads/file?name=${encodeURIComponent(basename)}`;
    return NextResponse.json({ ok: true, url: fileUrl, name: basename });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 