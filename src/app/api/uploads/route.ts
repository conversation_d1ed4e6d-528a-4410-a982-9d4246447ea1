import { NextResponse } from 'next/server';
import fs from 'fs/promises';
import path from 'path';

function getOrigin(req: Request) {
  const host = req.headers.get('x-forwarded-host') || req.headers.get('host') || 'localhost:3000';
  const proto = req.headers.get('x-forwarded-proto') || 'http';
  return `${proto}://${host}`;
}

export async function POST(req: Request) {
  try {
    const form = await req.formData();
    const file = form.get('file') as File | null;
    if (!file) return NextResponse.json({ ok: false, error: 'file required' }, { status: 400 });

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    const ext = (file.name.match(/\.[a-zA-Z0-9]+$/)?.[0] || '').toLowerCase();
    const safeExt = ['.jpg','.jpeg','.png','.webp','.gif'].includes(ext) ? ext : '.jpg';
    const fileName = `${Date.now()}-${Math.random().toString(36).slice(2,8)}${safeExt}`;

    const dir = path.join(process.cwd(), 'data', 'uploads');
    await fs.mkdir(dir, { recursive: true });
    const outPath = path.join(dir, fileName);
    await fs.writeFile(outPath, buffer);

    const origin = getOrigin(req);
    const url = `${origin}/api/uploads/file?name=${encodeURIComponent(fileName)}`;

    return NextResponse.json({ ok: true, url, name: fileName });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 