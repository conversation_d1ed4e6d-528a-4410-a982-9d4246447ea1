import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

interface ContentCard {
  id: string;
  userId: string;
  username: string;
  userTitles: string[];
  title: string;
  content: string;
  category: string;
  tags: string[];
  likeCount: number;
  favoriteCount: number;
  commentCount: number;
  viewCount: number;
  createdAt: string;
}

interface UserInteraction {
  userId: string;
  contentId: string;
  action: 'like' | 'favorite' | 'view';
  createdAt: string;
}

const CONTENT_FILE = 'contents.json';
const INTERACTIONS_FILE = 'interactions.json';

export async function POST(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await req.json();
    const { userId, action } = body;

    if (!userId || !action || !id) {
      return NextResponse.json({ ok: false, error: 'Missing required fields' }, { status: 400 });
    }

    // 读取内容数据
    const contents = await readJson<Record<string, ContentCard>>(CONTENT_FILE, {});
    const content = contents[id];

    if (!content) {
      return NextResponse.json({ ok: false, error: 'Content not found' }, { status: 404 });
    }

    // 读取用户互动数据
    const interactions = await readJson<UserInteraction[]>(INTERACTIONS_FILE, []);
    
    // 查找现有的互动记录
    const existingInteraction = interactions.find(
      i => i.userId === userId && i.contentId === id && i.action === action
    );

    let updatedContent = { ...content };
    let userState = {
      liked: interactions.some(i => i.userId === userId && i.contentId === id && i.action === 'like'),
      favorited: interactions.some(i => i.userId === userId && i.contentId === id && i.action === 'favorite')
    };

    if (action === 'view') {
      // 浏览量增加（不重复计算同一用户的浏览）
      if (!existingInteraction) {
        updatedContent.viewCount = (updatedContent.viewCount || 0) + 1;
        interactions.push({
          userId,
          contentId: id,
          action: 'view',
          createdAt: new Date().toISOString()
        });
      }
    } else if (action === 'like') {
      if (existingInteraction) {
        // 取消点赞
        updatedContent.likeCount = Math.max(0, (updatedContent.likeCount || 0) - 1);
        const index = interactions.findIndex(i => i.userId === userId && i.contentId === id && i.action === 'like');
        if (index > -1) interactions.splice(index, 1);
        userState.liked = false;
      } else {
        // 添加点赞
        updatedContent.likeCount = (updatedContent.likeCount || 0) + 1;
        interactions.push({
          userId,
          contentId: id,
          action: 'like',
          createdAt: new Date().toISOString()
        });
        userState.liked = true;
      }
    } else if (action === 'favorite') {
      if (existingInteraction) {
        // 取消收藏
        updatedContent.favoriteCount = Math.max(0, (updatedContent.favoriteCount || 0) - 1);
        const index = interactions.findIndex(i => i.userId === userId && i.contentId === id && i.action === 'favorite');
        if (index > -1) interactions.splice(index, 1);
        userState.favorited = false;
      } else {
        // 添加收藏
        updatedContent.favoriteCount = (updatedContent.favoriteCount || 0) + 1;
        interactions.push({
          userId,
          contentId: id,
          action: 'favorite',
          createdAt: new Date().toISOString()
        });
        userState.favorited = true;
      }
    }

    // 保存更新后的数据
    contents[id] = updatedContent;
    await writeJson(CONTENT_FILE, contents);
    await writeJson(INTERACTIONS_FILE, interactions);

    return NextResponse.json({
      ok: true,
      card: updatedContent,
      userState,
      message: `${action} operation completed`
    });

  } catch (error) {
    console.error('Content interaction error:', error);
    return NextResponse.json(
      { ok: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 