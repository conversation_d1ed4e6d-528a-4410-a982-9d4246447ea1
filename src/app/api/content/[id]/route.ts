import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import type { ContentCard } from '@/lib/types/content';

const CONTENT_FILE = 'content_cards.json';

// GET - 获取特定内容详情
export async function GET(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const contentId = params.id;
    
    const content = await readJson<ContentCard[]>(CONTENT_FILE, []);
    const card = content.find(c => c.id === contentId);
    
    if (!card) {
      return NextResponse.json({ ok: false, error: '内容不存在' }, { status: 404 });
    }
    
    // 增加浏览量
    card.viewCount += 1;
    card.updatedAt = new Date().toISOString();
    
    const cardIndex = content.findIndex(c => c.id === contentId);
    content[cardIndex] = card;
    await writeJson(CONTENT_FILE, content);
    
    return NextResponse.json({ ok: true, item: card });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
}

// PUT - 编辑内容
export async function PUT(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const contentId = params.id;
    const body = await req.json();
    const { userId, title, summary, isPublic } = body;
    
    const content = await readJson<ContentCard[]>(CONTENT_FILE, []);
    const cardIndex = content.findIndex(c => c.id === contentId);
    
    if (cardIndex === -1) {
      return NextResponse.json({ ok: false, error: '内容不存在' }, { status: 404 });
    }
    
    const card = content[cardIndex];
    
    // 验证是否是作者本人
    if (card.userId !== userId) {
      return NextResponse.json({ ok: false, error: '无权限编辑' }, { status: 403 });
    }
    
    // 更新内容
    if (title !== undefined) card.title = title.trim();
    if (summary !== undefined) card.summary = summary.trim();
    if (isPublic !== undefined) card.isPublic = isPublic;
    card.updatedAt = new Date().toISOString();
    
    content[cardIndex] = card;
    await writeJson(CONTENT_FILE, content);
    
    return NextResponse.json({ ok: true, item: card });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
}

// DELETE - 删除内容
export async function DELETE(
  req: Request,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const params = await context.params;
    const contentId = params.id;
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');
    
    if (!userId) {
      return NextResponse.json({ ok: false, error: '缺少用户ID' }, { status: 400 });
    }
    
    const content = await readJson<ContentCard[]>(CONTENT_FILE, []);
    const cardIndex = content.findIndex(c => c.id === contentId);
    
    if (cardIndex === -1) {
      return NextResponse.json({ ok: false, error: '内容不存在' }, { status: 404 });
    }
    
    const card = content[cardIndex];
    
    // 验证是否是作者本人
    if (card.userId !== userId) {
      return NextResponse.json({ ok: false, error: '无权限删除' }, { status: 403 });
    }
    
    // 删除内容
    content.splice(cardIndex, 1);
    await writeJson(CONTENT_FILE, content);
    
    return NextResponse.json({ ok: true, message: '删除成功' });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
} 