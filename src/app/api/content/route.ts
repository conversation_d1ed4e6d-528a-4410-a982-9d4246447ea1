import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';
import type { ContentCard, UserInteraction } from '@/lib/types/content';

const CONTENT_FILE = 'content_cards.json';
const INTERACTIONS_FILE = 'user_interactions.json';
const USERS_FILE = 'users.json';

// 意图标签映射
const INTENT_TAGS: Record<string, string> = {
  action: '#高能场面',
  detail: '#细节补完', 
  psych: '#心理深挖',
  side: '#配角外传',
  au: '#平行世界',
  real_person: '#真人宇宙',
  custom: '#自定义提示词',
  chat: '#交流区'
};

// 生成内容摘要
function generateSummary(content: string): string {
  const cleanContent = content.replace(/[#*\-\n]/g, ' ').trim();
  return cleanContent.length > 100 ? cleanContent.slice(0, 100) + '...' : cleanContent;
}

// GET - 获取内容卡片列表
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const userId = url.searchParams.get('userId');
    const type = url.searchParams.get('type') || 'public'; // public, user, favorites
    const intent = url.searchParams.get('intent'); // 按意图过滤
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '12');
    
    const content = await readJson<ContentCard[]>(CONTENT_FILE, []);
    const users = await readJson<any[]>(USERS_FILE, []);
    const interactions = await readJson<UserInteraction[]>(INTERACTIONS_FILE, []);
    
    let filteredContent = content;
    
    // 根据类型过滤
    if (type === 'public') {
      filteredContent = content.filter(c => c.isPublic);
    } else if (type === 'user' && userId) {
      filteredContent = content.filter(c => c.userId === userId);
    } else if (type === 'favorites' && userId) {
      const favoriteIds = interactions
        .filter(i => i.userId === userId && i.type === 'favorite')
        .map(i => i.contentId);
      filteredContent = content.filter(c => favoriteIds.includes(c.id));
    }
    
    // 按意图过滤
    if (intent) {
      filteredContent = filteredContent.filter(c => c.intent === intent);
    }
    
    // 排序（按创建时间倒序）
    filteredContent.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    
    // 分页
    const startIndex = (page - 1) * limit;
    const paginatedContent = filteredContent.slice(startIndex, startIndex + limit);
    
    // 增强数据（添加用户信息和互动状态）
    const enhancedContent = paginatedContent.map(card => {
      const user = users.find(u => u.id === card.userId);
      const userLiked = userId ? interactions.some(i => 
        i.userId === userId && i.contentId === card.id && i.type === 'like'
      ) : false;
      const userFavorited = userId ? interactions.some(i => 
        i.userId === userId && i.contentId === card.id && i.type === 'favorite'
      ) : false;
      
      return {
        ...card,
        author: {
          nickname: user?.name || '神秘作者',
          avatar: user?.avatar || null,
          id: user?.displayId || user?.id,
          titles: user?.equippedTitles || []
        },
        userState: {
          liked: userLiked,
          favorited: userFavorited
        }
      };
    });
    
    return NextResponse.json({
      ok: true,
      items: enhancedContent,
      pagination: {
        page,
        limit,
        total: filteredContent.length,
        totalPages: Math.ceil(filteredContent.length / limit)
      }
    });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
}

// POST - 发布新内容
export async function POST(req: Request) {
  try {
    const body = await req.json();
    const { userId, title, content, intent, universe, isPublic = true } = body;
    
    if (!userId || !title || !content || !intent) {
      return NextResponse.json({ ok: false, error: '缺少必要参数' }, { status: 400 });
    }
    
    const contentList = await readJson<ContentCard[]>(CONTENT_FILE, []);
    const users = await readJson<any[]>(USERS_FILE, []);
    const user = users.find(u => u.id === userId);
    
    const newCard: ContentCard = {
      id: Date.now().toString(36) + Math.random().toString(36).slice(2),
      userId,
      title: title.trim(),
      content: content.trim(),
      summary: generateSummary(content),
      intent,
      universe: universe || 'novel',
      isPublic,
      tags: [INTENT_TAGS[intent] || '#未知分类'],
      likeCount: 0,
      commentCount: 0,
      favoriteCount: 0,
      viewCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: {
        nickname: user?.name || '神秘作者',
        avatar: user?.avatar || null,
        id: user?.displayId || user?.id,
        titles: user?.equippedTitles || []
      }
    };
    
    contentList.push(newCard);
    await writeJson(CONTENT_FILE, contentList);
    
    return NextResponse.json({ ok: true, item: newCard });
  } catch (error) {
    return NextResponse.json({ ok: false, error: String(error) }, { status: 500 });
  }
} 