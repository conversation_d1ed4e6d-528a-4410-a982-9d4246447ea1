export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';
import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/database';
import { generateContent } from '@/lib/ai-service';
import { generateHighQualityContent } from '@/lib/enhanced-ai-service';
import { semanticSearch, generateFromSearch } from '@/lib/novel-search-service';
import { searchEvidence } from '@/lib/retrieval/book-search';
import { groundScene } from '@/lib/retrieval/scene-grounding';
import fs from 'fs';
import path from 'path';
import { getUserById } from '@/lib/auth/users';
import { applySubscriptionTick, spendCredits } from '@/lib/auth/users';
import { retrieveFromSupabase, rerankHeuristically } from '@/lib/retrieval/vector-retrieval';

function canonicalizeWorkTitle(title: string): string {
  const raw = (title || '').trim();
  // 去除所有空白（含中文空格、零宽字符），统一别名
  const t = raw
    .replace(/[\u200B-\u200D\uFEFF]/g, '') // zero-width
    .replace(/\s+/g, '');
  // 将“逆爱”统一映射到“逆袭”，以便使用整书索引
  if (t === '逆爱' || t.includes('逆爱')) return '逆袭';
  // 将“逆 襲/逆襲”等变体统一为“逆袭”
  if (t === '逆袭' || t.includes('逆袭') || t.includes('逆襲')) return '逆袭';
  return t;
}


// 新增：为耗时操作提供超时保护
async function withTimeout<T>(promise: Promise<T>, ms: number, tag = 'task'): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const id = setTimeout(() => reject(new Error(`TIMEOUT_${tag}_${ms}ms`)), ms);
    promise.then(
      (v) => { clearTimeout(id); resolve(v); },
      (err) => { clearTimeout(id); reject(err); }
    );
  });
}
export async function POST(request: NextRequest) {
  console.log('🚀 === 开始AI生成请求 ===');
  const t0 = Date.now();
  
  try {
    // 单次读取原始文本，避免 Body 已被读取错误
    const rawText = await request.text();
    let raw: any = {};
    if (rawText) {
      if (rawText.trim().startsWith('{')) {
        try { raw = JSON.parse(rawText); } catch { raw = {}; }
      } else {
        const params = new URLSearchParams(rawText);
        raw = Object.fromEntries(params.entries());
      }
    }
    console.log('📥 请求数据:', JSON.stringify(raw, null, 2));
    
    // 兼容旧参数名（别名）
    const workTitle = raw.workTitle ?? raw.work ?? raw.title ?? '';
    const intent = raw.intent ?? raw.intentId ?? raw.mode ?? '';
    const userPrompt = raw.userPrompt ?? raw.prompt ?? raw.content ?? '';
    const aiProvider = raw.aiProvider ?? 'deepseek';
    const useSmartSearch = !!(raw.useSmartSearch ?? false);
    const tier = raw.tier ?? 'deep';
    const authorStyle = raw.authorStyle;
    const referenceText = raw.referenceText;
    const maxRounds = raw.maxRounds;
    const minRounds = raw.minRounds;
    const targetLenOverride = raw.targetLen;
    const universe = raw.universe ?? 'novel'; // 新增：宇宙参数，默认为'novel'
    const previousContent = raw.previousContent ?? ''; // 新增：前文内容，用于续写模式
    const isContinuation = intent === 'continuation' && previousContent; // 判断是否为续写模式

    const cost = 10; // 每次生成消耗10灵感
    const userId = request.headers.get('x-user-id') || '';
    if (userId) {
      try {
        await applySubscriptionTick(userId);
        const u = await getUserById(userId);
        if (!u || (u.credits||0) < cost) {
          return NextResponse.json({ success:false, error:`余额不足（需要 ${cost} 灵感）` }, { status: 402 });
        }
        // 重要：只在AI真正生成成功后才扣费，避免失败也扣费
        // await spendCredits(userId, cost); // 先注释掉，生成成功后再扣
      } catch (err) {
        console.error('⚠️ 用户认证/扣费环节出现异常:', err);
      }
    }

    // 根据universe参数决定workTitleForSearch
    let workTitleForSearch;
    if (universe === 'real_person') {
      workTitleForSearch = '逆爱真人宇宙';
    } else {
      workTitleForSearch = canonicalizeWorkTitle(workTitle);
    }
    
    // 保持原有的canonicalTitle用于兼容性
    const canonicalTitle = workTitleForSearch;

    if (!canonicalTitle || !intent || !userPrompt) {
      console.log('❌ 缺少必要参数');
      return NextResponse.json(
        { error: '请提供作品名称、创作意图和具体描述' },
        { status: 400 }
      );
    }

    console.log('✅ 参数验证通过');
    console.log(`📖 作品: ${canonicalTitle}`);
    console.log(`🎯 意图: ${intent}`);
    console.log(`💭 用户描述: ${userPrompt}`);
    console.log(`🤖 AI服务: ${aiProvider}`);

    // RAG - 检索作品信息（用归一后的标题）
    console.log('🔍 开始检索作品信息...');
    let workInfo = null;

    if (canonicalTitle.startsWith('http')) {
      console.log('🌐 检测到URL输入，将进行网页抓取');
      try {
        const scrapedContent = await import('@/lib/ai-service').then(module => module.scrapeWebContent(canonicalTitle));
        console.log('✅ 网页内容抓取成功');
        workInfo = await createGenericWorkInfo('网页内容', userPrompt);
        workInfo.sampleText = scrapedContent;
        workInfo.title = '基于网页内容的创作';
      } catch (error) {
        console.error('❌ 网页抓取失败:', error);
        workInfo = await createGenericWorkInfo('网页内容', userPrompt);
      }
    } else {
      workInfo = await db.getWorkByTitle(canonicalTitle) || await db.getWorkByTitle(workTitle);
      console.log('📚 数据库查询结果:', workInfo ? '找到作品' : '未找到作品');
    }

    if (!workInfo) {
      console.log('📝 作品不在预设库中，创建通用作品信息');
      workInfo = await createGenericWorkInfo(canonicalTitle, userPrompt);
      console.log('✅ 通用作品信息创建完成');
    }

    console.log('✅ 作品信息获取成功');
    console.log(`📝 作品简介: ${workInfo.summary.substring(0, 100)}...`);
    console.log(`🎭 角色数量: ${Object.keys(workInfo.characters).length}`);
    console.log(`📖 样本文本长度: ${workInfo.sampleText.length}字符`);

    // 增强生成：锚点-证据-注入 三位一体
    let autoAuthorStyle = authorStyle;
    let autoReferenceText = referenceText;
    let aiPersonaPrompt = '';

    // 根据universe设置AI人格
    if (universe === 'real_person') {
      aiPersonaPrompt = `角色：你是一位顶级的真人CP（RPS）同人作家，也是一位善于"用显微镜磕糖"的微表情分析专家。你对田栩宁（田雷）和梓渝（郑朋）在《逆爱》拍摄期间的互动细节了如指掌。\n\n核心任务：你的任务不是模仿任何小说家的风格，而是要**"复刻真实"**。你需要将参考资料中那些关于他们真实互动的**分析性文字**，转化为一篇**细腻、真实、充满情感暗流和内心拉扯**的叙事故事。\n\n写作风格铁律 (Style Rules):\n1. **【聚焦潜台词】** 你的写作重心，必须放在**"未说出口的话"**上。重点描写那些非语言的交流：眼神的躲闪与追逐、下意识的触碰、身体姿态的靠近与疏远、呼吸的停滞与变化。\n2. **【丰富的内心风暴】** 必须为角色赋予**极其丰富、细腻且充满矛盾**的内心独白。深入探索他们在某个瞬间的真实想法：是试探？是心动？是嫉妒？还是不知所措？这正是粉丝分析的精髓。\n3. **【还原真实人设】** 严格遵循参考资料中对两人性格的分析：\n   * **田雷（田栩宁）：** 外在是情绪稳定到近乎"空心"的年上感，但对梓渝有着极强的**洞察力**和**无限的包容**。他的温柔和爱意，是通过**"兜住对方情绪"**的行动和**"无法隐藏的注视"**来体现的。\n   * **梓渝（郑朋）：** 在外人面前是"乖乖可爱小孩"，但在田雷面前，会展现出**"恃宠而骄"**的、爱"刺挠"人的真实一面。他敏感、细腻，渴望被关注，也极度依赖田雷给予的安全感。\n4. **【营造氛围】** 你的文字必须营造出一种**"旁若无人"**的亲密氛围和**"戏我难分"**的暧昧感。让读者感觉自己像一个躲在角落里的"显微镜女孩"，正在窥探着只属于他们二人的秘密。`;
    }

    if (workTitleForSearch === '逆袭' || workTitleForSearch === '逆爱真人宇宙') {
      // 先尝试 Supabase 向量检索（hybrid 模式）
      try {
        const useVector = (process.env.RAG_PROVIDER || 'hybrid') !== 'local';
        const qrw = (process.env.RAG_QUERY_REWRITE || 'true') !== 'false';
        console.log('🚀🚀🚀 DEBUG: RAG_QUERY_REWRITE =', qrw);
        let injectedEvidenceBlock = '';
        let injectedEvidenceSource: 'vector' | 'local' | 'none' = 'none';
        let vectorEvidenceUsed = false;
        let vectorEvidenceItems: any[] = [];
        if (useVector) {
          console.log('🧭 尝试向量检索（Supabase）...');
          const vecItems = await retrieveFromSupabase({ workTitle: workTitleForSearch, query: userPrompt, topK: 12, oversample: 36 });
          if (vecItems && vecItems.length) {
            console.log('🚀🚀🚀 DEBUG: 使用重排逻辑 rerankHeuristically');
            const ranked = rerankHeuristically(userPrompt, vecItems).slice(0, 8);
            vectorEvidenceItems = ranked;
            vectorEvidenceUsed = true;
            try {
              const preview = ranked.slice(0, 5).map((it, idx) => ({ idx, chap_index: it.chap_index, chapter_no: it.chapter_no, sim: it.similarity, textPreview: (it.chunk_text||'').slice(0, 120) }));
              console.log('🧪 向量证据预览（重排后top5）:', JSON.stringify(preview, null, 2));
            } catch {}
            const stitched = ranked
              .sort((a, b) => a.chap_index - b.chap_index || a.start - b.start)
              .map(it => `【第${it.chapter_no || it.chap_index}章 ${it.chapter_title || ''}】\n${(it.chunk_text || '').slice(0, 600)}`)
              .join('\n\n');
            const block = `【整书证据片段（向量检索｜章节提示）】\n` + stitched.substring(0, 4000);
            autoReferenceText = (autoReferenceText || '') + `\n\n` + block;
            injectedEvidenceBlock = block;
            injectedEvidenceSource = 'vector';
            console.log('✅ 向量检索命中，已注入证据');
          } else {
            console.log('ℹ️ 向量检索无结果，回退本地检索');
          }
        }

        // 再锚定
      const grounding = groundScene(workTitleForSearch, userPrompt);
        // 本地检索兜底
      let pack = searchEvidence(workTitleForSearch, userPrompt, 12);

      // 将锚点对应 chunk 强制置顶到证据列表
      if (grounding?.anchor) {
        const topIdx = pack.items.findIndex(it => it.chapIndex === grounding.anchor!.chapIndex);
        if (topIdx > 0) {
          const [hit] = pack.items.splice(topIdx, 1);
          pack.items.unshift(hit);
        }
      }

        // 注入本地证据：仅当未使用向量证据，或显式允许合并
        const allowLocalFallback = process.env.RAG_INJECT_LOCAL_FALLBACK === 'true';
        if (!vectorEvidenceUsed || allowLocalFallback) {
      if (pack.items.length > 0) {
        const stitched = pack.items
          .slice(0, 8)
          .sort((a, b) => a.chapIndex - b.chapIndex || a.start - b.start)
          .map(it => `【第${it.chapterNo || it.chapIndex}章 ${it.chapterTitle || ''}】\n${(it.text || '').slice(0, 600)}`)
          .join('\n\n');
            const block = `【整书证据片段（含章节提示｜锚点优先）】\n` + stitched.substring(0, 4000);
            autoReferenceText = (autoReferenceText || '') + `\n\n` + block;
            if (!vectorEvidenceUsed) {
              injectedEvidenceBlock = block;
              injectedEvidenceSource = 'local';
            }
        console.log('📚 已注入整书证据（锚点置顶）');
            try {
              const debugPreview = pack.items.slice(0, 5).map((it, idx) => ({
                idx,
                chapIndex: it.chapIndex,
                chapterNo: it.chapterNo,
                chapterTitle: it.chapterTitle,
                score: Number(it.score?.toFixed?.(3) || it.score) || it.score,
                textPreview: (it.text || '').slice(0, 160)
              }));
              console.log('🔎 证据预览（前5条）:', JSON.stringify(debugPreview, null, 2));
            } catch {}
          }
        } else {
          console.log('⏭️ 已跳过本地证据注入（已使用向量证据）');
        }

        // 双对比日志：注入来源与截断预览
        try {
          console.log('🧪 即将注入的证据来源:', injectedEvidenceSource);
          console.log('🧪 即将注入的证据预览:', injectedEvidenceBlock.slice(0, 300));
        } catch {}

      // 注入本次"剧情硬约束（来自用户提示）"，用于生成阶段严格贴合用户意图
      if (isContinuation) {
        // 续写模式：注入前文内容和续写指令
        autoReferenceText += `\n\n【续写模式 - 前文内容】\n${previousContent}\n\n【续写指令】\n请基于上述前文内容，自然流畅地续写下一章节。保持人物性格、情节逻辑和文风的一致性。\n用户续写要求：${userPrompt}`;
      } else {
        // 普通模式：原有逻辑
        autoReferenceText += `\n\n【本次剧情硬约束（来自用户提示）】\n请严格围绕用户提示进行创作，若与证据存在冲突，以用户提示为主，但不得违背人物卡的硬边界与口气。\n用户提示：${userPrompt}`;
      }

      if (grounding.anchor) {
        autoReferenceText += `\n\n【场景锚定】\n章节：第${grounding.anchor.chapterNo}章 ${grounding.anchor.chapterTitle || ''}\n片段：\n${grounding.anchor.snippet.slice(0, 600)}\n`;
          try {
            console.log('📌 锚点摘要:', {
              chapIndex: grounding.anchor.chapIndex,
              chapterNo: grounding.anchor.chapterNo,
              chapterTitle: grounding.anchor.chapterTitle,
              score: Number(grounding.anchor.score?.toFixed?.(3) || grounding.anchor.score) || grounding.anchor.score,
              prevPreview: grounding.anchor.prevText?.slice(0, 100),
              nextPreview: grounding.anchor.nextText?.slice(0, 100)
            });
          } catch {}
      }

      if (grounding.personas?.length) {
        try {
          const dataDir = path.join(process.cwd(), 'data');
          const dialogues = JSON.parse(fs.readFileSync(path.join(dataDir,`${workTitleForSearch}-dialogues.json`),'utf8'));
          const pc = grounding.personas.map(p => `${p.name}｜事实：${p.facts.join('、') || '——'}｜常用表达：${p.phrases.join(' / ') || '——'}`).join('\n');
          autoReferenceText += `\n【人物卡】\n${pc}\n`;
          // 说话风格指纹
          const speakers = new Set(grounding.involvedNames);
          const prints: string[] = [];
          for (const name of Array.from(speakers)) {
            const lines = dialogues.filter((d: any) => d.speaker === name).slice(0, 4).map((d: any) => `“${(d.text || '').slice(0, 28)}”`).join(' / ');
            if (lines) prints.push(`${name}: ${lines}`);
          }
          if (prints.length) {
            autoReferenceText += `\n【说话风格指纹（少量台词示例，禁止照搬原句）】\n${prints.join('\n')}\n`;
          }
        } catch {}
      }
      if (universe === 'real_person') {
        // 真人宇宙不设置autoAuthorStyle，使用aiPersonaPrompt
      } else {
        autoAuthorStyle = '柴鸡蛋';
      }

      console.log('✨ 使用增强风格模仿生成模式');
try {
  const generationOptions: any = {
    referenceText: autoReferenceText || referenceText,
    aiProvider,
    intent,
    ...(typeof maxRounds === 'number' ? { maxRounds } : {}),
    ...(typeof minRounds === 'number' ? { minRounds } : {}),
    ...(typeof targetLenOverride === 'number' ? { targetLen: targetLenOverride } : {}),
    ...(isContinuation ? { isContinuation: true, previousContent, universe } : {}),
  };
  
  // 根据universe设置不同的风格参数
  if (universe === 'real_person') {
    generationOptions.aiPersonaPrompt = aiPersonaPrompt;
  } else {
    generationOptions.authorStyle = autoAuthorStyle || authorStyle;
  }
  
  const generationResult = await withTimeout(
    generateHighQualityContent(userPrompt, generationOptions),
    200000,
    'enhanced_generate'
  );
  const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000));

  // 计算续写轮次
  let continuationRounds = 0;
  if (isContinuation && generationResult.metadata) {
    continuationRounds = (generationResult.metadata.rounds || 0) + 1;
    generationResult.metadata.rounds = continuationRounds;
    generationResult.metadata.isContinuation = true;
    generationResult.metadata.previousContentLength = previousContent.length;
  }

  // 生成成功，扣除灵感
  if (userId) {
    try {
      await spendCredits(userId, cost);
      console.log(`✅ 用户 ${userId} 成功扣除 ${cost} 灵感`);
    } catch (err) {
      console.error('⚠️ 扣费失败，但生成成功:', err);
    }
  }

  return NextResponse.json({
    success: true,
    mode: 'enhanced_quality',
    data: {
      workTitle: workTitle || `${autoAuthorStyle || authorStyle}风格创作`,
      intent,
      userPrompt,
      content: generationResult.content,
      wordCount: generationResult.content.length,
      qualityScore: 96,
      generationTime: elapsed,
      aiProvider,
      styleAnalysis: generationResult.styleAnalysis,
      selectedAuthor: autoAuthorStyle || authorStyle,
      metadata: generationResult.metadata,
      evidence: pack,
              evidenceVector: vectorEvidenceItems,
              injectedEvidenceSource: injectedEvidenceSource,
      grounding,
      timestamp: new Date().toISOString()
    }
  });
} catch (enhancedErr) {
  console.warn('⚠️ 增强模式失败或超时，切换到传统模式:', (enhancedErr as Error)?.message);
  try {
    const generatedContent = await withTimeout(
      generateContent({ workTitle, intent, userPrompt, workInfo }, aiProvider),
       60000,
      'fallback_traditional'
    );
    // 生成成功，扣除灵感
    if (userId) {
      try {
        await spendCredits(userId, cost);
        console.log(`✅ 用户 ${userId} 成功扣除 ${cost} 灵感`);
      } catch (err) {
        console.error('⚠️ 扣费失败，但生成成功:', err);
      }
    }
    
    const wordCount = generatedContent.length;
    const qualityScore = Math.floor(Math.random() * 10) + 90;
    const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000));
    return NextResponse.json({ success: true, mode: 'fallback_traditional', data: { workTitle, intent, userPrompt, content: generatedContent, wordCount, qualityScore, generationTime: elapsed, aiProvider, tier, cost, timestamp: new Date().toISOString(), warning: '增强模式耗时/失败，已自动切换传统生成' } });
  } catch (fallbackErr) {
    console.error('❌ 传统模式回退也失败:', fallbackErr);
    const fallbackContent2 = generateFallbackContent(workInfo, intent, userPrompt); const wordCount2 = fallbackContent2.length; const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000)); return NextResponse.json({ success: true, mode: 'template_fallback', data: { workTitle, intent, userPrompt, content: fallbackContent2, wordCount: wordCount2, qualityScore: 82, generationTime: elapsed, aiProvider: 'fallback', tier, cost: 0, timestamp: new Date().toISOString(), warning: '外部AI失败/超时，返回模板兜底内容（未扣费）' } });
  }
}
      } catch (e) {
        console.log('⚠️ 向量检索异常，回退本地检索:', (e as any)?.message || String(e));
    }

    // 非逆袭：沿用原逻辑
    console.log('🤖 选择生成模式...');
    let generationResult;
    try {
      if (useSmartSearch) {
        console.log('🔍 使用智能检索生成模式');
        const searchResults = semanticSearch(userPrompt);
        console.log(`📚 找到 ${searchResults.results.length} 个相关作品`);
        if (searchResults.results.length > 0) {
          generationResult = await generateFromSearch(userPrompt, searchResults.results, `创作主题：${userPrompt}`);
          return NextResponse.json({ success: true, mode: 'smart_search', data: { /* 省略，无改动 */ } });
        }
      }

      console.log('📝 使用传统生成模式');
      const generatedContent = await generateContent({ workTitle, intent, userPrompt, workInfo }, aiProvider);
      console.log('✅ AI生成完成');
      console.log(`📄 生成内容长度: ${generatedContent.length}字符`);
      
      // 生成成功，扣除灵感
      if (userId) {
        try {
          await spendCredits(userId, cost);
          console.log(`✅ 用户 ${userId} 成功扣除 ${cost} 灵感`);
        } catch (err) {
          console.error('⚠️ 扣费失败，但生成成功:', err);
        }
      }
      
      const wordCount = generatedContent.length;
      const qualityScore = Math.floor(Math.random() * 10) + 90;
      const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000));

      return NextResponse.json({ success: true, mode: 'traditional', data: { workTitle, intent, userPrompt, content: generatedContent, wordCount, qualityScore, generationTime: elapsed, aiProvider, tier, cost, timestamp: new Date().toISOString() } });
    } catch (aiError) {
      console.error('❌ AI服务调用失败:', aiError);
      const hasValidApiKey = process.env.GEMINI_API_KEY || process.env.KIMI_API_KEY || process.env.DEEPSEEK_API_KEY;
      if (!hasValidApiKey) {
        console.error('🚨 致命错误：没有配置任何API密钥');
        return NextResponse.json({ success: false, error: '系统配置错误：未找到有效的AI服务API密钥', details: { gemini: !!process.env.GEMINI_API_KEY, kimi: !!process.env.KIMI_API_KEY, deepseek: !!process.env.DEEPSEEK_API_KEY, message: '请检查.env.local文件配置并重启服务器' } }, { status: 500 });
      }
      console.log('🔄 AI服务调用失败，使用备用模板生成内容');
      const fallbackContent = generateFallbackContent(workInfo, intent, userPrompt);
      const errMsg = (aiError as Error)?.message || 'AI调用失败';
      const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000));
      return NextResponse.json({ success: true, data: { workTitle, intent, userPrompt, content: fallbackContent, wordCount: fallbackContent.length, qualityScore: 85, generationTime: elapsed, aiProvider: 'fallback', tier, cost, timestamp: new Date().toISOString(), warning: `AI服务调用失败: ${errMsg}。使用了备用生成方案。` } });
      }
    }
  } catch (error) {
    console.error('❌ === 生成请求失败 ===');
    console.error('错误详情:', error);
    
    // 确保返回有效的JSON，避免"Unexpected end of JSON input"错误
    try {
      // 如果是网络或API错误，不扣费
      if (error instanceof Error && (
        error.message.includes('fetch') || 
        error.message.includes('API') || 
        error.message.includes('网络') ||
        error.message.includes('timeout') ||
        error.message.includes('TIMEOUT')
      )) {
        console.log('🔄 检测到API/网络错误，使用备用内容，不扣费');
        const safeTitle = workTitle || '通用创作';
        const info = await createGenericWorkInfo(safeTitle, userPrompt || '');
        const content = generateFallbackContent(info, intent || 'detail', userPrompt || '系统兜底');
        const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000));
        return NextResponse.json({ 
          success: true, 
          mode: 'api_error_fallback', 
          data: { 
            workTitle: safeTitle, 
            intent: intent || 'detail', 
            userPrompt: userPrompt || '', 
            content, 
            wordCount: content.length, 
            qualityScore: 80, 
            generationTime: elapsed, 
            aiProvider: 'fallback', 
            cost: 0,
            timestamp: new Date().toISOString(),
            warning: 'AI服务暂时不可用，已使用备用生成方案（未扣费）'
          } 
        }, { status: 200 });
      }
      
      // 其他错误也使用备用方案
      const safeTitle = workTitle || '通用创作';
      const info = await createGenericWorkInfo(safeTitle, userPrompt || '');
      const content = generateFallbackContent(info, intent || 'detail', userPrompt || '系统兜底');
      const elapsed = Math.max(1, Math.round((Date.now() - t0) / 1000));
      return NextResponse.json({ 
        success: true, 
        mode: 'ultimate_fallback', 
        data: { 
          workTitle: safeTitle, 
          intent: intent || 'detail', 
          userPrompt: userPrompt || '', 
          content, 
          wordCount: content.length, 
          qualityScore: 80, 
          generationTime: elapsed, 
          aiProvider: 'fallback', 
          cost: 0,
          timestamp: new Date().toISOString(),
          warning: '生成过程出现问题，已使用备用方案（未扣费）'
        } 
      }, { status: 200 });
    } catch (fallbackError) {
      // 最终兜底：确保一定返回有效JSON
      console.error('❌ 连备用方案都失败了:', fallbackError);
      return NextResponse.json({ 
        success: false, 
        error: '系统暂时不可用，请稍后重试',
        timestamp: new Date().toISOString()
      }, { status: 500 });
    }
  }
}

// 创建通用作品信息（当数据库中没有预设作品时使用）
async function createGenericWorkInfo(workTitle: string, userPrompt?: string) {
  console.log(`🎭 为作品《${workTitle}》创建通用信息`);
  
  // 尝试从标题中提取角色名（支持CP格式）
  let characters: Record<string, string> = {};
  
  // 检查是否包含CP关系（如：谢怜x花城、魏无羡与蓝忘机）
  const cpPatterns = [
    /(.+?)[x×与和](.+?)$/,  // 匹配 A x B 或 A与B
    /(.+?)和(.+?)$/,        // 匹配 A和B
    /(.+?)与(.+?)$/,        // 匹配 A与B
  ];
  
  for (const pattern of cpPatterns) {
    const match = workTitle.match(pattern);
    if (match) {
      const [, char1, char2] = match;
      characters[char1.trim()] = `${char1.trim()}，故事中的重要角色之一，性格鲜明，有着独特的魅力`;
      characters[char2.trim()] = `${char2.trim()}，与${char1.trim()}关系密切的重要人物，在故事中扮演关键角色`;
      break;
    }
  }
  
  // 如果没有找到CP关系，尝试其他模式
  if (Object.keys(characters).length === 0) {
    // 检查用户提示中是否包含具体角色名
    if (userPrompt) {
      const nameMatches = userPrompt.match(/[\u4e00-\u9fff]{2,4}/g); // 匹配中文姓名
      if (nameMatches && nameMatches.length >= 2) {
        characters[nameMatches[0]] = `${nameMatches[0]}，故事中的主要角色，个性鲜明`;
        characters[nameMatches[1]] = `${nameMatches[1]}，与${nameMatches[0]}关系密切的重要人物`;
      }
    }
  }
  
  // 如果仍然没有找到，使用默认角色名
  if (Object.keys(characters).length === 0) {
    const defaultNames = extractPossibleNames(workTitle);
    if (defaultNames.length >= 2) {
      characters[defaultNames[0]] = `${defaultNames[0]}，故事的中心人物，性格鲜明，有着独特的魅力和成长轨迹`;
      characters[defaultNames[1]] = `${defaultNames[1]}，与${defaultNames[0]}关系密切的重要人物，在故事中扮演关键角色`;
    } else {
      characters['主角'] = '故事的中心人物，性格鲜明，有着独特的魅力和成长轨迹';
      characters['重要角色'] = '与主角关系密切的重要人物，在故事中扮演关键角色';
    }
  }
  
  return {
    id: 'generic-' + Math.random().toString(36).substr(2, 9),
    title: workTitle,
    author: '未知作者',
    summary: `《${workTitle}》是一部精彩的文学作品，拥有丰富的人物关系和引人入胜的故事情节。`,
    characters,
    worldSetting: determineWorldSetting(workTitle, userPrompt),
    style: '文笔流畅，情节紧凑，善于刻画人物心理和情感变化，注重细节描写和氛围营造',
    sampleText: generateSampleText(Object.keys(characters)),
    status: 'COMPLETED',
    created_at: new Date().toISOString()
  };
}

// 从作品标题中提取可能的角色名
function extractPossibleNames(title: string): string[] {
  const names: string[] = [];
  
  // 常见的角色名模式
  const namePatterns = [
    /《?(.+?)》?的?(.+?)$/,    // 匹配《作品名》的角色名
    /(.+?)[师尊|师父|师傅]$/,   // 匹配XX师尊
    /(.+?)[公子|少爷|大人]$/,   // 匹配XX公子
  ];
  
  for (const pattern of namePatterns) {
    const match = title.match(pattern);
    if (match && match.length > 1) {
      names.push(...match.slice(1).filter(name => name.length <= 4));
    }
  }
  
  return names.length > 0 ? names : [title.substring(0, 3) || '主角', title.substring(3, 6) || '配角'];
}

// 根据标题和用户输入确定世界设定
function determineWorldSetting(title: string, userPrompt?: string): string {
  if (userPrompt) {
    if (userPrompt.includes('校园') || userPrompt.includes('学校') || userPrompt.includes('现代')) {
      return '现代校园背景，青春洋溢的学生时代';
    }
    if (userPrompt.includes('古代') || userPrompt.includes('宫廷') || userPrompt.includes('江湖')) {
      return '古代背景，有着深厚历史文化底蕴的世界';
    }
    if (userPrompt.includes('修仙') || userPrompt.includes('仙侠') || userPrompt.includes('修真')) {
      return '仙侠修真世界，充满神秘力量和奇幻色彩';
    }
  }
  
  // 根据标题判断
  if (title.includes('师尊') || title.includes('修仙') || title.includes('仙')) {
    return '仙侠修真世界，充满神秘力量和奇幻色彩';
  }
  
  return '一个充满想象力的世界，有着独特的背景设定和世界观';
}

// 生成样本文本
function generateSampleText(characterNames: string[]): string {
  const char1 = characterNames[0] || '主角';
  const char2 = characterNames[1] || '重要角色';
  
  return `
    夜色如墨，月光如水般洒在大地上。

    ${char1}站在窗前，望着远方的灯火，心中五味杂陈。今天发生的一切，如电影般在脑海中回放。

    "你还好吗？"身后传来熟悉的声音。

    ${char1}转过身，看到${char2}正站在门口，眼中满含关切。

    "还好，只是在想一些事情。"${char1}轻声回答。

    ${char2}走近，与${char1}并肩站立："有什么心事可以告诉我。"

    ${char1}看着身边的人，心中涌起一阵暖流。在这个世界上，能有这样一个人愿意倾听、愿意陪伴，已经是莫大的幸福。

    "谢谢你，一直陪在我身边。"

    两人就这样静静地站着，享受着这份宁静美好的时光。
  `.trim();
}

// 备用内容生成函数（当AI服务不可用时使用）
function generateFallbackContent(workInfo: any, intent: string, userPrompt: string): string {
  console.log('🔄 生成个性化备用内容');
  
  const intentNames = {
    sequel: '续写番外',
    au: '平行世界',
    style: '风格模仿',
    detail: '细节补完',
    action: '高能场面',
    psych: '心理深挖',
    side: '配角外传',
  } as const;

  const characters = Object.keys(workInfo.characters);
  const char1 = characters[0] || '主角';
  const char2 = characters[1] || '重要角色';
  
  // 基于用户输入生成更个性化的内容
  let storyContent = '';
  
  if (userPrompt.includes('校园') || userPrompt.includes('学校') || userPrompt.includes('现代')) {
    storyContent = generateModernAuContent(char1, char2, userPrompt);
  } else if (userPrompt.includes('第一次') || userPrompt.includes('告白')) {
    storyContent = generateConfessionContent(char1, char2, userPrompt);
  } else if (userPrompt.includes('相遇') || userPrompt.includes('见面')) {
    storyContent = generateMeetingContent(char1, char2, userPrompt);
  } else {
    storyContent = generateGenericContent(char1, char2, userPrompt);
  }

  return `
【${intentNames[intent as keyof typeof intentNames]}】基于《${workInfo.title}》

根据您的要求："${userPrompt}"

${storyContent}

————————————————

⚠️ 提示：当前使用备用生成模式。要获得真正的AI创作内容，请：
1. 确保.env.local文件中配置了有效的API密钥  
2. 重启开发服务器 (npm run dev)
3. 检查API密钥是否有效且有足够配额
  `.trim();
}

// 现代AU内容生成
function generateModernAuContent(char1: string, char2: string, userPrompt: string): string {
  return `
阳光透过梧桐叶片洒在校园的石阶上，${char1}背着书包匆匆走过。

"诶，你等等！"身后传来喊声。

${char1}回头，看见${char2}正小跑着追上来，手里还拿着一本书。

"你的笔记本掉了。"${char2}有些气喘，将本子递给${char1}。

"谢谢..."${char1}接过本子，指尖无意中碰到${char2}的手，温热的触感让人心跳加速。

两人就这样静静对视着，周围的喧嚣似乎都安静下来，只有梧桐叶片沙沙作响。

"那个...要不要一起去图书馆？"${char2}轻声提议。

${char1}点点头，嘴角不自觉地上扬："好啊。"

从这一刻开始，他们的故事正式拉开了序幕...
  `.trim();
}

// 告白内容生成
function generateConfessionContent(char1: string, char2: string, userPrompt: string): string {
  return `
夜色深沉，月光如水。

${char1}站在窗前，深吸了一口气。今天，他终于要说出那句话了。

"${char2}。"他轻声唤道。

"嗯？"${char2}走过来，月光在他脸上洒下柔和的光影。

"我..."${char1}感到前所未有的紧张，"我有话想对你说。"

${char2}静静看着他，眼中有着温柔的鼓励。

"我爱你。"${char1}终于说出了口，声音虽然轻，但每个字都清晰有力。

空气中仿佛有什么在颤动，${char2}愣了一瞬，然后伸出手轻抚${char1}的脸颊。

"我等这句话...很久了。"${char2}的声音带着颤抖，却是幸福的颤抖。

他们拥抱在一起，在月光下许下永恒的誓言...
  `.trim();
}

// 相遇内容生成  
function generateMeetingContent(char1: string, char2: string, userPrompt: string): string {
  return `
人来人往的街头，${char1}正在寻找着什么。

忽然，一个熟悉的身影出现在视线里。是${char2}。

两人四目相对，时间仿佛静止了一般。

"真的是你啊。"${char2}先开了口，声音里带着难以置信的惊喜。

${char1}点点头，千言万语最终化为一个微笑："是我。"

"你还好吗？"

"很好，你呢？"

简单的对话，却承载着太多未曾言喻的思念。

街头的喧嚣继续，但对于他们来说，此刻的世界只有彼此。

"要不要找个地方坐下聊聊？"${char1}提议道。

${char2}欣然同意，就像很久很久以前那样...
  `.trim();
}

// 通用内容生成
function generateGenericContent(char1: string, char2: string, userPrompt: string): string {
  return `
${char1}和${char2}的故事总是充满了温馨与美好。

基于您的描述"${userPrompt}"，让我为您续写这个片段...

在这个特别的时刻，${char1}看着${char2}，心中涌起千般感慨。他们之间的情感如细水长流，在每一个平凡的日常中闪闪发光。

"${userPrompt}，"${char1}轻声说道，"这就是我想要的。"

${char2}会心一笑，理解了${char1}话中的深意。有些话不必说出口，有些情不必刻意表达，因为彼此都懂。

这就是属于他们的故事，独一无二的美好。

无论是在繁华的都市，还是在宁静的乡间，只要有彼此在身边，哪里都是家...
  `.trim();
}

// 获取支持的AI服务提供商列表
export async function GET() {
  return NextResponse.json({
    providers: [
      {
        id: 'gemini',
        name: 'Google Gemini',
        description: '强大的多模态大语言模型，擅长创作和对话',
        status: process.env.GEMINI_API_KEY ? 'available' : 'needs_config'
      },
      {
        id: 'kimi',
        name: 'Kimi',
        description: '专注于长文本理解和生成的AI助手',
        status: process.env.KIMI_API_KEY ? 'available' : 'needs_config'
      },
      {
        id: 'deepseek',
        name: 'DeepSeek',
        description: '深度思考的AI模型，在创作任务上表现优异',
        status: process.env.DEEPSEEK_API_KEY ? 'available' : 'needs_config'
      }
    ],
    fallback: {
      enabled: true,
      description: '当AI服务不可用时，使用基于模板的备用生成方案'
    }
  });
}