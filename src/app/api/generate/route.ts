// /srv/nixian-v2.2/api/src/app/api/generate/route.ts

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { spendCredits, getUserById, applySubscriptionTick } from '@/lib/auth/users';
import { retrieveFromSupabase, rerankHeuristically } from '@/lib/retrieval/vector-retrieval';
import { groundScene } from '@/lib/retrieval/scene-grounding';
// Note: searchEvidence is not used in this flow, relying on vector search.
import { generateNarrativeChapter, continueNarrativeChapter } from '@/lib/enhanced-ai-service';
import fs from 'fs';
import path from 'path';

// 为耗时操作提供超时保护
async function withTimeout<T>(promise: Promise<T>, ms: number, tag = 'task'): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const id = setTimeout(() => reject(new Error(`TIMEOUT_${tag}_${ms}ms`)), ms);
    promise.then(
      (v) => { clearTimeout(id); resolve(v); },
      (err) => { clearTimeout(id); reject(err); }
    );
  });
}

// 核心作品标题归一化函数 (内置于此文件以确保逻辑统一)
function canonicalizeWorkTitle(title: string, universe: string): string {
    if (universe === 'real_person') {
        return '逆爱真人宇宙';
    }
    const raw = (title || '').trim().replace(/\s+/g, '');
    if (raw.includes('逆袭') || raw.includes('逆愛') || raw.includes('池骋') || raw.includes('吴所畏')) {
        return '逆袭';
    }
    return '逆袭'; // 默认到逆袭
}


export async function POST(request: NextRequest) {
  console.log('🚀 === 开始AI生成请求 ===');
  const t0 = Date.now();
  
  try {
    const raw = await request.json();
    console.log('📥 请求数据:', JSON.stringify(raw, null, 2));
    
    // --- 1. 参数解析与智能修正 ---
    const userPrompt = raw.userPrompt ?? raw.prompt ?? '';
    let universe = raw.universe === 'real_person' ? 'real_person' : 'novel';
    const previousContent = raw.previousContent ?? '';
    const intent = raw.intent ?? '';
    const isContinuation = intent === 'continuation' && !!previousContent;
    const workTitle = raw.workTitle ?? '';
    
    // =================================================================
    // ✨【关键修复】智能修正 universe 参数
    // 不再盲目相信前端传来的 universe，而是基于内容进行判断，确保调用正确的AI人格和数据库。
    const promptContent = (workTitle + userPrompt + intent).toLowerCase();
    if (
      (promptContent.includes('真人') || promptContent.includes('realperson')) &&
      universe !== 'real_person'
    ) {
      console.log(`⚠️ Universe mismatch detected! Request sent '${raw.universe}', but content implies 'real_person'.`);
      console.log('✅ Forcing universe to "real_person".');
      universe = 'real_person';
    }
    // =================================================================

    const workTitleForSearch = canonicalizeWorkTitle(workTitle, universe as 'novel' | 'real_person');
    const aiProvider = raw.aiProvider ?? 'deepseek';

    if (!userPrompt) {
      return NextResponse.json({ error: '请提供具体创作描述' }, { status: 400 });
    }

    // --- 2. 用户鉴权与计费 (预检查) ---
    const cost = 10; // 每次生成消耗10灵感
    const userId = request.headers.get('x-user-id') || '';
    if (userId) {
      await applySubscriptionTick(userId);
      const user = await getUserById(userId);
      if (!user || (user.credits || 0) < cost) {
        return NextResponse.json({ success: false, error: `余额不足（需要 ${cost} 灵感）` }, { status: 402 });
      }
    }

    // --- 3. RAG 检索增强 (为AI提供记忆和素材) ---
    console.log(`🔍 Universe: ${universe}, Work: ${workTitleForSearch}. Starting RAG...`);
    let referenceText = '';
    
    // 步骤 3.1: 向量检索 + 重排
    const vecItems = await retrieveFromSupabase({ workTitle: workTitleForSearch, query: userPrompt, topK: 12, oversample: 36 });
    const rankedItems = vecItems?.length ? rerankHeuristically(userPrompt, vecItems).slice(0, 8) : [];
    if (rankedItems.length) {
        const stitched = rankedItems
            .sort((a, b) => a.chap_index - b.chap_index || a.start - b.start)
            .map(it => `【第${it.chapter_no}章 ${it.chapter_title}】\n${it.chunk_text}`)
            .join('\n\n');
        referenceText += `【相关参考片段】\n${stitched.substring(0, 4000)}\n\n`;
        console.log('✅ Vector search successful. Injected evidence.');
    } else {
        console.log('ℹ️ Vector search returned no results for this query.');
    }

    // 步骤 3.2: 场景锚定与人物卡
    const grounding = await groundScene(workTitleForSearch, userPrompt);
    if (grounding.personas?.length) {
      try {
        const dataDir = path.join(process.cwd(), 'data');
        const dialogues = JSON.parse(fs.readFileSync(path.join(dataDir,`${workTitleForSearch}-dialogues.json`),'utf8'));
        const pc = grounding.personas.map(p => `${p.name}｜事实：${p.facts.join('、')}｜常用表达：${p.phrases.join(' / ')}`).join('\n');
        referenceText += `【人物卡】\n${pc}\n`;

        const speakers = new Set(grounding.involvedNames);
        const prints: string[] = [];
        for (const name of Array.from(speakers)) {
            const lines = dialogues.filter((d: any) => d.speaker === name).slice(0, 4).map((d: any) => `“${d.text}”`).join(' / ');
            if (lines) prints.push(`${name}: ${lines}`);
        }
        if (prints.length) {
            referenceText += `\n【说话风格指纹（台词示例）】\n${prints.join('\n')}\n`;
        }
        console.log('✅ Personas and dialogue fingerprints injected.');
      } catch (e) {
        console.warn('⚠️ Could not load dialogue data:', e);
      }
    }
    
    // --- 4. 调用核心叙事引擎 ---
    let generationResult: { content: string; metadata: any; };
    const generationOptions = { referenceText, universe: universe as 'novel' | 'real_person', aiProvider };

    console.log(`🧠 Calling Narrative Engine. Mode: ${isContinuation ? 'Continuation' : 'New Chapter'}`);
    if (isContinuation) {
      generationResult = await withTimeout(
        continueNarrativeChapter(userPrompt, previousContent, generationOptions),
        240000, // 续写超时时间更长
        'continue_narrative'
      );
    } else {
      generationResult = await withTimeout(
        generateNarrativeChapter(userPrompt, generationOptions),
        240000,
        'generate_narrative'
      );
    }
    
    // --- 5. 成功后扣费并返回结果 ---
    if (userId) {
        await spendCredits(userId, cost);
        console.log(`✅ User ${userId} credits spent: ${cost}`);
    }

    const elapsed = Math.round((Date.now() - t0) / 1000);
    console.log(`✅ Generation successful in ${elapsed}s.`);

    return NextResponse.json({
      success: true,
      mode: isContinuation ? 'continuation' : 'enhanced_narrative',
      data: {
        workTitle: workTitle || workTitleForSearch,
        intent,
        userPrompt,
        content: generationResult.content,
        wordCount: generationResult.content.length,
        generationTime: elapsed,
        aiProvider,
        metadata: generationResult.metadata,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error: any) {
    console.error('❌ === 全局生成请求失败 ===');
    console.error('错误详情:', error);
    return NextResponse.json({ 
      success: false, 
      error: error.message.includes('TIMEOUT') ? 'AI创作超时，请您稍后重试' : 'AI生成失败，请您稍后重试',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// GET handler for service discovery (can be kept for frontend use)
export async function GET() {
  return NextResponse.json({
    providers: [
      { id: 'deepseek', name: 'DeepSeek', status: process.env.DEEPSEEK_API_KEY ? 'available' : 'needs_config' }
    ]
  });
}
