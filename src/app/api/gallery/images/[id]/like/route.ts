import { NextRequest, NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

export async function POST(_request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { id } = await context.params;
  const likes = await readJson<Record<string, number>>('gallery-likes.json', {});
  likes[id] = (likes[id] || 0) + 1;
  await writeJson('gallery-likes.json', likes);
  return NextResponse.json({ id, liked: true, likes: likes[id] });
} 