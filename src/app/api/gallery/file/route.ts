import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

const PHOTO_ROOT = path.resolve(process.cwd(), 'photo');
const CATEGORY_MAP: Record<string,string> = {
  cp_main: 'cp_main',
  cp_sub: 'cp_sub', 
  group: 'group',
  Portraits_cc: 'Portraits_cc',
  Portraits_wsw: 'Portraits_wsw',
  Portraits_gcy: 'Portraits_gcy',
  Portraits_jxs: 'Portraits_jxs'
};

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const category = searchParams.get('category') || 'cp_main';
  const name = searchParams.get('name');
  
  if (!name) {
    return new NextResponse('Missing name parameter', { status: 400 });
  }

  const relDir = CATEGORY_MAP[category] || 'cp_main';
  const filePath = path.join(PHOTO_ROOT, relDir, name);
  
  // 安全检查：确保文件在允许的目录内
  if (!filePath.startsWith(path.join(PHOTO_ROOT, relDir))) {
    return new NextResponse('Invalid file path', { status: 403 });
  }

  try {
    if (!fs.existsSync(filePath)) {
      return new NextResponse('File not found', { status: 404 });
    }

    const fileBuffer = fs.readFileSync(filePath);
    const ext = path.extname(name).toLowerCase();
    
    // 设置正确的MIME类型
    let contentType = 'application/octet-stream';
    switch (ext) {
      case '.jpg':
      case '.jpeg':
        contentType = 'image/jpeg';
        break;
      case '.png':
        contentType = 'image/png';
        break;
      case '.webp':
        contentType = 'image/webp';
        break;
      case '.gif':
        contentType = 'image/gif';
        break;
    }

    // 设置缓存和压缩头
    const headers = {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=3600, s-maxage=3600', // 1小时缓存
      'Content-Length': fileBuffer.length.toString(),
      'ETag': `"${Buffer.from(filePath + fs.statSync(filePath).mtime.getTime()).toString('base64')}"`,
      'Accept-Ranges': 'bytes',
    };

    // 检查If-None-Match缓存
    const ifNoneMatch = request.headers.get('if-none-match');
    if (ifNoneMatch === headers['ETag']) {
      return new NextResponse(null, { status: 304 });
    }

    return new NextResponse(fileBuffer, { headers });
    
  } catch (error) {
    console.error('Error serving file:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
} 