import { NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const LIKES_FILE = 'gallery-likes.json';
const LIKED_USERS_FILE = 'gallery-liked-users.json';

export async function POST(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const category = (searchParams.get('category') || '').toString();
    const name = (searchParams.get('name') || '').toString();
    const userId = req.headers.get('x-user-id') || 'guest';
    if (!category || !name) return NextResponse.json({ ok: false, error: 'missing params' }, { status: 400 });

    const key = encodeURIComponent(`${category}/${name}`);
    const likes = await readJson<Record<string, number>>(LIKES_FILE, {});
    const likedUsers = await readJson<Record<string, string[]>>(LIKED_USERS_FILE, {});
    const users = new Set(likedUsers[key] || []);

    if (users.has(userId)) {
      return NextResponse.json({ ok: true, liked: true, likes: likes[key] || 0 });
    }

    users.add(userId);
    likedUsers[key] = Array.from(users);
    likes[key] = (likes[key] || 0) + 1;

    await writeJson(LIKES_FILE, likes);
    await writeJson(LIKED_USERS_FILE, likedUsers);

    return NextResponse.json({ ok: true, liked: true, likes: likes[key] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const category = (searchParams.get('category') || '').toString();
    const name = (searchParams.get('name') || '').toString();
    const userId = req.headers.get('x-user-id') || 'guest';
    if (!category || !name) return NextResponse.json({ ok: false, error: 'missing params' }, { status: 400 });

    const key = encodeURIComponent(`${category}/${name}`);
    const likes = await readJson<Record<string, number>>(LIKES_FILE, {});
    const likedUsers = await readJson<Record<string, string[]>>(LIKED_USERS_FILE, {});
    const users = new Set(likedUsers[key] || []);

    if (!users.has(userId)) {
      return NextResponse.json({ ok: true, liked: false, likes: likes[key] || 0 });
    }

    users.delete(userId);
    likedUsers[key] = Array.from(users);
    likes[key] = Math.max(0, (likes[key] || 0) - 1);

    await writeJson(LIKES_FILE, likes);
    await writeJson(LIKED_USERS_FILE, likedUsers);

    return NextResponse.json({ ok: true, liked: false, likes: likes[key] });
  } catch (error) {
    const message = (error && typeof error === 'object' && 'message' in error) ? (error as any).message : String(error);
    return NextResponse.json({ ok: false, error: message }, { status: 500 });
  }
} 