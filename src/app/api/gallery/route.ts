import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { readJson } from '@/lib/persistence/jsondb';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

// 本地图片根目录（服务器环境）
const PHOTO_ROOT = path.resolve(process.cwd(), 'photo');
const CATEGORY_MAP: Record<string,string> = {
  cp_main: 'cp_main',
  cp_sub: 'cp_sub',
  group: 'group',
  Portraits_cc: 'Portraits_cc',
  Portraits_wsw: 'Portraits_wsw',
  Portraits_gcy: 'Portraits_gcy',
  Portraits_jxs: 'Portraits_jxs'
};

function listImages(absDir: string): { id: string; file: string }[] {
  if (!fs.existsSync(absDir)) return [];
  const files = fs.readdirSync(absDir).filter(f => /\.(jpe?g|png|webp|gif)$/i.test(f));
  return files.map(f => ({ id: f, file: path.join(absDir, f) }));
}

export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const { searchParams } = url;
  const category = searchParams.get('category') || 'cp_main';
  const relDir = CATEGORY_MAP[category] || 'cp_main';
  const absDir = path.join(PHOTO_ROOT, relDir);
  const likes = await readJson<Record<string, number>>('gallery-likes.json', {});

  const proto = request.headers.get('x-forwarded-proto') || url.protocol.replace(':','') || 'http';
  const host = request.headers.get('host') || url.host || 'localhost:3000';
  const origin = `${proto}://${host}`;

  const items = listImages(absDir).map(it => ({
    id: it.id,
    url: `${origin}/api/gallery/file?category=${encodeURIComponent(category)}&name=${encodeURIComponent(it.id)}`,
    likes: likes[encodeURIComponent(`${category}/${it.id}`)] || 0
  })).sort((a,b) => b.likes - a.likes);

  return NextResponse.json({ items });
} 