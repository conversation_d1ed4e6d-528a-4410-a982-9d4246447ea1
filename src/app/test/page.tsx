'use client';

import React from 'react';
import dynamic from 'next/dynamic';

// 动态导入A项目的组件
const ParticleUniverse = dynamic(() => import('@/components/ParticleUniverse'), { ssr: false });
const TopNavigation = dynamic(() => import('@/components/TopNavigation'), { ssr: false });
const CreationIsland = dynamic(() => import('@/components/CreationIsland'), { ssr: false });
const FlightHint = dynamic(() => import('@/components/FlightHint'), { ssr: false });

export default function TestPage() {
  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* 3D Particle Universe Background */}
      <ParticleUniverse count={50000} />

      {/* UI Components */}
      <TopNavigation />
      <CreationIsland />
      <FlightHint particleCount={50000} />

      {/* Test Info */}
      <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-50">
        <div className="frosted-glass-card p-4 text-white">
          <h1 className="text-xl font-bold mb-2">A项目组件测试</h1>
          <p>如果你能看到这个页面，说明A项目的组件已经成功集成到B项目中！</p>
          <p>你应该能看到：</p>
          <ul className="list-disc list-inside mt-2">
            <li>星空背景（ParticleUniverse）</li>
            <li>顶部导航栏（TopNavigation）</li>
            <li>创作岛（CreationIsland）</li>
            <li>飞行提示（FlightHint）</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
