'use client';

import React, { useState, useEffect } from 'react';

export default function WorkingRecharge() {
  const [isMobile, setIsMobile] = useState(false);
  const [showInviteCode, setShowInviteCode] = useState(false);
  const [customAmount, setCustomAmount] = useState('');
  
  // 模拟用户数据
  const [userStats] = useState({
    credits: 123,
    fragmentBalance: 2,
    fragmentCount: 47,
    gachaTickets: 1,
    inviteCode: 'me86osv658f449d6bc60',
    inviteCount: 23 // 已邀请人数
  });

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // 配色方案
  const colors = {
    primary: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
    secondary: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
    orange: 'linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%)',
    gold: '#FFD64F',
    purple: '#666BCE',
    text: 'rgba(255, 255, 255, 0.8)',
    textLight: 'rgba(255, 255, 255, 0.7)',
    textDim: 'rgba(255, 255, 255, 0.6)'
  };

  const handleDailySignin = () => {
    alert('签到成功！获得 1 枚灵感碎片！');
  };

  const handleInviteToggle = () => {
    setShowInviteCode(!showInviteCode);
  };

  const handleGachaRedirect = () => {
    alert('跳转到我的头衔页面进行抽取！');
    // window.location.href = '/titles';
  };

  // 邀请里程碑数据
  const inviteMilestones = [
    { count: 30, reward: '150灵感奖励', title: '🔥小醋包饲养员', completed: userStats.inviteCount >= 30 },
    { count: 60, reward: '350灵感奖励', title: '🚀"逆爱"首席军师', completed: userStats.inviteCount >= 60 },
    { count: 100, reward: '700灵感奖励', title: '👑"雷朋"显微镜学家', completed: userStats.inviteCount >= 100 },
    { count: 150, reward: '1200灵感奖励', title: '👑"逆爱"金牌制片人', completed: userStats.inviteCount >= 150 },
    { count: 200, reward: '10000灵感奖励', title: '✨"池畏"宿命连接者', completed: userStats.inviteCount >= 200 }
  ];

  // 获取下一个里程碑
  const getNextMilestone = () => {
    return inviteMilestones.find(milestone => !milestone.completed);
  };

  const handleCustomDonate = () => {
    const amount = parseInt(customAmount);
    if (amount && amount > 0) {
      alert(`感谢您的 ¥${amount} 投喂！站长收到了您温暖的爱意 💝`);
    } else {
      alert('请输入有效的金额');
    }
  };

  // 渲染灵感碎片进度
  const renderFragmentProgress = () => {
    const fragments = [];
    for (let i = 0; i < 3; i++) {
      fragments.push(
        <div key={i} style={{
          width: '12px',
          height: '12px',
          borderRadius: '50%',
          background: i < userStats.fragmentBalance ? colors.gold : 'rgba(255, 255, 255, 0.2)',
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '8px',
          margin: '0 2px'
        }}>
          {i < userStats.fragmentBalance ? '✓' : ''}
        </div>
      );
    }
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginTop: '8px' }}>
        <span style={{ fontSize: '0.75rem', color: colors.textLight }}>灵感碎片:</span>
        <div style={{ display: 'flex' }}>{fragments}</div>
        <span style={{ fontSize: '0.75rem', color: colors.textLight }}>({userStats.fragmentBalance}/3)</span>
      </div>
    );
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0F0B27 0%, #1A1A3A 100%)',
      padding: '40px 20px'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 标题 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '8px',
            background: colors.primary,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>为逆线充电</h1>
          <p style={{ fontSize: '1.125rem', color: colors.textLight }}>
            在无数逆转的线里，为爱发电
          </p>
        </div>
        
        {/* 版块化布局 - 关键是这里！*/}
        <div style={{
          display: 'flex',
          gap: '24px',
          marginBottom: '40px',
          flexDirection: isMobile ? 'column' : 'row'  // 响应式
        }}>
          {/* 站长的话 (2/3 宽度) */}
          <div style={{ flex: isMobile ? '1' : '2' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
                <div style={{ fontSize: '2rem' }}>💌</div>
                <h2 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>一封来自站长的信</h2>
              </div>
              
              <div style={{ 
                borderLeft: `3px solid ${colors.purple}`, 
                paddingLeft: '16px',
                marginBottom: '16px'
              }}>
                <h3 style={{ 
                  fontSize: '1rem', 
                  fontWeight: 'bold', 
                  color: colors.gold,
                  marginBottom: '8px'
                }}>嘿，亲爱的"逆线"旅人，</h3>
                <p style={{ 
                  color: colors.text, 
                  lineHeight: '1.6',
                  fontSize: '0.875rem',
                  marginBottom: '12px'
                }}>
                  我是站长。和你们一样，我也是那个为《逆爱》彻夜难眠的<strong style={{ color: colors.gold }}>"兵"</strong>。
                </p>
                <p style={{ 
                  color: colors.text, 
                  lineHeight: '1.6',
                  fontSize: '0.875rem',
                  marginBottom: '12px'
                }}>
                  因为舍不得，因为意难平，我用尽了所有技术和热爱，搭建了这座小小的避风港。每一次AI生成，都在真实地燃烧着服务器和我的头发。
                </p>
                <p style={{ 
                  color: colors.text, 
                  lineHeight: '1.6',
                  fontSize: '0.875rem',
                  marginBottom: '12px'
                }}>
                  <strong style={{ 
                    background: colors.secondary,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontWeight: 'bold'
                  }}>「逆线」的诞生，无关商业，只为热爱</strong>。您的每一份支持，都不是为了让我赚钱，而是化作服务器的一串串数据、AI模型的一次次运算，<strong style={{ color: colors.gold }}>为这个夏天，续写一个永不完结的未来</strong>。
                </p>
                <p style={{ 
                  color: colors.text, 
                  lineHeight: '1.6',
                  fontSize: '0.875rem',
                  marginBottom: '12px'
                }}>
                  当然，也请务必<strong style={{ color: colors.gold }}>量力而行</strong>。哪怕您只是在这里看看，或是签到白嫖，我也同样开心！
                </p>
                <p style={{ 
                  fontSize: '1rem',
                  fontWeight: 'bold',
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: '8px'
                }}>
                  能在这条逆转的线上与你相遇，已是荣幸！
                </p>
                <p style={{ 
                  fontSize: '0.75rem', 
                  color: colors.textDim, 
                  fontStyle: 'italic',
                  textAlign: 'right'
                }}>
                  — 逆线站长 敬上
                </p>
              </div>
            </div>
          </div>
          
          {/* 当前状态 (1/3 宽度) */}
          <div style={{ flex: '1' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '20px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '1.5rem' }}>⚡</div>
                <h3 style={{ 
                  fontSize: '1.125rem', 
                  fontWeight: 'bold', 
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>
                  当前状态
                </h3>
              </div>
              
              <div style={{ space: '12px' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <span style={{ fontSize: '0.875rem', color: colors.textLight }}>可用灵感</span>
                  <span style={{ 
                    fontWeight: 'bold', 
                    fontSize: '1.125rem',
                    background: colors.secondary,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}>{userStats.credits}</span>
                </div>
                
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                  <span style={{ fontSize: '0.875rem', color: colors.textLight }}>星轨召唤券</span>
                  <span style={{ 
                    fontWeight: 'bold', 
                    fontSize: '1.125rem',
                    color: colors.gold
                  }}>{userStats.gachaTickets}张</span>
                </div>
                
                {renderFragmentProgress()}
                
                <div style={{ 
                  marginTop: '12px',
                  padding: '8px',
                  backgroundColor: 'rgba(102, 107, 206, 0.1)',
                  borderRadius: '8px',
                  border: `1px solid rgba(102, 107, 206, 0.2)`
                }}>
                  <div style={{ fontSize: '0.75rem', color: colors.textLight, marginBottom: '4px' }}>
                    累计获得碎片: <span style={{ color: colors.gold, fontWeight: 'bold' }}>{userStats.fragmentCount}枚</span>
                  </div>
                  <div style={{ fontSize: '0.75rem', color: colors.textDim, marginBottom: '2px' }}>
                    💡 连续签到3天兑换10灵感
                  </div>
                  <div style={{ fontSize: '0.75rem', color: colors.textDim }}>
                    🎯 连续签到7天获得1张星轨召唤券
                  </div>
                </div>
                
                {/* 星轨召唤券按钮 */}
                {userStats.gachaTickets > 0 && (
                  <button 
                    onClick={handleGachaRedirect}
                    style={{
                      width: '100%',
                      padding: '10px',
                      marginTop: '12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(255, 214, 79, 0.3)',
                      background: 'rgba(255, 214, 79, 0.1)',
                      color: colors.gold,
                      fontSize: '0.875rem',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 214, 79, 0.2)';
                      e.currentTarget.style.transform = 'translateY(-1px)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = 'rgba(255, 214, 79, 0.1)';
                      e.currentTarget.style.transform = 'translateY(0)';
                    }}
                  >
                    ✨ 立即使用星轨召唤券 ({userStats.gachaTickets}张)
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* 产粮套餐标题 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{ fontSize: '3rem', marginBottom: '12px' }}>🚀</div>
          <h2 style={{
            fontSize: '2rem',
            fontWeight: 'bold',
            marginBottom: '8px',
            background: colors.primary,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>产粮套餐</h2>
          <p style={{ fontSize: '1rem', color: colors.textLight, marginBottom: '8px' }}>
            选择适合你的产粮计划，享受持续创作的乐趣
          </p>
          <p style={{ fontSize: '0.875rem', color: colors.gold, fontWeight: 'bold' }}>
            ⭐ 1章节 = 10灵感 | 10灵感 = 1次AI生成 (约1800字)
          </p>
        </div>
        
        {/* 产粮套餐 */}
        <div style={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          gap: '24px',
          marginBottom: '40px',
          maxWidth: '1000px',
          margin: '0 auto 40px auto'
        }}>
          {/* 执笔者套餐 */}
          <div style={{ flex: '1' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
              textAlign: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* 限时特惠标签 */}
              <div style={{
                position: 'absolute',
                top: '-3px',
                right: '-3px',
                background: colors.orange,
                color: 'white',
                fontSize: '0.75rem',
                fontWeight: 'bold',
                padding: '4px 12px',
                borderRadius: '8px',
                transform: 'rotate(12deg)',
                boxShadow: '0 4px 12px rgba(255, 107, 53, 0.4)'
              }}>限时特惠</div>
              
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                marginBottom: '12px',
                background: colors.secondary,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>产粮·执笔者</h3>
              
              <div style={{ marginBottom: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', marginBottom: '8px' }}>
                  <span style={{ fontSize: '1.25rem', color: colors.textLight, textDecoration: 'line-through' }}>¥12</span>
                  <span style={{ fontSize: '2.5rem', fontWeight: 'bold', color: colors.gold }}>¥6</span>
                  <span style={{ fontSize: '1rem', color: colors.textLight }}>/月</span>
                </div>
                <p style={{ fontSize: '0.75rem', color: colors.orange.replace('linear-gradient(135deg, ', '').replace(')', '').split(' 0%, ')[0], fontWeight: 'bold' }}>
                  🎉 首月限时尝鲜价，次月起恢复 ¥12/月
                </p>
              </div>
              
              <div style={{ 
                backgroundColor: 'rgba(102, 107, 206, 0.1)',
                borderRadius: '12px',
                padding: '16px',
                marginBottom: '16px',
                border: '1px solid rgba(102, 107, 206, 0.2)'
              }}>
                <div style={{ fontSize: '0.875rem', color: colors.text, marginBottom: '8px' }}>
                  📦 <strong>每月权益</strong>
                </div>
                <div style={{ fontSize: '0.875rem', color: colors.text, marginBottom: '6px' }}>
                  • <strong style={{ color: colors.gold }}>3,000 灵感</strong> (可生成 300 章节)
                </div>
                <div style={{ fontSize: '0.875rem', color: colors.text, marginBottom: '6px' }}>
                  • <strong style={{ color: colors.gold }}>1 张</strong> 星轨召唤券
                </div>
                <div style={{ fontSize: '0.875rem', color: colors.text }}>
                  • 解锁专属头衔收藏
                </div>
              </div>
              
              <button style={{
                width: '100%',
                padding: '14px',
                borderRadius: '10px',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                background: colors.secondary,
                color: 'white',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: '0 4px 12px rgba(102, 107, 206, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 24px rgba(102, 107, 206, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.3)';
              }}>
                为爱充电 (¥6)
              </button>
            </div>
          </div>
          
          {/* 太太套餐 */}
          <div style={{ flex: '1' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(255, 107, 53, 0.08)',
              border: '1px solid rgba(255, 107, 53, 0.3)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(255, 107, 53, 0.2)',
              textAlign: 'center',
              position: 'relative',
              overflow: 'hidden'
            }}>
              {/* 热门推荐标签 */}
              <div style={{
                position: 'absolute',
                top: '-3px',
                right: '-3px',
                background: colors.orange,
                color: 'white',
                fontSize: '0.75rem',
                fontWeight: 'bold',
                padding: '4px 12px',
                borderRadius: '8px',
                transform: 'rotate(12deg)',
                boxShadow: '0 4px 12px rgba(255, 107, 53, 0.4)'
              }}>热门推荐</div>
              
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: 'bold',
                marginBottom: '12px',
                background: colors.orange,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>产粮·太太</h3>
              
              <div style={{ marginBottom: '16px' }}>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', marginBottom: '8px' }}>
                  <span style={{ fontSize: '2.5rem', fontWeight: 'bold', color: '#FF6B35' }}>¥30</span>
                  <span style={{ fontSize: '1rem', color: colors.textLight }}>/月</span>
                </div>
              </div>
              
              <div style={{ 
                backgroundColor: 'rgba(255, 107, 53, 0.1)',
                borderRadius: '12px',
                padding: '16px',
                marginBottom: '16px',
                border: '1px solid rgba(255, 107, 53, 0.3)'
              }}>
                <div style={{ fontSize: '0.875rem', color: colors.text, marginBottom: '8px' }}>
                  👑 <strong>尊享权益</strong>
                </div>
                <div style={{ fontSize: '0.875rem', color: colors.text, marginBottom: '6px' }}>
                  • <strong style={{ color: '#FF6B35' }}>10,000 灵感</strong> (可生成 1,000 章节)
                </div>
                <div style={{ fontSize: '0.875rem', color: colors.text, marginBottom: '6px' }}>
                  • <strong style={{ color: '#FF6B35' }}>3 张</strong> 星轨召唤券
                </div>
                <div style={{ fontSize: '0.875rem', color: colors.text }}>
                  • 专属头衔 <strong style={{ color: colors.gold }}>"逆命红线"</strong>
                </div>
              </div>
              
              <button style={{
                width: '100%',
                padding: '14px',
                borderRadius: '10px',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                background: colors.orange,
                color: 'white',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                boxShadow: '0 4px 12px rgba(255, 107, 53, 0.3)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)';
                e.currentTarget.style.boxShadow = '0 8px 24px rgba(255, 107, 53, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)';
                e.currentTarget.style.boxShadow = '0 4px 12px rgba(255, 107, 53, 0.3)';
              }}>
                为爱充电 (¥30)
              </button>
            </div>
          </div>
        </div>
        
        {/* 投喂服务器 + 免费福利 */}
        <div style={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          gap: '24px',
          marginBottom: '40px'
        }}>
          {/* 投喂服务器 */}
          <div style={{ flex: '1' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '2rem' }}>💝</div>
                <h3 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>投喂服务器</h3>
              </div>
              
              <p style={{ 
                fontSize: '0.875rem', 
                color: colors.text, 
                lineHeight: '1.5', 
                marginBottom: '16px' 
              }}>
                此选项不会获得任何"灵感"或道具，只会让站长感受到你温暖的爱意，并把所有资金用于服务器维护。
              </p>
              
              <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                <span style={{ 
                  fontSize: '1.25rem', 
                  fontWeight: 'bold',
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>¥</span>
                <input
                  type="number"
                  min="1"
                  placeholder="输入投喂金额"
                  value={customAmount}
                  onChange={(e) => setCustomAmount(e.target.value)}
                  style={{
                    flex: '1',
                    padding: '10px 12px',
                    borderRadius: '8px',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    color: 'white',
                    fontSize: '0.875rem',
                    outline: 'none'
                  }}
                />
                <button 
                  onClick={handleCustomDonate}
                  style={{
                    padding: '10px 16px',
                    borderRadius: '8px',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    background: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    whiteSpace: 'nowrap',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  立即投喂
                </button>
              </div>
            </div>
          </div>
          
          {/* 免费福利 */}
          <div style={{ flex: '1' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                <div style={{ fontSize: '2rem' }}>🎁</div>
                <h3 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>免费福利</h3>
              </div>
              
              <div style={{ marginBottom: '16px' }}>
                <p style={{ fontSize: '0.875rem', color: colors.textLight, marginBottom: '12px' }}>
                  无需花费，轻松获得灵感奖励
                </p>
                
                <div style={{ space: '12px' }}>
                  {/* 每日重逢 */}
                  <div style={{ 
                    backgroundColor: 'rgba(102, 107, 206, 0.1)',
                    borderRadius: '8px',
                    padding: '12px',
                    marginBottom: '12px',
                    border: '1px solid rgba(102, 107, 206, 0.2)'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <span style={{ fontSize: '1rem' }}>📅</span>
                      <strong style={{ fontSize: '0.875rem', color: colors.gold }}>每日重逢</strong>
                    </div>
                    <p style={{ fontSize: '0.75rem', color: colors.text, lineHeight: '1.4', marginBottom: '8px' }}>
                      每日签到收集灵感碎片，连续签到3天自动兑换10灵感。连续签到7天获得1张星轨召唤券。
                    </p>
                    <button 
                      onClick={handleDailySignin}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '6px',
                        border: '1px solid rgba(255, 255, 255, 0.2)',
                        background: colors.secondary,
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: '600',
                        cursor: 'pointer'
                      }}
                    >
                      立即签到
                    </button>
                  </div>
                  
                  {/* 同好之邀 */}
                  <div style={{ 
                    backgroundColor: 'rgba(255, 214, 79, 0.1)',
                    borderRadius: '8px',
                    padding: '12px',
                    border: '1px solid rgba(255, 214, 79, 0.2)'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                      <span style={{ fontSize: '1rem' }}>👥</span>
                      <strong style={{ fontSize: '0.875rem', color: colors.gold }}>同好之邀</strong>
                    </div>
                    <p style={{ fontSize: '0.75rem', color: colors.text, lineHeight: '1.4', marginBottom: '8px' }}>
                      邀请双方各得30灵感！无限时！
                    </p>
                    <button 
                      onClick={handleInviteToggle}
                      style={{
                        padding: '6px 12px',
                        borderRadius: '6px',
                        border: '1px solid rgba(255, 214, 79, 0.3)',
                        background: 'rgba(255, 214, 79, 0.1)',
                        color: colors.gold,
                        fontSize: '0.75rem',
                        fontWeight: '600',
                        cursor: 'pointer'
                      }}
                    >
                      {showInviteCode ? '隐藏邀请码' : '查看邀请码'}
                    </button>
                    
                    {showInviteCode && (
                      <div style={{ 
                        marginTop: '12px',
                        padding: '12px',
                        backgroundColor: 'rgba(255, 214, 79, 0.05)',
                        borderRadius: '8px',
                        border: '1px solid rgba(255, 214, 79, 0.1)'
                      }}>
                        <div style={{ fontSize: '0.75rem', color: colors.textLight, marginBottom: '6px' }}>
                          您的专属邀请码：
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '8px' }}>
                          <code style={{
                            padding: '4px 8px',
                            backgroundColor: 'rgba(255, 255, 255, 0.1)',
                            borderRadius: '4px',
                            fontSize: '0.75rem',
                            color: colors.gold,
                            fontFamily: 'monospace'
                          }}>
                            {userStats.inviteCode}
                          </code>
                          <button 
                            onClick={() => {
                              navigator.clipboard.writeText(userStats.inviteCode);
                              alert('邀请码已复制到剪贴板');
                            }}
                            style={{
                              padding: '2px 6px',
                              borderRadius: '4px',
                              border: '1px solid rgba(255, 214, 79, 0.3)',
                              background: 'rgba(255, 214, 79, 0.1)',
                              color: colors.gold,
                              fontSize: '0.625rem',
                              cursor: 'pointer'
                            }}
                          >
                            复制
                          </button>
                        </div>
                        <div style={{ fontSize: '0.75rem', color: colors.textLight }}>
                          邀请链接: https://nixian.top/auth?invite={userStats.inviteCode}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 邀请进度里程碑 */}
        <div style={{
          marginBottom: '40px'
        }}>
          <div style={{
            backdropFilter: 'blur(16px)',
            backgroundColor: 'rgba(0, 0, 0, 0.35)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '16px',
            padding: '24px',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '20px' }}>
              <div style={{ fontSize: '2rem' }}>🎯</div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: 'bold',
                background: colors.secondary,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>邀请里程碑奖励</h3>
            </div>
            
            {/* 当前进度 */}
            <div style={{ 
              marginBottom: '20px',
              padding: '16px',
              backgroundColor: 'rgba(102, 107, 206, 0.1)',
              borderRadius: '12px',
              border: '1px solid rgba(102, 107, 206, 0.2)'
            }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                <span style={{ fontSize: '0.875rem', color: colors.textLight }}>当前邀请人数</span>
                <span style={{ 
                  fontWeight: 'bold', 
                  fontSize: '1.25rem',
                  background: colors.secondary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>{userStats.inviteCount}人</span>
              </div>
              
              {/* 下一个里程碑进度条 */}
              {(() => {
                const nextMilestone = getNextMilestone();
                if (nextMilestone) {
                  const progress = (userStats.inviteCount / nextMilestone.count) * 100;
                  return (
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '6px' }}>
                        <span style={{ fontSize: '0.75rem', color: colors.textDim }}>
                          距离下一个里程碑 ({nextMilestone.title})
                        </span>
                        <span style={{ fontSize: '0.75rem', color: colors.textDim }}>
                          {nextMilestone.count - userStats.inviteCount} 人
                        </span>
                      </div>
                      <div style={{ 
                        width: '100%', 
                        height: '8px', 
                        backgroundColor: 'rgba(255, 255, 255, 0.1)', 
                        borderRadius: '4px',
                        overflow: 'hidden'
                      }}>
                        <div style={{ 
                          width: `${Math.min(progress, 100)}%`, 
                          height: '100%', 
                          background: colors.secondary,
                          borderRadius: '4px',
                          transition: 'width 0.3s ease'
                        }}></div>
                      </div>
                    </div>
                  );
                }
                return <div style={{ fontSize: '0.75rem', color: colors.gold, fontWeight: 'bold' }}>🎉 恭喜！已达成所有里程碑！</div>;
              })()}
            </div>
            
            {/* 里程碑列表 */}
            <div style={{ space: '12px' }}>
              {inviteMilestones.map((milestone, index) => (
                <div key={index} style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'space-between',
                  padding: '16px',
                  marginBottom: '12px',
                  backgroundColor: milestone.completed ? 'rgba(76, 175, 80, 0.1)' : 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '12px',
                  border: `1px solid ${milestone.completed ? 'rgba(76, 175, 80, 0.3)' : 'rgba(255, 255, 255, 0.1)'}`
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                    <div style={{ 
                      fontSize: '2rem',
                      opacity: milestone.completed ? 1 : 0.7
                    }}>
                      {milestone.completed ? '✅' : '🎯'}
                    </div>
                    <div>
                      <div style={{ 
                        fontSize: '1rem', 
                        fontWeight: 'bold',
                        color: milestone.completed ? '#4CAF50' : colors.text,
                        marginBottom: '4px'
                      }}>
                        邀请 {milestone.count} 人解锁
                      </div>
                      <div style={{ 
                        fontSize: '0.875rem', 
                        color: milestone.completed ? '#4CAF50' : colors.gold,
                        fontWeight: 'bold'
                      }}>
                        专属头衔：{milestone.title}
                      </div>
                    </div>
                  </div>
                  <div style={{
                    textAlign: 'right'
                  }}>
                    <div style={{ 
                      fontSize: '1.125rem', 
                      fontWeight: 'bold',
                      color: milestone.completed ? '#4CAF50' : colors.gold,
                      marginBottom: '2px'
                    }}>
                      {milestone.completed ? '✓ 已获得' : milestone.reward}
                    </div>
                    <div style={{ 
                      fontSize: '0.75rem', 
                      color: milestone.completed ? '#4CAF50' : colors.textLight
                    }}>
                      {milestone.completed ? '奖励已发放' : '额外奖励'}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
      </div>
    </div>
  );
}