'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';

// 动态导入导航组件
const TopNavigation = dynamic(() => import('@/components/TopNavigation'), { ssr: false });

// CP应援色配色方案 - 与公告页面保持一致
const CP_GRADIENT_FULL = 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)';
const CP_GRADIENT_SIMPLE = 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)';
const BRIGHT_GRADIENT = 'linear-gradient(135deg, #FFD64F 0%, #FF6B35 100%)';
const GOLD_GRADIENT = 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)';
const POPULAR_GRADIENT = 'linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%)';

// CP应援色文字样式
const cpTextLarge = { background: CP_GRADIENT_FULL, WebkitBackgroundClip: 'text' as const, color: 'transparent', fontWeight: 800 as const };
const cpTextMedium = { background: CP_GRADIENT_SIMPLE, WebkitBackgroundClip: 'text' as const, color: 'transparent', fontWeight: 700 as const };
const cpTextSmall = { background: CP_GRADIENT_SIMPLE, WebkitBackgroundClip: 'text' as const, color: 'transparent', fontWeight: 600 as const };
const cpBodyColor = '#323272';

// 金色渐变用于章节数字
const goldTextStyle = { background: GOLD_GRADIENT, WebkitBackgroundClip: 'text' as const, color: 'transparent', fontWeight: 800 as const };

// 产粮套餐 - 双列布局 (根据新需求修改)
const TRIAL_PACKS = [
  { 
    id: 'writer', 
    price: 12, 
    credits: 300, // 3000灵感 / 10灵感每章节 = 300章节
    title: '产粮·执笔者', 
    desc: '每月3000灵感，送1张星轨召唤券',
    button: '为爱充电 (¥6)', // 上线首月限时尝鲜价 ¥6
    popular: true,
    badge: '限时尝鲜',
    originalPrice: 12,
    discountPrice: 6
  },
  { 
    id: 'patron', 
    price: 30, 
    credits: 1000, // 10000灵感 / 10灵感每章节 = 1000章节
    title: '产粮·太太', 
    desc: '每月10000灵感，送3张星轨召唤券，额外奖励："逆命红线"头衔',
    button: '为爱充电 (¥30)',
    popular: false,
    badge: '热门',
    originalPrice: 30,
    discountPrice: 30
  }
];

// 免费福利 - 优化文案
const FREE_BENEFITS = [
  { 
    id: 'daily', 
    title: '每日重逢', 
    desc: '每日签到可收集1枚灵感碎片，连续签到3天集齐3枚碎片即可自动兑换10灵感。连续签到7天可获取1张星轨召唤券；累计100枚碎片可兑换1张星轨召唤券，并解锁头衔"大宝天天见"', 
    icon: '📅', 
    action: '立即签到' 
  },
  { 
    id: 'invite', 
    title: '同好之邀', 
    desc: '邀请双方各得30灵感！无限时！邀请达到30/60/100/150/200人还有额外灵感和头衔奖励', 
    icon: '👥', 
    action: '查看邀请码' 
  },
];

export default function RechargeClient(){
  const [hydrated, setHydrated] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [selected, setSelected] = useState<{price:number; credits:number; title?:string}|null>(null);
  const [order, setOrder] = useState<any>(null);
  const [screenshotUrl, setScreenshotUrl] = useState('');
  const [uploading, setUploading] = useState(false);
  const [orderNoInput, setOrderNoInput] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [showInviteCode, setShowInviteCode] = useState(false);
  const [orderSubmitted, setOrderSubmitted] = useState(false);
  const router = useRouter();

  useEffect(()=>{ setHydrated(true); },[]);

  useEffect(() => {
    const userData = typeof window!=='undefined' ? localStorage.getItem('user') : null;
    if (userData) setUser(JSON.parse(userData));
    else setUser({ name: '游客', credits: 30, fragmentBalance: 2, inviteCode: 'me86osv658f449d6bc60' });
  }, []);

  const totalCredits = useMemo(() => user?.credits ?? 0, [user]);
  const fragmentBalance = useMemo(() => user?.fragmentBalance ?? 0, [user]);
  const fragmentCount = useMemo(() => user?.fragmentCount ?? 0, [user]);
  const gachaTickets = useMemo(() => user?.gachaTickets ?? 0, [user]);

  // 修改创建订单函数，支持限时价格
  const createOrder = async (price:number, credits:number, title?:string, subscriptionPlan?: 'writer'|'patron') => {
    if (!user?.id) {
      alert('请先登录后再进行充值');
      router.push('/auth');
      return;
    }

    // 使用折扣价格创建订单
    const pack = TRIAL_PACKS.find(p => p.id === (subscriptionPlan || title));
    const actualPrice = pack?.discountPrice !== undefined ? pack.discountPrice : price;

    const payload: any = { amountCny: actualPrice, userId: user.id };
    if (subscriptionPlan) payload.subscriptionPlan = subscriptionPlan; else payload.creditsToAdd = credits;
    
    try {
      console.log('Creating order with payload:', payload);
    const res = await fetch('/api/orders', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
    const data = await res.json();
      console.log('Order API response:', data);
      
      if (data?.ok) { 
        setOrder(data.item); 
        setSelected({ price: actualPrice, credits, title });
        console.log('Order created successfully:', data.item);
      } else {
        console.error('Order creation failed:', data);
        alert(data.error || '创建订单失败，请稍后重试');
      }
    } catch (error) {
      console.error('创建订单失败:', error);
      alert('创建订单失败，请稍后重试');
    }
  };

  async function handleFileChange(fi: File | null){
    if (!fi || !order?.id) return;
    try{
      setUploading(true);
      const fd = new FormData();
      fd.append('file', fi);
      const res = await fetch('/api/uploads/file', { method:'POST', body: fd });
      const j = await res.json();
      if (j?.ok && j.url){
        setScreenshotUrl(j.url);
      } else {
        alert(j?.error||'上传图片失败');
      }
    } finally { setUploading(false); }
  }

  const handleDailySignin = async () => {
    try {
      const u = JSON.parse(localStorage.getItem('user') || 'null');
      if (!u?.id) { alert('请先登录'); location.href = '/auth'; return; }
      const r = await fetch('/api/auth/daily-signin', {
        method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ userId: u.id })
      });
      const j = await r.json();
      if (j?.ok) {
        if (j.alreadySigned) {
          alert('今天已经重逢过啦，明天再来吧！');
        } else if (j.fragmentReward) {
          const message = j.chapterAwarded 
            ? `太棒了！3枚碎片已自动兑换为 1 次免费章节生成机会！`
            : `签到成功！获得 1 枚灵感碎片！再签到${3 - (j.newFragmentBalance || 0)}天即可免费续写一章！`;
          alert(message);
        } else {
          alert(`签到成功，获得 ${j.awarded} 个章节${j.bonus?`（含连续奖励 ${j.bonus} 个章节）`:''}`);
        }
        const nu = { 
          ...(u||{}), 
          credits: (u?.credits||0) + (j.awarded||0),
          fragmentBalance: j.newFragmentBalance || (u?.fragmentBalance||0)
        };
        localStorage.setItem('user', JSON.stringify(nu));
        setUser(nu);
      } else {
        alert(j?.error || j?.message || '签到失败');
      }
    } catch {
      alert('网络异常，请稍后重试');
    }
  };

  const handleInviteAction = (benefitId: string) => {
    if (benefitId === 'invite') {
      setShowInviteCode(!showInviteCode);
    } else if (benefitId === 'daily') {
      handleDailySignin();
    }
  };

  const handleSubmitOrder = async () => {
    if (!orderNoInput.trim()) {
      alert('请输入订单号');
      return;
    }
    
    try {
      setOrderSubmitted(true);
      // 这里可以添加提交订单的API调用
      // 暂时只是显示已提交状态
      alert('订单已提交，站长将尽快处理');
    } catch (error) {
      alert('提交失败，请重试');
      setOrderSubmitted(false);
    }
  };

  // 渲染碎片进度 - 居中显示
  const renderFragmentProgress = () => {
    const fragments = [];
    for (let i = 0; i < 3; i++) {
      fragments.push(
        <span key={i} className={`inline-block w-3 h-3 rounded-full mx-1 ${
          i < fragmentBalance ? 'bg-gradient-to-r from-yellow-400 to-orange-500' : 'bg-gray-300'
        }`}>
          {i < fragmentBalance ? '✓' : ''}
        </span>
      );
    }
    return (
      <div className="flex items-center justify-center text-sm" style={{color: cpBodyColor}}>
        <span className="mr-2">灵感碎片:</span>
        {fragments}
        <span className="ml-2">({fragmentBalance}/3)</span>
      </div>
    );
  };

  // 渲染星轨召唤券
  const renderGachaTickets = () => {
    return (
      <div className="flex items-center justify-center text-sm mt-2" style={{color: cpBodyColor}}>
        <span className="mr-2">星轨召唤券:</span>
        <span className="font-bold" style={cpTextSmall}>{gachaTickets}张</span>
      </div>
    );
  };

  if (!hydrated) return null;

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0F0B27 0%, #1A1A3A 100%)',
      position: 'relative'
    }}>
      <TopNavigation />
      
      {/* 粒子背景 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 214, 79, 0.3) 0%, transparent 50%)',
        pointerEvents: 'none'
      }} />
      
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 16px', paddingTop: '80px', paddingBottom: '32px', position: 'relative', zIndex: 10 }}>
        {/* 页面标题 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <h1 style={{
            fontSize: '2.25rem',
            fontWeight: 'bold',
            marginBottom: '8px',
            background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>为逆线充电</h1>
          <p style={{ fontSize: '1.125rem', color: 'rgba(255, 255, 255, 0.7)' }}>在无数逆转的线里，为爱发电</p>
        </div>
        
        {/* 版块化布局 */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
          marginBottom: '32px'
        }}>
          {/* 第一行：站长的话 + 当前状态 */}
          <div style={{
            display: 'flex',
            gap: '24px',
            flexDirection: 'column'
          }} className="lg:flex-row">
            {/* 站长的话 */}
            <div style={{ flex: '2' }}>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '20px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div className="flex items-center gap-3 mb-4">
                <div className="text-2xl">💌</div>
                <h2 className="text-xl font-bold" style={{
                  background: CP_GRADIENT_SIMPLE,
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>站长的话</h2>
              </div>
              <div className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 rounded-full overflow-hidden" style={{background: CP_GRADIENT_FULL, padding: '2px'}}>
                    <Image 
                      src="/head_portrait.png" 
                      alt="站长头像" 
                      width={60} 
                      height={60} 
                      className="rounded-full"
                    />
                  </div>
                </div>
                <div className="flex-1">
                  <p className="text-sm mb-2" style={{color: 'rgba(255, 255, 255, 0.8)', lineHeight: '1.6'}}>嘿，亲爱的"逆线"旅人，我是站长。和你们一样，我也是那个为《逆爱》彻夜难眠的"兵"。</p>
                  <p className="text-sm mb-2" style={{color: 'rgba(255, 255, 255, 0.8)', lineHeight: '1.6'}}>因为舍不得，因为意难平，我搭建了这座小小的避风港。您的每一份支持都是为这个夏天续写永不完结的未来。</p>
                  <p className="text-xs italic" style={{color: 'rgba(255, 255, 255, 0.6)'}}>— 逆线站长 敬上</p>
                </div>
              </div>
            </div>
            
            {/* 当前状态 */}
            <div style={{ flex: '1' }}>
              <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '20px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div className="flex items-center gap-3 mb-4">
                <div className="text-2xl">⚡</div>
                <h3 className="text-lg font-bold" style={{color: 'rgba(255, 255, 255, 0.9)'}}>当前状态</h3>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm" style={{color: 'rgba(255, 255, 255, 0.7)'}}>可用灵感</span>
                  <span className="font-bold" style={{color: '#FFD64F'}}>{totalCredits}</span>
                </div>
                {renderFragmentProgress()}
                {renderGachaTickets()}
              </div>
            </div>
          </div>
          </div>

        {/* 产粮套餐 */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <div className="text-4xl mb-2">🚀</div>
            <h2 className="text-2xl font-bold mb-2" style={{
              background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>产粮套餐</h2>
            <p className="text-sm" style={{color: 'rgba(255, 255, 255, 0.7)'}}>选择适合你的产粮计划，享受持续创作的乐趣</p>
          </div>
          
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '24px',
            maxWidth: '1200px',
            margin: '0 auto'
          }} className="md:flex-row">
            {TRIAL_PACKS.map(pack => (
              <div key={pack.id} className="relative">
                {/* 徽章角标 */}
                {pack.badge && (
                  <div className="absolute -top-2 -right-2 z-20 px-2 py-1 rounded-full text-xs font-bold text-white shadow-lg transform rotate-12" style={{
                    background: pack.popular ? BRIGHT_GRADIENT : CP_GRADIENT_SIMPLE,
                    fontSize: '10px'
                  }}>
                    {pack.badge}
                  </div>
                )}
                
                <div style={{
                  backdropFilter: 'blur(16px)',
                  backgroundColor: pack.popular ? 'rgba(255, 107, 53, 0.1)' : 'rgba(0, 0, 0, 0.35)',
                  border: pack.popular ? '1px solid rgba(255, 107, 53, 0.3)' : '1px solid rgba(255, 255, 255, 0.1)',
                  borderRadius: '16px',
                  padding: '24px',
                  boxShadow: pack.popular ? '0 20px 60px rgba(255, 107, 53, 0.2)' : '0 20px 60px rgba(0, 0, 0, 0.3)',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  {/* 背景装饰 */}
                  <div style={{
                    position: 'absolute',
                    top: '-50%',
                    right: '-30%',
                    width: '100px',
                    height: '100px',
                    background: pack.popular ? 'rgba(255, 107, 53, 0.1)' : 'rgba(102, 107, 206, 0.1)',
                    borderRadius: '50%',
                    filter: 'blur(20px)',
                    pointerEvents: 'none'
                  }} />
                  
                  <div className="text-center relative z-10">
                    <h3 className="text-xl font-bold mb-3" style={{
                      background: pack.popular ? POPULAR_GRADIENT : CP_GRADIENT_SIMPLE,
                      WebkitBackgroundClip: 'text',
                      backgroundClip: 'text',
                      WebkitTextFillColor: 'transparent'
                    }}>{pack.title}</h3>
                    
                    <div className="flex items-center justify-center gap-2 mb-3">
                      {pack.originalPrice !== pack.discountPrice && (
                        <div className="text-sm line-through" style={{color: 'rgba(255, 255, 255, 0.5)'}}>¥{pack.originalPrice}</div>
                      )}
                      <div className="text-3xl font-extrabold" style={{
                        background: pack.popular ? POPULAR_GRADIENT : CP_GRADIENT_SIMPLE,
                        WebkitBackgroundClip: 'text',
                        backgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}>¥{pack.discountPrice}</div>
                    </div>
                    
                    <p className="text-sm mb-4" style={{color: 'rgba(255, 255, 255, 0.8)', lineHeight: '1.5'}}>{pack.desc}</p>
                    
                    {/* 套餐描述 */}
                    <div className="bg-black/20 rounded-lg p-3 mb-4 text-left">
                      <p className="text-xs" style={{color: 'rgba(255, 255, 255, 0.8)', lineHeight: '1.4'}}>
                        {pack.id === 'writer' ? (
                          '选择此方案，成为故事的执笔者。为了感谢你的支持，你的账户每月将收到 3,000 灵感的创作基金，以及 1 张用于召唤稀有头衔的"星轨召唤券"作为谢礼。'
                        ) : (
                          '选择此方案，成为社区的守护神。为了感谢你的卓越贡献，你的账户每月将收到 10,000 灵感的创作基金和 3 张"星轨召唤券"。同时，你将立刻获得一枚【逆命红线】。'
                        )}
                      </p>
                    </div>
                  </div>
                  
                  <button
                    onClick={() => createOrder(pack.price, pack.credits, pack.title, pack.id as 'writer' | 'patron')}
                    style={{
                      width: '100%',
                      padding: '12px',
                      borderRadius: '8px',
                      border: '1px solid rgba(255, 255, 255, 0.2)',
                      background: pack.popular ? POPULAR_GRADIENT : 'rgba(255, 255, 255, 0.1)',
                      color: 'white',
                      fontSize: '14px',
                      fontWeight: '600',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      position: 'relative',
                      zIndex: 10
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)';
                      e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                  >
                    {pack.button}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 纯投喂和免费福利 */}
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '24px',
          marginBottom: '32px'
        }} className="lg:flex-row">
          {/* 纯投喂选项 */}
          <div>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '20px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div className="flex items-center gap-3 mb-4">
                <div className="text-2xl">💝</div>
                <h3 className="text-lg font-bold" style={{color: 'rgba(255, 255, 255, 0.9)'}}>投喂服务器</h3>
              </div>
              <p className="text-sm mb-4" style={{color: 'rgba(255, 255, 255, 0.7)', lineHeight: '1.5'}}>此选项不会获得任何"灵感"或道具，只会让站长感受到你温暖的爱意，并把所有资金用于服务器维护。</p>
              
              <div className="flex items-center gap-3">
                <span className="text-xl font-bold" style={{
                  background: CP_GRADIENT_SIMPLE,
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>¥</span>
                <input
                  type="number"
                  min="1"
                  placeholder="输入投喂金额"
                  className="flex-1 px-3 py-2 rounded-lg border border-white/20 bg-black/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      const value = parseInt(e.currentTarget.value);
                      if (value && value > 0) {
                        createOrder(value, 0, '纯投喂');
                      } else {
                        alert('请输入有效的金额');
                      }
                    }
                  }}
                />
                <button 
                  style={{
                    padding: '8px 16px',
                    borderRadius: '8px',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    background: 'rgba(255, 255, 255, 0.1)',
                    color: 'white',
                    fontSize: '14px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onClick={(e) => {
                    const input = e.currentTarget.parentElement?.querySelector('input');
                    if (input) {
                      const value = parseInt(input.value);
                      if (value && value > 0) {
                        createOrder(value, 0, '纯投喂');
                      } else {
                        alert('请输入有效的金额');
                      }
                    }
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  立即投喂
                </button>
              </div>
            </div>
          </div>
          
          {/* 免费福利 */}
          <div>
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '20px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
            }}>
              <div className="flex items-center gap-3 mb-4">
                <div className="text-2xl">🎁</div>
                <h3 className="text-lg font-bold" style={{color: 'rgba(255, 255, 255, 0.9)'}}>免费福利</h3>
              </div>
              
              <div className="space-y-3">
                {FREE_BENEFITS.map(benefit => (
                  <div key={benefit.id} className="bg-black/20 rounded-lg p-3">
                    <div className="flex items-start justify-between gap-3">
                      <div className="flex items-start gap-3 flex-1">
                        <div className="text-lg flex-shrink-0">{benefit.icon}</div>
                        <div className="flex-1">
                          <div className="font-semibold mb-1" style={{
                            background: CP_GRADIENT_SIMPLE,
                            WebkitBackgroundClip: 'text',
                            backgroundClip: 'text',
                            WebkitTextFillColor: 'transparent',
                            fontSize: '14px'
                          }}>{benefit.title}</div>
                          <div className="text-xs leading-relaxed" style={{color: 'rgba(255, 255, 255, 0.7)'}}>{benefit.desc}</div>
                          
                          {benefit.id === 'daily' && (
                            <div className="mt-2 space-y-2">
                              <div className="flex justify-between text-xs">
                                <span style={{color: 'rgba(255, 255, 255, 0.6)'}}>累计碎片:</span>
                                <span style={{color: '#666BCE'}}>{user?.fragmentCount || 0}枚</span>
                              </div>
                              <div className="flex justify-between text-xs">
                                <span style={{color: 'rgba(255, 255, 255, 0.6)'}}>可用碎片:</span>
                                <span style={{color: '#666BCE'}}>{fragmentBalance}枚</span>
                              </div>
                            </div>
                          )}
                          
                          {benefit.id === 'invite' && showInviteCode && (
                            <div className="mt-3 p-2 bg-purple-50/10 rounded border border-purple-200/20">
                              <div className="text-xs mb-1" style={{color: 'rgba(255, 255, 255, 0.7)'}}>您的专属邀请码：</div>
                              <div className="flex items-center gap-2">
                                <code className="px-2 py-1 bg-white/10 rounded text-xs font-mono" style={{color: '#C2A8F2'}}>
                                  {user?.inviteCode || 'me86osv658f449d6bc60'}
                                </code>
                                <button 
                                  className="text-xs px-2 py-1 rounded bg-white/10 hover:bg-white/20"
                                  style={{color: 'rgba(255, 255, 255, 0.8)'}}
                                  onClick={() => {
                                    navigator.clipboard.writeText(user?.inviteCode || 'me86osv658f449d6bc60');
                                    alert('邀请码已复制到剪贴板');
                                  }}
                                >
                                  复制
                                </button>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                      <button 
                        onClick={() => handleInviteAction(benefit.id)}
                        style={{
                          padding: '6px 12px',
                          borderRadius: '6px',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          background: 'rgba(255, 255, 255, 0.1)',
                          color: 'white',
                          fontSize: '12px',
                          fontWeight: '600',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                          whiteSpace: 'nowrap'
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
                        }}
                      >
                        {benefit.action}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}