'use client';

import { useEffect, useMemo, useState } from 'react';
import Image from 'next/image';

const BRAND_GRADIENT = 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)';

// 按 1.5 元 = 10 灵感 计算基础额度，并保留约 10% 的加赠
const PACKS = [
  { id: 'p6', price: 6, credits: 40, bonus: 0, tag: '' },                 // 6 / 1.5 * 10 = 40
  { id: 'p30', price: 30, credits: 200, bonus: 20, tag: '推荐' },          // 30 / 1.5 * 10 = 200，+10%
  { id: 'p98', price: 98, credits: 650, bonus: 65, tag: '' },              // 98 / 1.5 * 10 ≈ 653 → 650，+10%
  { id: 'p328', price: 328, credits: 2180, bonus: 220, tag: '大R' },       // 328 / 1.5 * 10 ≈ 2187 → 2180，+约10%
];

// 按输出长度分级已取消，统一为深度章节（约3000字，消耗10灵感）

const PASSES = [
  { id: 'm', key: 'month', title: '月卡', price: 30, credits: 200, desc: '加赠50 灵感（次月自动发）' },
  { id: 'q', key: 'quarter', title: '季卡', price: 88, credits: 600, desc: '加赠60 灵感/月（分3月发）' },
  { id: 'y', key: 'year', title: '年卡', price: 328, credits: 2500, desc: '加赠80 灵感/月（分12月发）' },
] as const;

export default function RechargePage() {
  const [user, setUser] = useState<any>(null);
  const [selected, setSelected] = useState<{price:number; credits:number; title?:string}|null>(null);
  const [order, setOrder] = useState<any>(null);
  const [screenshotUrl, setScreenshotUrl] = useState('');

  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) setUser(JSON.parse(userData));
    else setUser({ name: '游客', credits: 30 });
  }, []);

  const totalCredits = useMemo(() => user?.credits ?? 0, [user]);

  const createOrder = async (price:number, credits:number, title?:string, subscriptionPlan?: 'month'|'quarter'|'year') => {
    const payload: any = { amountCny: price, userId: user?.id || 'guest' };
    if (subscriptionPlan) payload.subscriptionPlan = subscriptionPlan; else payload.creditsToAdd = credits;
    const res = await fetch('/api/orders', { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify(payload) });
    const data = await res.json();
    if (data?.ok) { setOrder(data.item); setSelected({ price, credits, title }); }
  };

  const uploadScreenshot = async () => {
    if (!order?.id || !screenshotUrl) return;
    const res = await fetch(`/api/orders/${order.id}/upload`, { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ url: screenshotUrl })});
    const data = await res.json();
    if (data?.ok) alert('已上传凭证，等待审核入账');
  };

  const handleDailySignin = async () => {
    try {
      const u = JSON.parse(localStorage.getItem('user') || 'null');
      if (!u?.id) { alert('请先登录'); location.href = '/auth'; return; }
      const r = await fetch('/api/auth/daily-signin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId: u.id })
      });
      const j = await r.json();
      if (j?.ok) {
        alert(j.alreadySigned ? '今日已签到' : `签到成功，获得 ${j.awarded}${j.bonus?`（含连续奖励 ${j.bonus}）`:''}`);
        // 可选：本地更新余额
        const nu = { ...(u||{}), credits: (u?.credits||0) + (j.alreadySigned?0:(j.awarded||0)) };
        localStorage.setItem('user', JSON.stringify(nu));
        setUser(nu);
      } else {
        alert(j?.error || j?.message || '签到失败');
      }
    } catch {
      alert('网络异常，请稍后重试');
    }
  };

  return (

  const handleDailySignin = async () => {
    try {
      const u = JSON.parse(localStorage.getItem("user") || "null");
      if (!u||!u.id) { alert("请先登录"); location.href = "/auth"; return; }
      const r = await fetch("/api/auth/daily-signin", { method: "POST", headers: { "Content-Type": "application/json" }, body: JSON.stringify({ userId: u.id }) });
      const j = await r.json();
      if (j && j.ok) { alert(j.alreadySigned ? "今日已签到" : `签到成功，获得 ${j.awarded}${j.bonus?`（含连续奖励 ${j.bonus}）`:}`); const nu = { ...(u||{}), credits: (u.credits||0) + (j.alreadySigned?0:(j.awarded||0)) }; localStorage.setItem("user", JSON.stringify(nu)); }
      else { alert((j && (j.error||j.message)) || "签到失败"); }
    } catch(e) { alert("网络异常，请稍后重试"); }
  };

    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <header className="max-w-6xl mx-auto px-4 py-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Image src="/logo-final.svg" alt="逆线" width={56} height={56} />
            <h1 className="text-3xl font-extrabold bg-gradient-to-r from-[#666BCE] to-[#FFD64F] bg-clip-text text-transparent">为逆线充电</h1>
          </div>
                      <div className="text-right">
            <div className="text-gray-700">当前灵感</div>
            <div className="text-2xl font-bold" style={{ background: BRAND_GRADIENT, WebkitBackgroundClip: 'text', color: 'transparent' }}>{totalCredits}</div>
          </div>
        </div>
      </header>

      <main className="max-w-6xl mx-auto px-4 pb-16">
        {/* 当前创作规格说明 */}
        <section className="mb-10">
          <div className="rounded-2xl p-5 bg-white shadow-sm border">
            <div className="text-lg font-bold text-gray-800">当前创作规格</div>
            <div className="text-sm text-gray-600 mt-1">深度章节 · 约3000字 · 每次消耗 <span className="font-semibold text-[#3D4087]">10</span> 灵感</div>
          </div>
        </section>

                  {/* 灵感充值包 */}
        <section className="mb-10">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">灵感充值</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {PACKS.map((p) => (
            <div key={p.id} className="relative rounded-2xl p-5 bg-white shadow-sm border hover:shadow-md transition-all">
              {p.tag ? <div className="absolute -top-2 right-3 text-xs px-2 py-1 rounded-full text-white" style={{background:'#666BCE'}}>{p.tag}</div> : null}
              <div className="text-3xl font-extrabold mb-2" style={{ background: BRAND_GRADIENT, WebkitBackgroundClip: 'text', color: 'transparent' }}>¥ {p.price}</div>
              <div className="text-gray-700 mb-1">{p.credits} 灵感</div>
              {p.bonus ? <div className="text-xs inline-block px-2 py-1 rounded-full text-[#3D4087]" style={{background:'#E8EAF6'}}>加赠 {p.bonus} 灵感</div> : <div className="h-6" />}
              <div className="mt-4 text-right">
                <button onClick={() => createOrder(p.price, p.credits + p.bonus, `灵感包¥${p.price}`)} className="px-4 py-2 rounded-lg text-white hover:opacity-90" style={{background:'#666BCE'}}>立即充值</button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 月卡特权 */}
      <section className="mb-10">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">月卡特权</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {PASSES.map(p => (
            <div key={p.id} className="rounded-2xl p-5 bg-white shadow-sm border hover:shadow-md transition-all">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-lg font-bold text-gray-800">{p.title}</div>
                  <div className="text-sm text-gray-600">{p.credits} 灵感</div>
                  <div className="text-xs text-[#3D4087] mt-1">{p.desc}</div>
                </div>
                <div className="text-2xl font-extrabold" style={{ background: BRAND_GRADIENT, WebkitBackgroundClip: 'text', color: 'transparent' }}>¥ {p.price}</div>
              </div>
              <div className="mt-4 text-right">
                <button onClick={() => createOrder(p.price, p.credits, p.title, p.key)} className="px-4 py-2 rounded-lg text-white hover:opacity-90" style={{background:'#C2A8F2'}}>立即开通</button>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 灵感的无尽源泉 */}
      <section className="mb-10">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">灵感的无尽源泉</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="rounded-2xl p-5 bg-white shadow-sm border hover:shadow-md transition-all">
            <div className="text-lg font-bold text-gray-800">初遇之礼</div>
            <div className="text-sm text-gray-600 mt-1">注册即赠 <span className="font-semibold text-[#3D4087]">30</span> 点灵感</div>
            <div className="text-xs text-gray-500 mt-2">新用户首次注册立即到账</div>
          </div>
          <div className="rounded-2xl p-5 bg-white shadow-sm border hover:shadow-md transition-all">
            <div className="text-lg font-bold text-gray-800">每日重逢</div>
            <div className="text-sm text-gray-600 mt-1">每日签到可领 <span className="font-semibold text-[#3D4087]">10</span> 点灵感</div>
            <div className="text-xs text-gray-500 mt-2">连续打卡有惊喜加赠</div>
            <div className="mt-4 text-right">
              <button onClick={handleDailySignin} className="px-4 py-2 rounded-lg text-white hover:opacity-90" style={{background:'#666BCE'}}>去签到</button>
            </div>
          </div>
          <div className="rounded-2xl p-5 bg-white shadow-sm border hover:shadow-md transition-all">
            <div className="text-lg font-bold text-gray-800">同好之邀</div>
            <div className="text-sm text-gray-600 mt-1">邀请好友注册，你和TA各得 <span className="font-semibold text-[#3D4087]">30</span> 点灵感</div>
            <div className="text-xs text-gray-500 mt-1">活动截止：2025-08-12</div>
            <div className="mt-3 text-xs text-gray-500">你的邀请码：<span className="font-mono text-gray-700">{user?.id || '登录查看'}</span></div>
            <div className="mt-2 flex gap-2">
              <button onClick={()=>{ const link = `${location.origin}/auth?invite=${encodeURIComponent(user?.id||'')}`; navigator.clipboard.writeText(link); alert('邀请链接已复制'); }} disabled={!user?.id} className={`px-3 py-2 rounded-lg text-white ${user?.id? 'hover:opacity-90':''}`} style={{background:'#C2A8F2'}}>复制邀请链接</button>
              <button onClick={()=>{ const code = user?.id||''; if (!code) { alert('请先登录'); return; } navigator.clipboard.writeText(code); alert('邀请码已复制'); }} className="px-3 py-2 rounded-lg text-white hover:opacity-90" style={{background:'#FFD64F', color:'#3D4087'}}>复制邀请码</button>
            </div>
          </div>
        </div>
      </section>

      {/* 支付弹窗 */}
      {selected && order && (
        <div className="fixed inset-0 bg-black/40 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-xl p-6 w-[560px]">
            <div className="flex items-center justify-between mb-3">
              <div className="text-xl font-bold text-gray-800">订单确认</div>
              <button onClick={() => { setSelected(null); setOrder(null); }} className="text-gray-500 hover:text-gray-700">✕</button>
            </div>
            <div className="text-3xl font-extrabold mb-2" style={{ background: BRAND_GRADIENT, WebkitBackgroundClip: 'text', color: 'transparent' }}>¥ {selected.price.toFixed(2)}</div>
            <div className="text-sm text-gray-600 mb-1">订单号：{order.id}</div>
            {order?.payAmountCny ? (
              <div className="text-sm text-gray-700 mb-1">建议支付金额：<span className="font-semibold">¥ {order.payAmountCny.toFixed(2)}</span></div>
            ) : null}
            {order?.orderCode ? (
              <div className="text-sm text-gray-700 mb-4">备注校验码：<span className="font-semibold">{order.orderCode}</span>（请在支付宝备注此码便于核对）</div>
            ) : <div className="mb-4"/>}
            <div className="text-sm text-gray-600 mb-4">请支付时备注订单号，便于快速审核</div>
                          <div className="rounded-xl p-4 bg-gradient-to-r from-[#666BCE] to-[#FFD64F] text-white text-center mb-4">
              <div className="text-sm opacity-90">支付宝收款码</div>
              <div className="mt-2 flex items-center justify-center">
                <img src="/api/pay/qrcode" alt="支付宝收款码" className="w-40 h-40 object-cover rounded-lg bg-white" />
              </div>
            </div>
            <ol className="text-sm text-gray-700 list-decimal list-inside space-y-1 mb-3">
              <li>请使用支付宝扫描上方二维码，按“建议支付金额”支付。</li>
              <li>支付时备注订单号或备注校验码，便于核对。</li>
              <li>支付成功后，请将支付成功凭证截图并粘贴图床链接上传。</li>
              <li>我们将在审核后为您入账灵感，请稍作等待。</li>
            </ol>
            <div className="flex gap-2 flex-wrap items-center">
              <input value={screenshotUrl} onChange={e=>setScreenshotUrl(e.target.value)} placeholder="粘贴支付截图的图床URL（必填）" className="flex-1 border rounded-lg px-3 py-2" />
              <button onClick={uploadScreenshot} className="px-4 py-2 rounded-lg text-white hover:opacity-90" style={{background:'#666BCE'}}>上传支付凭证</button>
              <div className="text-sm text-gray-600">已上传凭证后请耐心等待审核，到账后本页会自动更新余额或您可前往“个人中心”查看。</div>
            </div>
          </div>
        </div>
      )}
      </main>
    </div>
  );
} 
