'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// 动态导入导航组件
const TopNavigation = dynamic(() => import('@/components/TopNavigation'), { ssr: false });

export default function SimpleRechargeTest() {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg breakpoint
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0F0B27 0%, #1A1A3A 100%)',
      position: 'relative'
    }}>
      <TopNavigation />
      
      {/* 粒子背景 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 214, 79, 0.3) 0%, transparent 50%)',
        pointerEvents: 'none'
      }} />
      
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '80px 20px 40px',
        position: 'relative',
        zIndex: 10
      }}>
        {/* 标题 */}
        <div style={{ textAlign: 'center', marginBottom: '40px' }}>
          <h1 style={{
            fontSize: '2.5rem',
            fontWeight: 'bold',
            marginBottom: '8px',
            background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
            WebkitBackgroundClip: 'text',
            backgroundClip: 'text',
            WebkitTextFillColor: 'transparent'
          }}>为逆线充电</h1>
          <p style={{ fontSize: '1.125rem', color: 'rgba(255, 255, 255, 0.7)' }}>
            在无数逆转的线里，为爱发电
          </p>
        </div>
        
        {/* 第一行：站长的话 + 当前状态 */}
        <div style={{
          display: 'flex',
          gap: '24px',
          marginBottom: '40px',
          flexDirection: isMobile ? 'column' : 'row'
        }}>
          {/* 站长的话 */}
          <div style={{ flex: isMobile ? '1' : '2' }}>
              <div style={{
                backdropFilter: 'blur(16px)',
                backgroundColor: 'rgba(0, 0, 0, 0.35)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '16px',
                padding: '24px',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                  <div style={{ fontSize: '1.5rem' }}>💌</div>
                  <h2 style={{
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    background: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
                    WebkitBackgroundClip: 'text',
                    backgroundClip: 'text',
                    WebkitTextFillColor: 'transparent'
                  }}>站长的话</h2>
                </div>
                <p style={{ 
                  color: 'rgba(255, 255, 255, 0.8)', 
                  lineHeight: '1.6',
                  fontSize: '0.875rem' 
                }}>
                  嘿，亲爱的"逆线"旅人，我是站长。和你们一样，我也是那个为《逆爱》彻夜难眠的"兵"。
                  因为舍不得，因为意难平，我搭建了这座小小的避风港。您的每一份支持都是为这个夏天续写永不完结的未来。
                </p>
              </div>
            </div>
            
            {/* 当前状态 */}
            <div style={{ flex: isMobile ? '1' : '1' }}>
              <div style={{
                backdropFilter: 'blur(16px)',
                backgroundColor: 'rgba(0, 0, 0, 0.35)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '16px',
                padding: '24px',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
                  <div style={{ fontSize: '1.5rem' }}>⚡</div>
                  <h3 style={{ fontSize: '1.125rem', fontWeight: 'bold', color: 'rgba(255, 255, 255, 0.9)' }}>
                    当前状态
                  </h3>
                </div>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.7)' }}>可用灵感</span>
                  <span style={{ fontWeight: 'bold', color: '#FFD64F' }}>123</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 产粮套餐 */}
        <div style={{ marginBottom: '40px' }}>
          <div style={{ textAlign: 'center', marginBottom: '32px' }}>
            <div style={{ fontSize: '2.5rem', marginBottom: '8px' }}>🚀</div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              marginBottom: '8px',
              background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>产粮套餐</h2>
            <p style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.7)' }}>
              选择适合你的产粮计划，享受持续创作的乐趣
            </p>
          </div>
          
          <div style={{
            display: 'flex',
            flexDirection: isMobile ? 'column' : 'row',
            gap: '24px',
            maxWidth: '1000px',
            margin: '0 auto'
          }}>
            {/* 套餐1 */}
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
              position: 'relative'
            }}>
              <div style={{ textAlign: 'center' }}>
                <h3 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  marginBottom: '12px',
                  background: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>产粮·执笔者</h3>
                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#FFD64F', marginBottom: '8px' }}>¥6</div>
                <p style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.8)', marginBottom: '16px' }}>
                  每月3000灵感，送1张星轨召唤券
                </p>
                <button style={{
                  width: '100%',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}>
                  为爱充电 (¥6)
                </button>
              </div>
            </div>
            
            {/* 套餐2 */}
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(255, 107, 53, 0.1)',
              border: '1px solid rgba(255, 107, 53, 0.3)',
              borderRadius: '16px',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(255, 107, 53, 0.2)',
              position: 'relative'
            }}>
              <div style={{ textAlign: 'center' }}>
                <h3 style={{
                  fontSize: '1.25rem',
                  fontWeight: 'bold',
                  marginBottom: '12px',
                  background: 'linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}>产粮·太太</h3>
                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#FF6B35', marginBottom: '8px' }}>¥30</div>
                <p style={{ fontSize: '0.875rem', color: 'rgba(255, 255, 255, 0.8)', marginBottom: '16px' }}>
                  每月10000灵感，送3张星轨召唤券，额外奖励："逆命红线"头衔
                </p>
                <button style={{
                  width: '100%',
                  padding: '12px',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  background: 'linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%)',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}>
                  为爱充电 (¥30)
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}