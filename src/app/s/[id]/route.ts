import { NextRequest, NextResponse } from 'next/server';
import { readJson, writeJson } from '@/lib/persistence/jsondb';

const FILE = 'shortlinks.json';

type ShortLink = { id: string; url: string; userId?: string; createdAt: string; hits: number };

export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try{
    const { id } = await params;
    const list = await readJson<ShortLink[]>(FILE, []);
    const idx = list.findIndex(x=>x.id===id);
    if (idx === -1) return NextResponse.redirect(new URL('/', req.url));
    list[idx].hits = (list[idx].hits||0) + 1;
    await writeJson(FILE, list);
    return NextResponse.redirect(list[idx].url);
  }catch{
    return NextResponse.redirect(new URL('/', req.url));
  }
} 