'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Heart, MessageCircle, TrendingUp, Lightbulb, Rocket, Users, Target, Star, Plus } from 'lucide-react';

// CP应援色配置
const cpColors = {
  primary: '#666BCE',
  secondary: '#C2A8F2', 
  accent: '#FFD64F',
  gradient: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
  glow: 'rgba(102, 107, 206, 0.3)',
  glowAccent: 'rgba(255, 214, 79, 0.3)'
};

// 官方蓝图数据
const OFFICIAL_BLUEPRINTS = [
  {
    id: 'info_stream',
    icon: '📡',
    name: '全时空信息流',
    description: '告别在各处平台大海捞针的日子。一个专属情报中心将7x24小时为你自动追踪、聚合所有公开动态。你将以上帝视角，从容品味每一颗新鲜的糖。',
    votes: 127,
    comments: 23
  },
  {
    id: 'creation_engine',
    icon: '✍️',
    name: '现实交错创作引擎',
    description: '将每一次"磕上头"的激动，无缝转化为创作的洪流。当现实中爆发大糖，引擎会自动发布限时挑战，让你带着最新鲜的感动，为心动加冕。',
    votes: 156,
    comments: 31
  },
  {
    id: 'emotion_analyzer',
    icon: '🔬',
    name: '情感共振分析仪',
    description: '让AI成为你的"嗑学拍档"。将任何图文、视频投入其中，它就会为你标记出那些一闪而过的眼神交汇、下意识的肢体靠近和话语中的情感暗流。',
    votes: 89,
    comments: 18
  },
  {
    id: 'divine_gallery',
    icon: '🏛️',
    name: '神图光影库',
    description: '一个由我们共同构筑的、圈内最全的"神图"圣殿。你可以在此随心检索、收藏每一张高清美图，并打造专属于你的"云端心动博物馆"。',
    votes: 203,
    comments: 45
  }
];

// 用户灵感分类
const INSPIRATION_CATEGORIES = [
  { id: 'prompt', name: 'Prompt建议', icon: '💡', color: cpColors.primary },
  { id: 'feature', name: '功能建议', icon: '⚙️', color: cpColors.secondary },
  { id: 'ui', name: 'UI优化', icon: '🎨', color: cpColors.accent },
  { id: 'other', name: '其他想法', icon: '✨', color: '#10B981' }
];

interface UserInspiration {
  id: string;
  title: string;
  content: string;
  category: string;
  author: string;
  votes: number;
  comments: number;
  createdAt: string;
}

export default function FeedbackPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('blueprints');
  const [inspirations, setInspirations] = useState<UserInspiration[]>([]);
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [blueprintVotes, setBlueprintVotes] = useState<Record<string, boolean>>({});
  const [inspirationVotes, setInspirationVotes] = useState<Record<string, boolean>>({});

  useEffect(() => {
    // 模拟用户灵感数据
    setInspirations([
      {
        id: '1',
        title: '增加更多真人宇宙的甜蜜日常prompt',
        content: '希望能有更多关于日常生活中的小互动，比如一起看电影、逛街、做饭等温馨场景的prompt模板。',
        category: 'prompt',
        author: '月下守护者_abc123',
        votes: 45,
        comments: 12,
        createdAt: '2024-01-15'
      },
      {
        id: '2',
        title: '添加作品收藏分类功能',
        content: '建议在收藏夹中增加自定义分类功能，可以按照不同CP、不同类型来整理收藏的作品。',
        category: 'feature',
        author: '心动体验官_def456',
        votes: 32,
        comments: 8,
        createdAt: '2024-01-14'
      }
    ]);
  }, []);

  const handleBlueprintVote = (blueprintId: string) => {
    setBlueprintVotes(prev => ({
      ...prev,
      [blueprintId]: !prev[blueprintId]
    }));
  };

  const handleInspirationVote = (inspirationId: string) => {
    setInspirationVotes(prev => ({
      ...prev,
      [inspirationId]: !prev[inspirationId]
    }));
  };

  return (
    <div className="min-h-screen" style={{
      background: `linear-gradient(135deg, ${cpColors.primary}08 0%, ${cpColors.secondary}05 50%, ${cpColors.accent}08 100%)`
    }}>
      {/* 头部 */}
      <div className="bg-white/95 backdrop-blur-md border-b border-gray-200 sticky top-0 z-20">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-black mb-2" style={{ 
                textShadow: `0 0 30px ${cpColors.glow}`,
                background: cpColors.gradient,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}>
                🚀 「逆线」共创发射台
              </h1>
              <p className="text-lg" style={{ 
                color: cpColors.secondary,
                textShadow: `0 0 10px ${cpColors.glow}` 
              }}>
                与我们一起构建更美好的逆线宇宙
              </p>
            </div>
            <button 
              onClick={() => router.push('/')}
              className="px-6 py-3 rounded-xl bg-white font-semibold hover:scale-105 transition-all duration-300 shadow-lg"
              style={{
                color: cpColors.primary,
                boxShadow: `0 0 20px ${cpColors.glow}`
              }}
            >
              返回首页
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 导航标签 */}
        <div className="flex justify-center mb-8">
          <div className="bg-white/20 backdrop-blur-md rounded-2xl p-2 flex space-x-2" style={{
            border: '1px solid rgba(255,255,255,0.3)',
            boxShadow: `0 0 30px ${cpColors.glow}`
          }}>
            {[
              { id: 'blueprints', label: '🌟 官方蓝图', icon: Target },
              { id: 'inspirations', label: '💡 社区灵感', icon: Lightbulb }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 ${
                  activeTab === tab.id 
                    ? 'scale-105' 
                    : 'hover:scale-105'
                }`}
                style={activeTab === tab.id ? {
                  background: cpColors.gradient,
                  boxShadow: `0 0 25px ${cpColors.glow}`,
                  color: '#FFFFFF',
                  textShadow: '0 0 10px rgba(0,0,0,0.6)'
                } : {
                  color: cpColors.secondary,
                  textShadow: `0 0 10px ${cpColors.glow}`
                }}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* 官方蓝图区 */}
        {activeTab === 'blueprints' && (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4" style={{ 
                color: cpColors.accent,
                textShadow: `0 0 20px ${cpColors.glowAccent}` 
              }}>
                ✨ 官方蓝图 | 等待发射
              </h2>
              <p className="text-xl" style={{ 
                color: cpColors.secondary,
                textShadow: `0 0 15px ${cpColors.glow}` 
              }}>
                为每个蓝图注入能量，告诉我你最期待的未来功能
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {OFFICIAL_BLUEPRINTS.map((blueprint, index) => {
                const isChampion = blueprint.votes === Math.max(...OFFICIAL_BLUEPRINTS.map(b => b.votes));
                
                return (
                <div 
                  key={blueprint.id}
                  className={`group relative overflow-hidden rounded-3xl p-8 transition-all duration-500 hover:scale-105 ${
                    isChampion ? 'md:col-span-2 transform scale-105' : ''
                  }`}
                  style={{
                    background: isChampion 
                      ? 'linear-gradient(135deg, rgba(255,247,230,0.95) 0%, rgba(255,251,235,0.95) 100%)'
                      : 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(247,250,255,0.9) 100%)',
                    border: isChampion 
                      ? `4px solid ${cpColors.accent}` 
                      : `3px solid ${cpColors.primary}40`,
                    boxShadow: isChampion
                      ? `0 20px 60px ${cpColors.glowAccent}, 0 0 40px ${cpColors.glow}, inset 0 0 30px rgba(255,214,79,0.1)`
                      : `0 15px 40px ${cpColors.glow}, 0 0 30px ${cpColors.glowAccent}`
                  }}
                >
                  {/* 最受期待标签 */}
                  {isChampion && (
                    <div className="absolute -top-3 -right-3 px-4 py-2 rounded-full text-sm font-bold text-white shadow-xl transform rotate-12 animate-pulse"
                      style={{
                        background: 'linear-gradient(135deg, #FF6B35 0%, #FF8E53 100%)',
                        boxShadow: '0 8px 25px rgba(255, 107, 53, 0.4)'
                      }}>
                      🔥 最受期待
                    </div>
                  )}

                  {/* 流光边框效果 */}
                  {isChampion && (
                    <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                      <div className="absolute -inset-2 bg-gradient-to-r from-transparent via-yellow-400/30 to-transparent skew-x-12 animate-shimmer"></div>
                    </div>
                  )}
                  <div className="text-center mb-6">
                    <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                      {blueprint.icon}
                    </div>
                    <h3 className="text-2xl font-bold mb-3" style={{ 
                      color: cpColors.primary,
                      textShadow: `0 0 15px ${cpColors.glow}` 
                    }}>
                      {blueprint.name}
                    </h3>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      {blueprint.description.split(/(".*?"|「.*?」|【.*?】)/).map((part, idx) => {
                        const keywords = ['7x24小时', '专属情报中心', '自动追踪', '自动发布限时挑战', '为心动加冕', '嗑学拍档', '任何图文、视频', '下意识', '情感暗流', '神图', '圣殿', '随心检索、收藏', '云端心动博物馆'];
                        const isKeyword = keywords.some(keyword => part.includes(keyword));
                        
                        if (isKeyword) {
                          return (
                            <span key={idx} className="font-bold" style={{
                              color: cpColors.accent,
                              textShadow: `0 0 10px ${cpColors.glowAccent}`
                            }}>
                              {part}
                            </span>
                          );
                        }
                        return part;
                      })}
                    </p>
                  </div>

                  <div className="flex items-center justify-between">
                    <button
                      onClick={() => handleBlueprintVote(blueprint.id)}
                      className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                        blueprintVotes[blueprint.id] ? 'scale-105' : 'hover:scale-105'
                      }`}
                      style={{
                        background: blueprintVotes[blueprint.id] 
                          ? cpColors.gradient 
                          : 'linear-gradient(135deg, rgba(102, 107, 206, 0.1) 0%, rgba(255, 214, 79, 0.1) 100%)',
                        color: blueprintVotes[blueprint.id] ? '#FFFFFF' : cpColors.primary,
                        boxShadow: blueprintVotes[blueprint.id] 
                          ? `0 0 20px ${cpColors.glow}` 
                          : `0 0 10px ${cpColors.glow}`,
                        textShadow: blueprintVotes[blueprint.id] ? '0 0 10px rgba(0,0,0,0.6)' : 'none'
                      }}
                    >
                      <Heart className={`w-5 h-5 ${blueprintVotes[blueprint.id] ? 'fill-current' : ''}`} />
                      <span>注入能量 {blueprint.votes + (blueprintVotes[blueprint.id] ? 1 : 0)}</span>
                    </button>

                    <div className="flex items-center space-x-4 text-gray-600">
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-4 h-4" />
                        <span>{blueprint.comments}</span>
                      </div>
                    </div>
                  </div>
                </div>
                );
              })}
            </div>
          </div>
        )}

        {/* 社区灵感区 */}
        {activeTab === 'inspirations' && (
          <div className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4" style={{ 
                color: cpColors.accent,
                textShadow: `0 0 20px ${cpColors.glowAccent}` 
              }}>
                💡 社区灵感 | 新星孵化
              </h2>
              <p className="text-xl mb-6" style={{ 
                color: cpColors.secondary,
                textShadow: `0 0 15px ${cpColors.glow}` 
              }}>
                分享你的创意想法，让最棒的点子脱颖而出
              </p>

              <button
                onClick={() => setShowSubmitModal(true)}
                className="px-8 py-4 rounded-xl text-white font-bold text-lg hover:scale-105 transition-all duration-300 animate-pulse"
                style={{
                  background: cpColors.gradient,
                  boxShadow: `0 0 25px ${cpColors.glow}`,
                  textShadow: '0 0 10px rgba(0,0,0,0.6)'
                }}
              >
                <Plus className="w-5 h-5 inline mr-2" />
                提交我的新灵感
              </button>
            </div>

            <div className="grid grid-cols-1 gap-6">
              {inspirations.map((inspiration) => (
                <div 
                  key={inspiration.id}
                  className="group relative overflow-hidden rounded-2xl p-6 transition-all duration-300 hover:scale-102"
                  style={{
                    background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(247,250,255,0.9) 100%)',
                    border: `2px solid ${cpColors.primary}30`,
                    boxShadow: `0 10px 30px ${cpColors.glow}`
                  }}
                >
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{
                      background: cpColors.gradient
                    }}>
                      <span className="text-white font-bold">
                        {inspiration.author.charAt(0)}
                      </span>
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-bold" style={{ color: cpColors.primary }}>
                          {inspiration.author}
                        </span>
                        <span className="px-2 py-1 rounded-lg text-xs font-bold" style={{
                          background: INSPIRATION_CATEGORIES.find(c => c.id === inspiration.category)?.color + '20',
                          color: INSPIRATION_CATEGORIES.find(c => c.id === inspiration.category)?.color
                        }}>
                          {INSPIRATION_CATEGORIES.find(c => c.id === inspiration.category)?.name}
                        </span>
                        <span className="text-sm text-gray-500">{inspiration.createdAt}</span>
                      </div>
                      
                      <h3 className="text-lg font-bold mb-2" style={{ color: cpColors.primary }}>
                        {inspiration.title}
                      </h3>
                      
                      <p className="text-gray-700 mb-4 leading-relaxed">
                        {inspiration.content}
                      </p>
                      
                      <div className="flex items-center space-x-6">
                        <button
                          onClick={() => handleInspirationVote(inspiration.id)}
                          className={`flex items-center space-x-2 transition-all duration-300 ${
                            inspirationVotes[inspiration.id] ? 'scale-105' : 'hover:scale-105'
                          }`}
                          style={{
                            color: inspirationVotes[inspiration.id] ? cpColors.accent : cpColors.secondary
                          }}
                        >
                          <TrendingUp className={`w-4 h-4 ${inspirationVotes[inspiration.id] ? 'text-yellow-500' : ''}`} />
                          <span className="font-semibold">
                            顶 {inspiration.votes + (inspirationVotes[inspiration.id] ? 1 : 0)}
                          </span>
                        </button>
                        
                        <div className="flex items-center space-x-1 text-gray-600">
                          <MessageCircle className="w-4 h-4" />
                          <span>{inspiration.comments}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 