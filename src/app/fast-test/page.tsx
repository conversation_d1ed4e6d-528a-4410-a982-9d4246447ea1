'use client';

import React from 'react';
import FastParticleUniverse from '@/components/FastParticleUniverse';

export default function FastTestPage() {
  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      position: 'relative'
    }}>
      <FastParticleUniverse count={8000} />
      
      {/* 主标题 */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        textAlign: 'center',
        zIndex: 100,
        color: 'white'
      }}>
        <h1 style={{
          fontSize: '2.5rem',
          fontWeight: 'bold',
          margin: 0,
          background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          marginBottom: '20px'
        }}>
          🌌 逆线宇宙
        </h1>
        <div style={{
          background: 'rgba(0,0,0,0.7)',
          padding: '15px',
          borderRadius: '10px',
          backdropFilter: 'blur(10px)'
        }}>
          <p style={{ margin: '5px 0', fontSize: '14px' }}>✨ 高性能Three.js粒子系统</p>
          <p style={{ margin: '5px 0', fontSize: '14px' }}>🎨 8000个CP应援色粒子</p>
          <p style={{ margin: '5px 0', fontSize: '12px', color: '#ccc' }}>
            如果快速加载并看到旋转粒子，说明问题已解决！
          </p>
        </div>
      </div>
    </div>
  );
}