'use client';

import React, { useEffect, useState } from 'react';

export default function CleanHomePage() {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div style={{
        width: '100vw',
        height: '100vh',
        background: '#0a0a1a',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white'
      }}>
        <p>正在加载...</p>
      </div>
    );
  }

  // 你的CP应援色粒子
  const particles = Array.from({ length: 200 }, (_, i) => {
    const colors = ['#666BCE', '#C2A8F2', '#FFD64F'];
    const color = colors[i % 3];
    
    return (
      <div
        key={i}
        style={{
          position: 'absolute',
          width: `${Math.random() * 8 + 2}px`,
          height: `${Math.random() * 8 + 2}px`,
          backgroundColor: color,
          borderRadius: '50%',
          left: `${Math.random() * 100}%`,
          top: `${Math.random() * 100}%`,
          animation: `float-${i % 4} ${3 + Math.random() * 7}s infinite ease-in-out`,
          opacity: 0.6 + Math.random() * 0.4,
          boxShadow: `0 0 ${Math.random() * 15 + 5}px ${color}`,
        }}
      />
    );
  });

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* CSS 动画定义 */}
      <style jsx>{`
        @keyframes float-0 {
          0%, 100% { 
            transform: translateY(0px) translateX(0px) rotate(0deg) scale(1); 
          }
          25% { 
            transform: translateY(-30px) translateX(15px) rotate(90deg) scale(1.1); 
          }
          50% { 
            transform: translateY(0px) translateX(30px) rotate(180deg) scale(0.9); 
          }
          75% { 
            transform: translateY(30px) translateX(15px) rotate(270deg) scale(1.05); 
          }
        }
        @keyframes float-1 {
          0%, 100% { 
            transform: translateY(0px) translateX(0px) rotate(0deg); 
          }
          33% { 
            transform: translateY(-20px) translateX(-10px) rotate(120deg); 
          }
          66% { 
            transform: translateY(20px) translateX(25px) rotate(240deg); 
          }
        }
        @keyframes float-2 {
          0%, 100% { 
            transform: translateY(0px) translateX(0px) scale(1); 
          }
          50% { 
            transform: translateY(-40px) translateX(20px) scale(1.2); 
          }
        }
        @keyframes float-3 {
          0%, 100% { 
            transform: translateY(0px) translateX(0px) rotate(0deg); 
          }
          25% { 
            transform: translateY(-15px) translateX(-20px) rotate(45deg); 
          }
          50% { 
            transform: translateY(0px) translateX(-40px) rotate(90deg); 
          }
          75% { 
            transform: translateY(15px) translateX(-20px) rotate(135deg); 
          }
        }
      `}</style>
      
      {particles}
      
      {/* 主标题和说明 */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        textAlign: 'center',
        zIndex: 100,
        color: 'white'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          margin: 0,
          background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          marginBottom: '20px'
        }}>
          🌌 逆线宇宙
        </h1>
        <p style={{
          fontSize: '1.2rem',
          margin: 0,
          opacity: 0.9,
          marginBottom: '30px'
        }}>
          在无数逆转的线里，重逢唯一的你
        </p>
        <div style={{
          background: 'rgba(0,0,0,0.7)',
          padding: '20px',
          borderRadius: '10px',
          backdropFilter: 'blur(10px)',
          fontSize: '14px'
        }}>
          <p style={{ margin: '5px 0' }}>✨ 粒子系统测试成功！</p>
          <p style={{ margin: '5px 0' }}>🎨 CP应援色：紫色 + 紫晶色 + 柠檬黄</p>
          <p style={{ margin: '5px 0' }}>🚀 如果你看到移动的彩色粒子，说明问题已解决</p>
        </div>
      </div>
    </div>
  );
}