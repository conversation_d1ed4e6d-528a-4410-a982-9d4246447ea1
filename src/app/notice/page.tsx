'use client';

import React, { useEffect, useState } from 'react';
import UnifiedLayout from '@/components/UnifiedLayout';
import { 
  UnifiedButton, 
  UnifiedCard, 
  UnifiedInput,
  UnifiedTextarea,
  UnifiedBadge,
  cpColors 
} from '@/components/ui/UnifiedComponents';

type Notice = { 
  id: string; 
  title: string; 
  content: string; 
  createdAt: string; 
  pinned?: boolean; 
};

export default function NoticePage() {
  const [items, setItems] = useState<Notice[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAdmin, setShowAdmin] = useState(false);
  const [token, setToken] = useState('');
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [pinned, setPinned] = useState(false);

  // 轻量 Markdown 渲染
  const renderInline = (text: string, keyPrefix: string) => {
    const parts = text.split(/(\*\*[^*]+\*\*|\*[^*]+\*)/g);
    return parts.map((p, i) => {
      if (p.startsWith('**') && p.endsWith('**')) {
        return (
          <strong 
            key={`${keyPrefix}-b-${i}`} 
            style={{
              background: cpColors.gradient,
              WebkitBackgroundClip: 'text',
              backgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              fontWeight: 'bold'
            }}
          >
            {p.slice(2, -2)}
          </strong>
        );
      }
      if (p.startsWith('*') && p.endsWith('*')) {
        return (
          <em 
            key={`${keyPrefix}-i-${i}`} 
            style={{ 
              color: 'rgba(255, 255, 255, 0.8)', 
              fontStyle: 'italic' 
            }}
          >
            {p.slice(1, -1)}
          </em>
        );
      }
      return (
        <span 
          key={`${keyPrefix}-t-${i}`} 
          style={{ color: 'rgba(255, 255, 255, 0.9)' }}
        >
          {p}
        </span>
      );
    });
  };

  const renderMarkdown = (md: string) => {
    const lines = md.split('\n');
    const blocks: React.ReactNode[] = [];
    let listBuf: string[] = [];
    
    const flushList = () => {
      if (listBuf.length) {
        blocks.push(
          <ul 
            key={`ul-${blocks.length}`} 
            style={{
              listStyle: 'disc',
              paddingLeft: '24px',
              color: 'rgba(255, 255, 255, 0.8)',
              marginBottom: '16px'
            }}
          >
            {listBuf.map((li, idx) => (
              <li key={`li-${idx}`} style={{ marginBottom: '4px' }}>
                {renderInline(li, `li-${idx}`)}
              </li>
            ))}
          </ul>
        );
        listBuf = [];
      }
    };
    
    lines.forEach((raw, idx) => {
      const line = raw.replace(/\r$/, '');
      const m = line.match(/^(#{1,6})\s+(.*)$/);
      
      if (m) {
        flushList();
        const level = m[1].length;
        const text = m[2];
        const children = renderInline(text, `h-${idx}`);
        
        const headingStyle = {
          background: cpColors.gradient,
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          fontWeight: 'bold',
          marginBottom: '12px',
          marginTop: '20px'
        };
        
        if (level === 1) {
          blocks.push(
            <h1 key={`h1-${idx}`} style={{ ...headingStyle, fontSize: '24px' }}>
              {children}
            </h1>
          );
        } else if (level === 2) {
          blocks.push(
            <h2 key={`h2-${idx}`} style={{ ...headingStyle, fontSize: '20px' }}>
              {children}
            </h2>
          );
        } else if (level === 3) {
          blocks.push(
            <h3 key={`h3-${idx}`} style={{ ...headingStyle, fontSize: '18px' }}>
              {children}
            </h3>
          );
        } else {
          blocks.push(
            <div key={`h-${idx}`} style={{ ...headingStyle, fontSize: '16px' }}>
              {children}
            </div>
          );
        }
        return;
      }
      
      if (/^\s*-\s+/.test(line)) {
        listBuf.push(line.replace(/^\s*-\s+/, ''));
        return;
      }
      
      if (/^[-*_]{3,}\s*$/.test(line)) {
        flushList();
        blocks.push(
          <hr 
            key={`hr-${idx}`} 
            style={{
              margin: '24px 0',
              border: 'none',
              borderTop: '2px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '1px'
            }} 
          />
        );
        return;
      }
      
      if (line.trim() === '') {
        flushList();
        blocks.push(<div key={`sp-${idx}`} style={{ height: '12px' }} />);
        return;
      }
      
      flushList();
      blocks.push(
        <p 
          key={`p-${idx}`} 
          style={{
            color: 'rgba(255, 255, 255, 0.9)',
            lineHeight: 1.6,
            marginBottom: '12px'
          }}
        >
          {renderInline(line, `p-${idx}`)}
        </p>
      );
    });
    
    flushList();
    return <div>{blocks}</div>;
  };

  useEffect(() => {
    const t = localStorage.getItem('adminToken') || '';
    if (t) setToken(t);
    
    (async () => {
      try {
        const r = await fetch('/api/notice');
        const j = await r.json();
        setItems(j.items || []);
      } catch (error) {
        console.error('Failed to load notices:', error);
      } finally { 
        setLoading(false); 
      }
    })();
  }, []);

  const publish = async () => {
    if (!title.trim() || !content.trim()) return;
    
    const res = await fetch('/api/notice', { 
      method: 'POST', 
      headers: { 
        'Content-Type': 'application/json', 
        'x-admin-token': token 
      }, 
      body: JSON.stringify({ title, content, pinned }) 
    });
    
    const j = await res.json();
    if (j?.ok) {
      setTitle(''); 
      setContent(''); 
      setPinned(false);
      
      // 重新加载公告列表
      const r = await fetch('/api/notice');
      const data = await r.json();
      setItems(data.items || []);
      
      alert('发布成功');
    } else {
      alert(j?.error || '发布失败');
    }
  };

  const publishFirstNotice = async () => {
    if (!confirm('将清空并替换为新的第一篇公告，确认继续？')) return;
    
    // 清空所有公告
    await fetch('/api/notice', { 
      method: 'DELETE', 
      headers: { 'x-admin-token': token } 
    });
    
    // 发布第一篇公告
    const title = '第一篇公告';
    const content = `# 第一篇公告

## 亲爱的，欢迎来到"逆线"。

如果你会在这里驻足，我想，你一定也经历了一个相似的夏天。

还记得吗？

那些守在屏幕前，等心跳倒数的周一和周二。那些在小红书、微博、LOFTER等等，靠着一点点物料的"糖渣"就能反复品味一整天的日子。那些明明隔着屏幕，却在看到他们对视一笑时，比自己恋爱了还要幸福到流泪的瞬间。

我们曾以为，这只是又一部普通的剧。直到KTV那首不成调的《大城小爱》响起，直到那句哽咽的"我舍不得你"传来，我们才发现，有什么东西，已经彻底失控了。

**这份失控，这份深刻的"意难平"，这份共同的"舍不得"，就是"逆线"存在于此的唯一理由。**

当故事的结局尘埃落定，当热搜的喧嚣渐渐散去，我们心中那份巨大的、无处安放的情感要怎么办？那些在深夜里反复咀嚼、反复想象的"如果当初"，那些只属于他们的、未被讲述的千万种未来，又要去哪里安放？

"逆线"想成为那个地方。

一个可以让我们所有"舍不得"得到回响的宇宙。一个能将我们汹涌的爱意和不甘，转化为真实篇章的避风港。

在这里，没有原作的界线，没有结局的遗憾。在命运的莫比乌斯环上，每一次分离，都可以是我们笔下永恒厮守的序章。

## **关于"灵感"（本站积分系统）**

**"灵感"是我们唯一的货币 (1.5元=10点灵感)。** 所有的创作，都需要消耗一点"灵感"来驱动AI这个不知疲倦的写手。

**我们为你准备了免费的"灵感"启动资金：**

- **初遇之礼：** 注册即赠 **30点灵感**。
- **每日重逢：** 每日签到，可领 **10点灵感** (连续打卡有惊喜)。
- **同好之邀：** 邀请每一位朋友加入，你和TA**各得30点灵感**。

**如果你需要更多"灵感"来构筑长篇大梦：**

- 我们准备了几个**"灵感补给包"**（6元/30元/98元/328元），丰俭由人。
- 也为重度创作者准备了**"月卡"**、**"季卡"**、"年卡"**，能以更优惠的方式，获得源源不断的"灵感"和专属特权。

## **关于"回响之墙"（我们的共创中心）**

"逆线"刚刚诞生，它还只是一个毛坯房，一定有很多BUG，很多不完美。我真诚地、迫切地希望能听到你的声音。

任何对网站的**意见、建议、发现的BUG、或是对未来的期许**，都可以留在导航栏的**"回响之墙"**。你的每一条反馈，都是让这个家变得更好的基石。我会认真阅读，并努力回应。

------

最后，谢谢你，能找到这里。

谢谢我们，曾在同一个故事里，如此真切地活过、爱过、哭过、笑过。

愿我们在这个莫比乌斯环上，在这个名为"逆线"的空间里，用我们的爱与想象，创造出无数个属于他们的，也属于我们的，心动瞬间。

点击注册，

让我们一起，为这个夏天，续写一个永不完结的未来。

—— "逆线"站长 （2025年8月9日，这个难忘的夏天）`;

    const r = await fetch('/api/notice', { 
      method: 'POST', 
      headers: { 
        'Content-Type': 'application/json', 
        'x-admin-token': token 
      }, 
      body: JSON.stringify({ title, content, pinned: true }) 
    });
    
    const j = await r.json();
    if (j?.ok) {
      const d = await fetch('/api/notice');
      const dj = await d.json();
      setItems(dj.items || []);
      alert('已替换为第一篇公告');
    } else {
      alert(j?.error || '替换失败');
    }
  };

  return (
    <UnifiedLayout title="📢 公告" backgroundIntensity="medium">
      {/* 管理员控制面板 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
        flexWrap: 'wrap',
        gap: '12px'
      }}>
        <div style={{
          color: 'rgba(255, 255, 255, 0.8)',
          fontSize: '16px'
        }}>
          共 {items.length} 条公告
        </div>
        
        <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
          <UnifiedButton
            variant="secondary"
            size="small"
            onClick={() => {
              const t = token || prompt('输入管理员令牌') || '';
              if (t) { 
                setToken(t); 
                localStorage.setItem('adminToken', t); 
                setShowAdmin(true); 
              }
            }}
          >
            🔑 管理员发布
          </UnifiedButton>
          
          {showAdmin && (
            <UnifiedButton
              variant="danger"
              size="small"
              onClick={async () => {
                if (!confirm('确认清空所有公告？')) return;
                
                const r = await fetch('/api/notice', { 
                  method: 'DELETE', 
                  headers: { 'x-admin-token': token } 
                });
                
                const j = await r.json();
                if (j?.ok) { 
                  setItems([]); 
                  alert('已删除全部公告'); 
                } else {
                  alert(j?.error || '删除失败');
                }
              }}
            >
              🗑️ 清空公告
            </UnifiedButton>
          )}
        </div>
      </div>

      {/* 管理员发布表单 */}
      {showAdmin && token && (
        <UnifiedCard title="发布新公告" style={{ marginBottom: '24px' }}>
          <div style={{ marginBottom: '16px' }}>
            <UnifiedInput
              placeholder="公告标题"
              value={title}
              onChange={setTitle}
              style={{ marginBottom: '12px' }}
            />
            
            <label style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: '14px',
              marginBottom: '12px'
            }}>
              <input 
                type="checkbox" 
                checked={pinned} 
                onChange={(e) => setPinned(e.target.checked)}
                style={{ transform: 'scale(1.2)' }}
              />
              置顶公告
            </label>
          </div>

          <UnifiedTextarea
            placeholder="公告内容（支持 Markdown 格式）"
            value={content}
            onChange={setContent}
            rows={8}
            style={{ marginBottom: '16px' }}
          />

          <div style={{
            display: 'flex',
            gap: '12px',
            justifyContent: 'flex-end',
            flexWrap: 'wrap'
          }}>
            <UnifiedButton
              variant="primary"
              onClick={publish}
              disabled={!title.trim() || !content.trim()}
            >
              📝 发布公告
            </UnifiedButton>

            <UnifiedButton
              variant="secondary"
              onClick={publishFirstNotice}
            >
              ✨ 一键发布"第一篇公告"
            </UnifiedButton>
          </div>
        </UnifiedCard>
      )}

      {/* 公告列表 */}
      {loading ? (
        <UnifiedCard>
          <div style={{ textAlign: 'center', padding: '60px' }}>
            <div style={{ fontSize: '48px', marginBottom: '16px' }}>📢</div>
            <div style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
              正在加载公告...
            </div>
          </div>
        </UnifiedCard>
      ) : items.length === 0 ? (
        <UnifiedCard>
          <div style={{ textAlign: 'center', padding: '80px 20px' }}>
            <div style={{ fontSize: '64px', marginBottom: '24px' }}>📰</div>
            <div style={{ 
              fontSize: '20px', 
              marginBottom: '12px',
              color: 'rgba(255, 255, 255, 0.9)'
            }}>
              暂无公告
            </div>
            <div style={{ 
              fontSize: '14px', 
              color: 'rgba(255, 255, 255, 0.6)'
            }}>
              期待第一条公告的发布！
            </div>
          </div>
        </UnifiedCard>
      ) : (
        <div style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
          {items.map(notice => (
            <UnifiedCard key={notice.id} variant="default">
              <div style={{
                display: 'flex',
                alignItems: 'flex-start',
                justifyContent: 'space-between',
                marginBottom: '16px',
                flexWrap: 'wrap',
                gap: '12px'
              }}>
                <h2 style={{
                  fontSize: '20px',
                  fontWeight: 'bold',
                  background: cpColors.gradient,
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0,
                  flex: 1
                }}>
                  {notice.title}
                </h2>
                
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}>
                  {notice.pinned && (
                    <UnifiedBadge variant="warning" size="small">
                      📌 置顶
                    </UnifiedBadge>
                  )}
                  
                  <div style={{
                    fontSize: '12px',
                    color: 'rgba(255, 255, 255, 0.5)'
                  }}>
                    {new Date(notice.createdAt).toLocaleString()}
                  </div>
                </div>
              </div>
              
              <div style={{ lineHeight: 1.6 }}>
                {renderMarkdown(notice.content)}
              </div>
            </UnifiedCard>
          ))}
        </div>
      )}
    </UnifiedLayout>
  );
}