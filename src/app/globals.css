@tailwind base;
@tailwind components;
@tailwind utilities;

/* 暂时注释掉项目A的样式导入，避免路径解析错误 */
/* @import '@/styles/globals.css'; */

/* 基础样式 */
.frosted-glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
}

.frosted-glass-button {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.frosted-glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
  box-shadow: 0 8px 32px rgba(102, 107, 206, 0.3);
}

.cp-gradient-text {
  background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* 基础样式 */
@layer components {
  .cp-gradient-text {
    background: linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  
  .frosted-glass-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
  }
  
  .frosted-glass-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
  
  .breathing-glow {
    animation: breathing-glow 3s ease-in-out infinite;
  }
  
  @keyframes breathing-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(102, 107, 206, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(102, 107, 206, 0.6);
    }
  }
  
  .hover-lift {
    transition: all 0.3s ease;
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
  }
}
