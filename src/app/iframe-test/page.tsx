'use client';

import React from 'react';

export default function IframeParticlePage() {
  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 独立的3D粒子系统 */}
      <iframe
        src="/particle-3d.html"
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 0
        }}
        title="3D粒子宇宙"
      />
      
      {/* UI覆盖层 */}
      <div style={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        textAlign: 'center',
        zIndex: 100,
        color: 'white',
        pointerEvents: 'none'
      }}>
        <h1 style={{
          fontSize: '3rem',
          fontWeight: 'bold',
          margin: 0,
          background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
          WebkitBackgroundClip: 'text',
          backgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          marginBottom: '20px'
        }}>
          🌌 逆线宇宙
        </h1>
        <div style={{
          background: 'rgba(0,0,0,0.7)',
          padding: '15px',
          borderRadius: '10px',
          backdropFilter: 'blur(10px)'
        }}>
          <p style={{ margin: '5px 0', fontSize: '14px' }}>✨ 独立模块化3D粒子系统</p>
          <p style={{ margin: '5px 0', fontSize: '14px' }}>🎨 避开Next.js兼容性问题</p>
          <p style={{ margin: '5px 0', fontSize: '12px', color: '#ccc' }}>
            点击3D区域 → WASD移动 → 鼠标视角
          </p>
        </div>
      </div>
    </div>
  );
}