"use client";
import { useEffect } from 'react';

export default function ClientSync(){
  useEffect(()=>{
    let timer: any;
    async function sync(){
      try{
        const raw = localStorage.getItem('user');
        if (!raw) return;
        const u = JSON.parse(raw);
        if (!u?.id) return;
        const r = await fetch('/api/me', { headers:{ 'x-user-id': u.id } });
        const j = await r.json();
        if (j?.ok){ localStorage.setItem('user', JSON.stringify(j.user)); }
      }catch{}
    }
    const onFocus = ()=>sync();
    window.addEventListener('focus', onFocus);
    timer = setInterval(sync, 60000);
    sync();

    const onError = (ev: ErrorEvent)=>{
      try{ fetch('/api/debug', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ type:'onerror', message: ev.message, stack: ev.error?.stack, href: location.href }) }).catch(()=>{}); }catch{}
    };
    const onRej = (ev: PromiseRejectionEvent)=>{
      try{ fetch('/api/debug', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ type:'unhandledrejection', reason: String((ev as any).reason||''), href: location.href }) }).catch(()=>{}); }catch{}
    };
    window.addEventListener('error', onError);
    window.addEventListener('unhandledrejection', onRej);

    return ()=>{ window.removeEventListener('focus', onFocus); if (timer) clearInterval(timer); window.removeEventListener('error', onError); window.removeEventListener('unhandledrejection', onRej); };
  },[]);
  return null;
} 