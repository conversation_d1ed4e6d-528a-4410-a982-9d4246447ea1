'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>R<PERSON>, Sparkles } from 'lucide-react';
import TopNavigation from '../../components/TopNavigation';
import CreationIsland from '../../components/CreationIsland';
import FlightHint from '../../components/FlightHint';
import ScreenshotButton from '../../components/ScreenshotButton';
import ScreenshotWatermark from '../../components/ScreenshotWatermark';

const PARTICLE_COUNT = 50000;

export default function LandingPage() {
  const router = useRouter();

  return (
    <div id="screenshot-root" className="relative w-full h-screen overflow-hidden">
      {/* UI Overlays - 完全复制A项目的所有组件 */}
      <TopNavigation />
      <CreationIsland />
      <FlightHint particleCount={PARTICLE_COUNT} />
      <ScreenshotButton />
      <ScreenshotWatermark />
      
      {/* 中央品牌展示区域 */}
      <div className="relative z-20 flex flex-col items-center justify-center h-full text-center px-4">
        <h1 className="text-6xl md:text-8xl font-extrabold mb-6 cp-gradient-text">逆线</h1>
        <p className="text-xl md:text-2xl text-white/90 max-w-2xl mx-auto leading-relaxed">
          在无数逆转的线里，重逢唯一的你
        </p>
        
        <div className="mt-8 space-y-4">
          <button 
            onClick={() => router.push('/create')} 
            className="group frosted-glass-button px-8 py-4 text-lg font-semibold breathing-glow hover-lift"
          >
            <div className="flex items-center gap-3">
              <Sparkles className="w-6 h-6" />
              <span>进入创作圣地</span>
              <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
            </div>
          </button>
        </div>

        <div className="mt-12 text-white/60 text-sm">
          <p>使用 WASD 移动，鼠标控制视角</p>
          <p className="mt-2">体验沉浸式星空之旅</p>
        </div>
      </div>

      <div className="absolute bottom-6 left-6 text-white/40 text-xs">
        <p>逆线 · 创作平台</p>
      </div>
    </div>
  );
}
