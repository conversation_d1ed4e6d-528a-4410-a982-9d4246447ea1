'use client';

import { useState, useEffect, useRef, Suspense } from 'react';
import Head from 'next/head';
import { useSearchParams, useRouter } from 'next/navigation';
import { ArrowLeft, BookOpen, Copy, Share2, Heart, Star, Download, RefreshCw, Sparkles, Settings, AlertCircle, CheckCircle, Globe, Lock } from 'lucide-react';
import Image from 'next/image';
import ReactMarkdown from 'react-markdown';

function GenerateClient() {
  const [isGenerating, setIsGenerating] = useState(true);
  const [generatedContent, setGeneratedContent] = useState('');
  const [workTitle, setWorkTitle] = useState('');
  const [intent, setIntent] = useState('');
  const [userPrompt, setUserPrompt] = useState('');
  const [showPreview, setShowPreview] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [aiProvider, setAiProvider] = useState('deepseek');
  const [generationData, setGenerationData] = useState<any>(null);
  const [user, setUser] = useState<any>(null);
  const [tier, setTier] = useState<'fragment'|'standard'|'deep'>('deep');
  const [showDebugInfo, setShowDebugInfo] = useState(false);
  const [debugLogs, setDebugLogs] = useState<string[]>([]);
  const [error, setError] = useState('');
  const hasRequestedRef = useRef(false);
  const [evidencePack, setEvidencePack] = useState<any>(null);
  const [showEvidence, setShowEvidence] = useState(true);
  const [expandedIdx, setExpandedIdx] = useState<number | null>(0);
  const [grounding, setGrounding] = useState<any>(null);
  const [isFav, setIsFav] = useState(false);
  const [chapters, setChapters] = useState<Array<{content: string, chapterNumber: number}>>([]);
  const [currentChapter, setCurrentChapter] = useState(1);
  const [isContinuing, setIsContinuing] = useState(false);
  const contentEndRef = useRef<HTMLDivElement>(null);
  const quotesPool = [
    '我舍不得你。',
    '只有你生气，我才会着急。',
    '我的东西，你休想染指。',
    '别动，不然我控制不住我自己。',
    '我可以等你，但你别不来。',
    '你往前走，我一直在你后面。',
    '以后只管往前冲，恶霸罩着你。',
    '那以后我就叫你大宝了。',
    '你又不是我男朋友，凭什么让你碰我。',
    '五万卖不卖？搭上你就卖。',
    '晃来晃去，晃得我心都乱了。',
    '你为我做任何事情，跟我喜不喜欢你没有任何关系，你就算每天屁事不干，我照样喜欢你。',
    '你表达爱意的方式太直接了。',
    '但我就知道我喜欢你 me too。',
    '我给你的东西不需要知道为什么，只要你需要。',
    '池骋，你个渣男！',
    '因为只有你生气我才会着急。',
    '我也不知道我喜不喜欢男的，但我就知道我喜欢你。',
    '怎么钓男人？钓谁？钓我前女友的现男友。',
    '屌丝装什么高雅？',
    '就这点觉悟，别当小三了。',
    '你带我来这地方，跟你打野战呢？',
    '那你是我的吗？你废话！',
    '我看你心态，我就特别过瘾。',
    '这什么意思啊？回去办证！',
    '我承认是我不好，都是我太着急。',
    '我看你心疼，我就特别过瘾。',
    '我懂你们的，我是害怕，怕你们这条路会走的很艰难的。',
    '乌黑的头发盘成一个圈，缠绕所有对你的眷恋。',
    '脑袋都是你，心里都是你，小小的爱在大城里好甜蜜。',
    '剪掉一丝头发让我放在胸前，走到哪里都有你陪。',
    '我不想吃这个，就想吃你这个。',
    '畏畏，我保证：以后不会再让你受到伤害。',
    '我想吃你的，不都一个味儿吗。',
    '你看，像不像你。',
    '如果我可以早点动手，你就不会受那么严重的伤了。',
    '其实，我挺喜欢你的。',
    '我舍不得你。',
    '我看你心疼，我就特别过瘾。',
    '你今天先回去好不好，明天我早点去找你。',
    '你又不是我男朋友，凭什么让你碰我。',
    '大宝，谁都可以生我的气，就你不行。',
    '我应了你的事情，一定会做到。',
  ];
  const colorCombos = [
    { bg: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 100%)', fg: '#FFFFFF' },
    { bg: 'linear-gradient(135deg, #C2A8F2 0%, #FFD64F 100%)', fg: '#1A1A3A' },
    { bg: 'linear-gradient(135deg, #FFD64F 0%, #FF6B35 100%)', fg: '#FFFFFF' },
    { bg: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)', fg: '#FFFFFF' },
    { bg: 'linear-gradient(135deg, #FF6B35 0%, #C2A8F2 100%)', fg: '#FFFFFF' },
  ];
  const animeFonts = `\'ZCOOL XiaoWei\', \'ZCOOL KuaiLe\', \'Ma Shan Zheng\', sans-serif`;
  
  const searchParams = useSearchParams();
  const router = useRouter();

  // 生成动态标题的函数
  const generateDynamicTitle = () => {
    const intent = searchParams.get('intent');
    const promptFromUrl = searchParams.get('prompt');
    
    // 首先尝试从inspirations中匹配标题
    if (intent && promptFromUrl) {
      try {
        // 同步导入inspirations
        const { INSPIRATIONS } = require('@/lib/inspirations');
        const categoryCards = INSPIRATIONS[intent as keyof typeof INSPIRATIONS];
        if (categoryCards) {
          // 更精确的匹配逻辑
          const matchingCard = categoryCards.find((card: any) => {
            if (!card.prompt) return false;
            // 匹配prompt的关键词
            const promptKeywords = promptFromUrl.toLowerCase().split(' ').slice(0, 3);
            const cardPromptLower = card.prompt.toLowerCase();
            return promptKeywords.some(keyword => cardPromptLower.includes(keyword));
          });
          
          if (matchingCard) {
            return `✨《${matchingCard.title}》创作完成`;
          }
        }
      } catch (e) {
        console.log('Failed to load inspirations:', e);
      }
    }
    
    // 如果有用户自定义提示词，提取关键词作为标题
    if (userPrompt && userPrompt.trim()) {
      const cleanPrompt = userPrompt.replace(/[^\u4e00-\u9fa5\w\s]/g, '').trim();
      const words = cleanPrompt.split(/\s+/).filter(w => w.length > 1);
      let title = '';
      
      if (words.length > 0) {
        // 取前3个有意义的词作为标题
        title = words.slice(0, 3).join('');
        if (title.length > 15) {
          title = title.slice(0, 15);
        }
      }
      
      if (!title) {
        title = cleanPrompt.slice(0, 10);
      }
      
      return title ? `✨《${title}》创作完成` : '✨《自定义创作》创作完成';
    }
    
    // 根据意图生成标题
    if (intent) {
      const intentTitleMap: Record<string, string> = {
        'psych': '心理深挖',
        'action': '高能场面', 
        'detail': '细节描写',
        'side': '副线剧情',
        'au': '架空世界',
        'realPerson': '真人演绎'
      }
      const intentTitle = intentTitleMap[intent] || '精彩内容';
      return `✨《${intentTitle}》创作完成`;
    }
    
    if (workTitle) {
      return `✨《${workTitle}》创作完成`;
    }
    
    return '✨《逆线创作》完成';
  }

  useEffect(() => {
    const work = searchParams.get('work') || '';
    const intentParam = searchParams.get('intent') || '';
    const prompt = searchParams.get('prompt') || '';
    const tierParam = (searchParams.get('tier') as any) || 'deep';
    const universeParam = searchParams.get('universe') || 'novel';
    
    setWorkTitle(work);
    setIntent(intentParam);
    setUserPrompt(prompt);
    if (['fragment','standard','deep'].includes(tierParam)) setTier(tierParam);

    // 检查用户登录状态
    const userData = localStorage.getItem('user');
    if (userData) {
      setIsLoggedIn(true);
      setUser(JSON.parse(userData));
    }

    // 同步服务器余额（进入页面与窗口聚焦时）
    async function syncMe(){
      try{
        const current = JSON.parse(localStorage.getItem('user')||'null');
        if (current?.id){
          const r = await fetch('/api/me', { headers:{ 'x-user-id': current.id } });
          const j = await r.json();
          if (j?.ok){ localStorage.setItem('user', JSON.stringify(j.user)); setUser(j.user); }
        }
      }catch{}
    }
    syncMe();
    const onFocus = ()=>syncMe();
    window.addEventListener('focus', onFocus);

    // 开始生成过程
    if (hasRequestedRef.current) return;

    if (work && intentParam && prompt) {
      hasRequestedRef.current = true;
      generateContent(work, intentParam, prompt, aiProvider, universeParam);
    }

    return ()=>{
      window.removeEventListener('focus', onFocus);
    }
  }, [searchParams]);

  const tierCost = (t: 'fragment'|'standard'|'deep') => 10; // 统一改为10灵感
  const generateContent = async (workTitle: string, intent: string, userPrompt: string, provider: string = 'gemini', universe: string = 'novel') => {
    console.log('🚀 开始生成内容...');
    setIsGenerating(true);
    setError('');
    setDebugLogs([]);
    
    const logs: string[] = [];
    const addLog = (message: string) => {
      const timestamp = new Date().toLocaleTimeString();
      const logMessage = `[${timestamp}] ${message}`;
      logs.push(logMessage);
      setDebugLogs([...logs]);
      console.log(logMessage);
    }

    try {
      // 校验余额
      const current = JSON.parse(localStorage.getItem('user')||'null');
      const need = tierCost(tier);
      if (!current || (current.credits||0) < need) {
        setIsGenerating(false);
        setError(`余额不足，需要充值啦（所需灵感：${need}）`);
        alert('余额不足，需要充值啦');
        return;
      }

      addLog('📥 发送生成请求到服务器...');
      addLog(`📖 作品: ${workTitle}`);
      addLog(`🎯 意图: ${intent}`);
      addLog(`💭 描述: ${userPrompt}`);
      addLog(`🤖 AI服务: ${provider}`);

      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(current?.id ? { 'x-user-id': current.id } : {})
        },
        body: JSON.stringify({
          workTitle,
          intent,
          userPrompt,
          aiProvider: provider,
          tier,
          universe
        }),
      });

      addLog(`📡 服务器响应状态: ${response.status}`);

      const data = await response.json();

      if (data.success) {
        // 后端已经扣费，前端不再扣费，只更新本地用户信息
        // 从返回的data中获取最新用户信息，或重新获取
        try {
          const userResponse = await fetch('/api/me', {
            headers: { 'x-user-id': current.id }
          });
          if (userResponse.ok) {
            const userData = await userResponse.json();
            localStorage.setItem('user', JSON.stringify(userData.user));
            setUser(userData.user);
          }
        } catch (e) {
          console.error('更新用户信息失败:', e);
        }
        addLog('✅ 内容生成成功!');
        addLog(`📄 生成内容长度: ${data.data.content.length}字符`);
        addLog(`⭐ 质量评分: ${data.data.qualityScore}%`);
        addLog(`⏱️ 生成耗时: ${data.data.generationTime}秒`);
        
        setGeneratedContent(data.data.content);
        setGenerationData(data.data);
        if (data.data?.evidence?.items?.length) {
          setEvidencePack(data.data.evidence);
        } else {
          setEvidencePack(null);
        }
        if (data.data?.grounding) setGrounding(data.data.grounding);
        
        if (data.data.warning) {
          addLog(`⚠️ 警告: ${data.data.warning}`);
        }
        
      } else {
        addLog(`❌ 生成失败: ${data.error}`);
        setError(data.error || '生成失败');
      }

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '网络错误';
      addLog(`❌ 请求失败: ${errorMessage}`);
      
      // 检查是否是JSON解析错误
      if (errorMessage.includes('json') || errorMessage.includes('JSON')) {
        setError('服务器返回数据格式错误，请稍后重试');
        addLog('💡 提示：可能是AI服务暂时不可用');
      } else {
        setError(errorMessage);
      }
    } finally {
      setIsGenerating(false);
      addLog('🏁 生成流程结束');
    }
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(generatedContent);
    alert('内容已复制到剪贴板');
  }

  const handleUnlock = () => {
    setShowPreview(false);
  }

  const handleRegenerate = () => {
    generateContent(workTitle, intent, userPrompt, aiProvider);
  }

  const getIntentName = (intentId: string) => {
    const intents: { [key: string]: string } = {
      'action': '高能场面',
      'detail': '细节补完',
      'psych': '心理深挖',
      'side': '配角外传',
      'au': '平行世界',
      // 兼容旧值
      'sequel': '续写番外',
      'style': '风格模仿',
    }
    return intents[intentId] || '创作';
  }

  const getAIProviderName = (provider: string) => {
    const providers: { [key: string]: string } = {
      'gemini': 'Google Gemini',
      'kimi': 'Moonshot Kimi',
      'deepseek': 'DeepSeek',
      'fallback': '备用模板'
    }
    return providers[provider] || provider;
  }

  const addDebugLog = (message: string) => {
    setDebugLogs(prev => [...prev.slice(-19), message]);
  }

  // 简单的Markdown渲染函数
  const renderMarkdown = (text: string) => {
    return text.split('\n').map((line, idx) => {
      // 处理标题
      if (line.startsWith('### ')) {
        return <h3 key={idx} className="text-lg font-bold text-gray-800 mb-2">{line.replace('### ', '')}</h3>;
      } else if (line.startsWith('## ')) {
        return <h2 key={idx} className="text-xl font-bold text-gray-800 mb-2">{line.replace('## ', '')}</h2>;
      } else if (line.startsWith('# ')) {
        return <h1 key={idx} className="text-2xl font-bold text-gray-800 mb-2">{line.replace('# ', '')}</h1>;
      } else if (line.includes('**')) {
        // 处理加粗文字
        const parts = line.split(/(\*\*[^*]+\*\*)/g);
        return (
          <p key={idx} className="mb-1">
            {parts.map((part, i) => 
              part.startsWith('**') && part.endsWith('**') ? 
                <strong key={i} className="font-bold text-gray-800">{part.slice(2, -2)}</strong> : 
                <span key={i} className="text-gray-700">{part}</span>
            )}
          </p>
        );
      } else if (line.trim()) {
        return <p key={idx} className="mb-1 text-gray-700">{line}</p>;
      }
      return null;
    }).filter(Boolean);
  }

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #0F0B27 0%, #1A1A3A 100%)',
      position: 'relative'
    }}>
      <Head>
        <link href="https://fonts.googleapis.com/css2?family=Ma+Shan+Zheng&family=ZCOOL+KuaiLe&family=ZCOOL+XiaoWei&display=swap" rel="stylesheet" />
      </Head>
      
      {/* 粒子背景 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 214, 79, 0.3) 0%, transparent 50%)',
        pointerEvents: 'none'
      }} />
      
      {/* 顶部导航和用户信息 */}
      <div style={{
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between', 
        padding: '24px', 
        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(16px)',
        backgroundColor: 'rgba(0, 0, 0, 0.2)',
        position: 'relative',
        zIndex: 10
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          <button 
            onClick={() => router.push('/')} 
            style={{
              display: 'flex', 
              alignItems: 'center', 
              gap: '8px', 
              color: 'rgba(255, 255, 255, 0.8)', 
              background: 'none',
              border: 'none',
              cursor: 'pointer',
              fontSize: '14px',
              transition: 'color 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = '#666BCE';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';
            }}
          >
            <ArrowLeft style={{ width: '18px', height: '18px' }} />
            <span>返回首页</span>
          </button>
          <div style={{ height: '20px', width: '1px', backgroundColor: 'rgba(255, 255, 255, 0.2)' }}></div>
          <h1 style={{
            fontSize: '1.5rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            margin: 0
          }}>
            ✨ {intent ? `${intent}创作中` : '逆线创作工坊'}
          </h1>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
          {user && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
              <div style={{ textAlign: 'right' }}>
                <div style={{ fontSize: '14px', fontWeight: '500', color: 'rgba(255, 255, 255, 0.9)' }}>{user.name || '创作者'}</div>
                <div style={{ fontSize: '12px', color: 'rgba(255, 255, 255, 0.6)' }}>ID: {user.id?.slice(-8) || 'guest'}</div>
              </div>
              <div style={{
                padding: '8px 16px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: '600',
                color: 'white',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)',
                boxShadow: '0 4px 15px rgba(102, 107, 206, 0.3)'
              }}>
                可用灵感：{user?.credits ?? JSON.parse(localStorage.getItem('user')||'{}')?.credits ?? 0}
              </div>
            </div>
          )}
          <button
            onClick={() => setShowDebugInfo(!showDebugInfo)}
            style={{
              padding: '10px',
              borderRadius: '8px',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              color: 'rgba(255, 255, 255, 0.8)',
              cursor: 'pointer',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
            }}
            title="调试信息"
          >
            <Settings style={{ width: '16px', height: '16px' }} />
          </button>
        </div>
      </div>

      <main style={{ 
        maxWidth: '1200px', 
        margin: '0 auto', 
        padding: '32px 20px', 
        position: 'relative', 
        zIndex: 10 
      }}>
        {/* 调试信息面板 */}
        {showDebugInfo && (
          <div style={{
            backdropFilter: 'blur(16px)',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            color: '#4ADE80',
            borderRadius: '16px',
            padding: '20px',
            marginBottom: '24px',
            fontFamily: 'monospace',
            fontSize: '14px',
            maxHeight: '256px',
            overflowY: 'auto',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
              <Settings style={{ width: '16px', height: '16px', color: 'white' }} />
              <span style={{ color: 'white', fontWeight: 'bold' }}>生成日志</span>
            </div>
            {debugLogs.length > 0 ? (
              debugLogs.map((log, index) => (
                <div key={index} style={{ marginBottom: '4px', lineHeight: '1.4' }}>
                  {log}
                </div>
              ))
            ) : (
              <div style={{ color: 'rgba(255, 255, 255, 0.6)' }}>暂无日志...</div>
            )}
          </div>
        )}

        {/* 页面标题 */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{ position: 'relative', marginBottom: '24px' }}>
            <h1 style={{
              fontSize: '2.5rem',
              fontWeight: 'bold',
              marginBottom: '12px',
              background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent'
            }}>
              {generateDynamicTitle()}
            </h1>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px', marginBottom: '8px' }}>
              <span style={{
                padding: '6px 16px',
                borderRadius: '20px',
                fontSize: '14px',
                fontWeight: '600',
                color: 'white',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)',
                boxShadow: '0 4px 15px rgba(102, 107, 206, 0.3)'
              }}>
                {getIntentName(intent)}
              </span>
              <Sparkles style={{ width: '20px', height: '20px', color: '#FFD64F' }} />
            </div>
            <p style={{ 
              color: 'rgba(255, 255, 255, 0.8)', 
              fontSize: '1.125rem',
              marginBottom: '0'
            }}>
              🎭 专属为你量身定制的心动内容
            </p>
          </div>
          
          <div style={{
            backdropFilter: 'blur(16px)',
            backgroundColor: 'rgba(0, 0, 0, 0.35)',
            borderRadius: '16px',
            padding: '24px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
            maxWidth: '600px',
            margin: '0 auto'
          }}>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: '12px' }}>
              <BookOpen style={{ width: '20px', height: '20px', color: '#666BCE' }} />
              <span style={{ fontWeight: '600', color: '#666BCE' }}>创作需求</span>
            </div>
            <div style={{ textAlign: 'left', color: 'rgba(255, 255, 255, 0.9)' }}>
              {renderMarkdown(userPrompt)}
            </div>
            {generationData && (
              <div style={{ 
                marginTop: '16px', 
                paddingTop: '16px', 
                borderTop: '1px solid rgba(255, 255, 255, 0.1)'
              }}>
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center', 
                  justifyContent: 'space-between', 
                  fontSize: '14px', 
                  color: 'rgba(255, 255, 255, 0.7)' 
                }}>
                  <span>✨ 由 {getAIProviderName(generationData.aiProvider)} 精心创作</span>
                  <span>🕒 生成时间: {new Date().toLocaleTimeString()}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 错误提示 */}
        {error && (
          <div style={{
            backdropFilter: 'blur(16px)',
            backgroundColor: 'rgba(239, 68, 68, 0.1)',
            border: '1px solid rgba(239, 68, 68, 0.3)',
            borderRadius: '16px',
            padding: '20px',
            marginBottom: '24px',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            boxShadow: '0 20px 60px rgba(239, 68, 68, 0.2)'
          }}>
            <AlertCircle style={{ width: '20px', height: '20px', color: '#EF4444', flexShrink: 0 }} />
            <div>
              <div style={{ color: '#FCA5A5', fontWeight: '600', marginBottom: '4px' }}>生成失败</div>
              <div style={{ color: '#FCA5A5', fontSize: '14px' }}>{error}</div>
            </div>
          </div>
        )}

        {/* 生成状态 */}
        {isGenerating ? (
          <div style={{
            backdropFilter: 'blur(16px)',
            backgroundColor: 'rgba(0, 0, 0, 0.35)',
            borderRadius: '20px',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            padding: '48px 32px',
            textAlign: 'center'
          }}>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '24px' }}>
              <div style={{ position: 'relative' }}>
                <div style={{
                  width: '64px',
                  height: '64px',
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)',
                  boxShadow: '0 8px 32px rgba(102, 107, 206, 0.4)'
                }}>
                  <Sparkles style={{ width: '32px', height: '32px', color: 'white', animation: 'pulse 2s infinite' }} />
                </div>
                <div style={{
                  position: 'absolute',
                  inset: 0,
                  width: '64px',
                  height: '64px',
                  borderRadius: '50%',
                  animation: 'ping 2s infinite',
                  opacity: 0.2,
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)'
                }}></div>
              </div>
              <div>
                <h2 style={{
                  fontSize: '1.75rem',
                  fontWeight: '800',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: '8px',
                  lineHeight: '1.2'
                }}>
                  莫比乌斯环正在扭转，为你捕捉灵魂共鸣的瞬间。
                </h2>
                <p style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  marginBottom: '16px'
                }}>
                  时空穿梭预计2-3分钟，请坐稳，即将抵达你心动的世界。
                </p>
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '8px' }}>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    background: '#666BCE',
                    animation: 'bounce 1s infinite'
                  }}></div>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    background: '#C2A8F2',
                    animation: 'bounce 1s infinite',
                    animationDelay: '0.1s'
                  }}></div>
                  <div style={{
                    width: '8px',
                    height: '8px',
                    borderRadius: '50%',
                    background: '#FFD64F',
                    animation: 'bounce 1s infinite',
                    animationDelay: '0.2s'
                  }}></div>
                </div>
              </div>

              {/* 弹幕式经典台词滚动区 */}
              <div style={{ width: '100%', marginTop: '24px' }}>
                <div className="danmu-container">
                  {Array.from({ length: 4 }).map((_, rowIndex) => (
                    quotesPool
                      .filter((_, idx) => idx % 4 === rowIndex)
                      .map((q, k) => {
                        const combo = colorCombos[(k + rowIndex) % colorCombos.length];
                        const dur = 45; // 保持45s的慢速弹幕
                        const delay = -(k * 4 + Math.random() * 3); // 依序错峰+轻微扰动
                        const top = rowIndex * 46; // 行距
                        const fontPick = (k + rowIndex) % 3;
                        const font = fontPick === 0 ? 'ZCOOL XiaoWei' : (fontPick === 1 ? 'ZCOOL KuaiLe' : 'Ma Shan Zheng');
                        return (
                          <div
                            key={`${rowIndex}-${k}`}
                            className="danmu-item"
                            style={{ 
                              top, 
                              animationDuration: `${dur}s`, 
                              animationDelay: `${delay}s`, 
                              background: combo.bg, 
                              color: combo.fg, 
                              fontFamily: font, 
                              textShadow: '0 0 12px rgba(0,0,0,0.5)',
                              backdropFilter: 'blur(4px)',
                              border: '1px solid rgba(255,255,255,0.1)',
                              boxShadow: '0 4px 15px rgba(0,0,0,0.2)'
                            }}
                          >
                            {q}
                          </div>
                        );
                      })
                  ))}
                </div>
              </div>
            </div>
          </div>
        ) : generatedContent && (
          <>
            {/* 操作按钮栏 */}
            <div style={{ 
              display: 'flex', 
              flexWrap: 'wrap', 
              gap: '16px', 
              marginBottom: '24px', 
              justifyContent: 'center' 
            }}>
              <button
                onClick={handleCopy}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  borderRadius: '12px',
                  backdropFilter: 'blur(16px)',
                  backgroundColor: 'rgba(0, 0, 0, 0.35)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(102, 107, 206, 0.2)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 107, 206, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                }}
              >
                <Copy className="w-4 h-4" />
                <span>复制内容</span>
              </button>
              {/* 分享按钮 */}
              <button 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  borderRadius: '12px',
                  backdropFilter: 'blur(16px)',
                  backgroundColor: 'rgba(0, 0, 0, 0.35)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(194, 168, 242, 0.2)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(194, 168, 242, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                }}
                onClick={async()=>{
                  try{
                    const longUrl = typeof window!=='undefined'? window.location.href : '';
                    const r = await fetch('/api/short', { method:'POST', headers:{'Content-Type':'application/json', ...(user?.id?{ 'x-user-id': user.id }:{} )}, body: JSON.stringify({ url: longUrl }) });
                    const j = await r.json();
                    const shortUrl = j?.ok ? j.shortUrl : longUrl;
                    await navigator.clipboard.writeText(shortUrl);
                    alert('短链接已复制：'+shortUrl);
                  }catch(e:any){ alert('生成短链失败，可直接分享当前链接'); }
                }}
              >
                <Share2 style={{ width: '16px', height: '16px' }} />
                <span>分享</span>
              </button>

              {/* 收藏按钮 */}
              <button 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  borderRadius: '12px',
                  backdropFilter: 'blur(16px)',
                  backgroundColor: isFav ? 'rgba(255, 214, 79, 0.2)' : 'rgba(0, 0, 0, 0.35)',
                  border: `1px solid ${isFav ? 'rgba(255, 214, 79, 0.3)' : 'rgba(255, 255, 255, 0.2)'}`,
                  color: isFav ? '#FFD64F' : 'white',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
                }}
                onMouseEnter={(e) => {
                  if (!isFav) {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 214, 79, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(255, 214, 79, 0.3)';
                  }
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 214, 79, 0.3)';
                }}
                onMouseLeave={(e) => {
                  if (!isFav) {
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                  }
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                }}
                onClick={async()=>{
                  if (!user?.id) { alert('请先登录再收藏'); return; }
                  const r = await fetch('/api/favorites', { method:'POST', headers:{'Content-Type':'application/json','x-user-id': user.id}, body: JSON.stringify({ type:'item', title: `${getIntentName(intent)} · ${workTitle}`, content: generatedContent }) });
                  const j = await r.json();
                  if (j?.ok) { setIsFav(true); alert('已收藏到默认文件夹'); } else alert(j?.error||'收藏失败');
                }}
              >
                <Heart style={{ width: '16px', height: '16px' }} />
                <span>收藏</span>
              </button>

              {/* 下载按钮 */}
              <button 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  borderRadius: '12px',
                  backdropFilter: 'blur(16px)',
                  backgroundColor: 'rgba(0, 0, 0, 0.35)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '14px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(255, 107, 53, 0.2)';
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(255, 107, 53, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                }}
                onClick={async ()=>{
                  try{
                    const res = await fetch('/api/generate-image', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ content: generatedContent, prompt: userPrompt, userId: user?.id || 'guest', multi: true, mode: 'fixed', fixedChars: 27, format: 'png' }) });
                    const j = await res.json().catch(()=>null);
                    if(!res.ok || !j?.ok){ const msg = j?.error || res.status; alert(`生成失败: ${msg}`); return; }
                    const pages: string[] = j.pages || [];
                    if (!pages.length) { alert('没有可下载的页面'); return; }
                    pages.forEach((dataUrl, idx)=>{
                      const a = document.createElement('a');
                      a.href = dataUrl; a.download = `nixian-page-${idx+1}.png`;
                      document.body.appendChild(a);
                      a.click();
                      a.remove();
                    });
                  }catch(e:any){ alert(`生成失败: ${e?.message||e}`); }
                }}
              >
                <Download style={{ width: '16px', height: '16px' }} />
                <span>下载为图片</span>
              </button>

              {/* 重新生成按钮 */}
              <button
                onClick={handleRegenerate}
                disabled={isGenerating}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  borderRadius: '12px',
                  backdropFilter: 'blur(16px)',
                  backgroundColor: 'rgba(0, 0, 0, 0.35)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  cursor: isGenerating ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '600',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                  opacity: isGenerating ? 0.5 : 1
                }}
                onMouseEnter={(e) => {
                  if (!isGenerating) {
                    e.currentTarget.style.backgroundColor = 'rgba(102, 107, 206, 0.2)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 107, 206, 0.3)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isGenerating) {
                    e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';
                  }
                }}
              >
                <RefreshCw style={{ width: '16px', height: '16px' }} />
                <span>重新生成</span>
              </button>
              
              {/* 续写按钮 */}
              <button
                onClick={async () => {
                  if (!generatedContent) {
                    alert('没有可续写的内容');
                    return;
                  }
                  
                  if (isContinuing) {
                    return;
                  }
                  
                  setIsContinuing(true);
                  setError('');
                  
                  try {
                    // 构建续写请求
                    const allContent = chapters.length > 0 
                      ? chapters.map(ch => ch.content).join('\n\n') + '\n\n' + generatedContent
                      : generatedContent;
                    
                    const response = await fetch('/api/generate', {
                      method: 'POST',
                      headers: {
                        'Content-Type': 'application/json',
                        ...(user?.id ? { 'x-user-id': user.id } : {})
                      },
                      body: JSON.stringify({
                        workTitle: workTitle,
                        intent: 'continuation',
                        userPrompt: `请基于以下内容续写下一章：\n\n${allContent}`,
                        tier: tier,
                        universe: searchParams.get('universe') || 'novel',
                        isContinuation: true,
                        previousContent: allContent
                      })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                      // 将当前内容添加到章节列表
                      if (chapters.length === 0) {
                        setChapters([{content: generatedContent, chapterNumber: 1}]);
                      }
                      
                      // 添加新章节
                      const newChapterNumber = currentChapter + 1;
                      setChapters(prev => [...prev, {content: data.data.content, chapterNumber: newChapterNumber}]);
                      setCurrentChapter(newChapterNumber);
                      setGeneratedContent(data.data.content);
                      
                      // 更新生成数据
                      setGenerationData(data.data);
                      if (data.data?.evidence?.items?.length) {
                        setEvidencePack(data.data.evidence);
                      }
                      if (data.data?.grounding) setGrounding(data.data.grounding);
                      
                      // 平滑滚动到新内容
                      setTimeout(() => {
                        contentEndRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                      }, 100);
                    } else {
                      throw new Error(data.error || '续写失败');
                    }
                  } catch (error) {
                    console.error('续写错误:', error);
                    setError(error instanceof Error ? error.message : '续写失败，请重试');
                  } finally {
                    setIsContinuing(false);
                  }
                }}
                disabled={isGenerating || isContinuing || !generatedContent}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  padding: '12px 20px',
                  borderRadius: '12px',
                  backdropFilter: 'blur(16px)',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  color: 'white',
                  cursor: (isGenerating || isContinuing || !generatedContent) ? 'not-allowed' : 'pointer',
                  fontSize: '14px',
                  fontWeight: '700',
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(102, 107, 206, 0.4)',
                  opacity: (isGenerating || isContinuing || !generatedContent) ? 0.6 : 1
                }}
                onMouseEnter={(e) => {
                  if (!isGenerating && !isContinuing && generatedContent) {
                    e.currentTarget.style.transform = 'translateY(-3px)';
                    e.currentTarget.style.boxShadow = '0 12px 35px rgba(102, 107, 206, 0.5)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isGenerating && !isContinuing && generatedContent) {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 107, 206, 0.4)';
                  }
                }}
              >
                <BookOpen style={{ width: '16px', height: '16px' }} />
                <span>{isContinuing ? '续写中...' : '续写下一章'}</span>
              </button>
            </div>

            {/* 内容展示区域 */}
            <div style={{
              backdropFilter: 'blur(16px)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              borderRadius: '20px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              overflow: 'hidden',
              display: 'grid',
              gridTemplateColumns: '1fr',
              gap: '0'
            }} className="lg:grid-cols-3">
              {/* 内容头部 */}
              <div style={{
                background: 'linear-gradient(135deg, rgba(102, 107, 206, 0.1) 0%, rgba(194, 168, 242, 0.1) 60%, rgba(255, 214, 79, 0.1) 100%)',
                padding: '24px',
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                gridColumn: '1 / -1'
              }} className="lg:col-span-3">
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <div>
                    <h2 style={{
                      fontSize: '1.25rem',
                      fontWeight: 'bold',
                      color: 'rgba(255, 255, 255, 0.95)',
                      marginBottom: '8px'
                    }}>生成内容</h2>
                    <div style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '16px',
                      fontSize: '14px',
                      color: 'rgba(255, 255, 255, 0.7)'
                    }}>
                      <span>约{generationData?.wordCount || generatedContent.length}字</span>
                      {generationData?.metadata?.rounds !== undefined && (
                        <span>续写轮次: {generationData.metadata.rounds}</span>
                      )}
                      <span>生成时间: {generationData?.generationTime || 30}秒</span>
                      {generationData?.warning && (
                        <span style={{ display: 'flex', alignItems: 'center', gap: '4px', color: '#FB923C' }}>
                          <AlertCircle style={{ width: '16px', height: '16px' }} />
                          <span>备用模式</span>
                        </span>
                      )}
                    </div>
                  </div>
                  
                </div>
              </div>

              {/* 侧栏：参考原文证据 - 前端隐藏，避免版权问题 */}
              <aside style={{
                borderRight: '1px solid rgba(255, 255, 255, 0.1)',
                padding: '20px',
                display: 'none'
              }} className="lg:block">
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '12px' }}>
                  <div style={{ fontWeight: '600', color: 'rgba(255, 255, 255, 0.9)' }}>创作辅助</div>
                  {evidencePack && (
                    <button 
                      onClick={() => setShowEvidence(!showEvidence)} 
                      style={{
                        fontSize: '14px',
                        color: '#C2A8F2',
                        background: 'none',
                        border: 'none',
                        cursor: 'pointer',
                        textDecoration: 'underline'
                      }}
                    >
                      {showEvidence ? '收起' : '展开'}
                    </button>
                  )}
                </div>
                {/* 不再显示原文锚定内容，避免版权问题 */}
                {grounding?.personas?.length ? (
                  <div style={{
                    marginBottom: '16px',
                    fontSize: '14px',
                    color: 'rgba(255, 255, 255, 0.8)',
                    backgroundColor: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '8px',
                    padding: '12px'
                  }}>
                    <div style={{ fontWeight: '600', marginBottom: '4px' }}>人物设定</div>
                        <ul className="list-disc pl-4 text-gray-600">
                          {grounding.personas.slice(0,3).map((p:any) => (
                        <li key={p.name} className="mb-1">
                          <span className="font-medium">{p.name}</span>
                          {p.traits && <span>｜特征：{(p.traits||[]).join('、')||'——'}</span>}
                        </li>
                          ))}
                        </ul>
                      </div>
                    ) : null}
                {evidencePack ? (
                  <div className={`space-y-3 ${showEvidence ? '' : 'hidden'}`}>
                    {(grounding?.anchor ? evidencePack.items.filter((it: any)=> it.chapIndex===grounding.anchor.chapIndex) : evidencePack.items).map((it: any, idx: number) => (
                      <div key={it.chunkId} className="border rounded-lg p-3 bg-slate-50">
                        <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                          <span>第{it.chapterNo || it.chapIndex}章 {it.chapterTitle || ''}</span>
                          <button className="text-purple-600" onClick={() => setExpandedIdx(expandedIdx === idx ? null : idx)}>{expandedIdx === idx ? '折叠' : '展开'}</button>
                        </div>
                        <div className={`text-gray-700 whitespace-pre-line ${expandedIdx === idx ? '' : 'line-clamp-4'}`}>
                          {(it.prev?.text ? `【上文】\n${it.prev.text}\n\n` : '') + `【命中片段】\n${(it.text || '').slice(0, 800)}` + (it.next?.text ? `\n\n【下文】\n${it.next.text}` : '')}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-sm text-gray-400">暂无证据</div>
                )}
              </aside>

              {/* 内容正文 */}
              <div style={{ 
                padding: '24px',
                gridColumn: '1 / -1' 
              }} className="lg:col-span-2">
                <div style={{ 
                  maxWidth: 'none',
                  fontFamily: 'Noto Serif SC, Songti SC, Source Han Serif SC, SimSun, serif',
                  color: 'rgba(255, 255, 255, 0.95)',
                  lineHeight: 1.8,
                  fontSize: '16px'
                }}>
                  {/* 显示所有章节 */}
                  {chapters.length > 0 ? (
                    <div>
                      {chapters.map((chapter, index) => (
                        <div key={chapter.chapterNumber}>
                          {index > 0 && (
                            <div style={{ margin: '32px 0', display: 'flex', alignItems: 'center' }}>
                              <div style={{ 
                                flex: 1, 
                                height: '1px', 
                                background: 'linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent)' 
                              }}></div>
                              <div style={{ 
                                padding: '8px 16px', 
                                background: 'linear-gradient(135deg, rgba(102, 107, 206, 0.2) 0%, rgba(194, 168, 242, 0.2) 100%)', 
                                borderRadius: '20px', 
                                fontSize: '14px', 
                                fontWeight: '600', 
                                color: 'rgba(255, 255, 255, 0.8)',
                                border: '1px solid rgba(255, 255, 255, 0.2)'
                              }}>
                                以下为第{chapter.chapterNumber}章节
                              </div>
                              <div style={{ 
                                flex: 1, 
                                height: '1px', 
                                background: 'linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent)' 
                              }}></div>
                            </div>
                          )}
                          <div style={{ 
                            whiteSpace: 'pre-line',
                            textShadow: '0 1px 2px rgba(0, 0, 0, 0.3)'
                          }}>{chapter.content}</div>
                        </div>
                      ))}
                      {/* 当前正在显示的内容（如果不在章节列表中） */}
                      {!chapters.some(ch => ch.content === generatedContent) && (
                        <div>
                          <div style={{ margin: '32px 0', display: 'flex', alignItems: 'center' }}>
                            <div style={{ 
                              flex: 1, 
                              height: '1px', 
                              background: 'linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent)' 
                            }}></div>
                            <div style={{ 
                              padding: '8px 16px', 
                              background: 'linear-gradient(135deg, rgba(102, 107, 206, 0.2) 0%, rgba(194, 168, 242, 0.2) 100%)', 
                              borderRadius: '20px', 
                              fontSize: '14px', 
                              fontWeight: '600', 
                              color: 'rgba(255, 255, 255, 0.8)',
                              border: '1px solid rgba(255, 255, 255, 0.2)'
                            }}>
                              以下为第{currentChapter}章节
                            </div>
                            <div style={{ 
                              flex: 1, 
                              height: '1px', 
                              background: 'linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent)' 
                            }}></div>
                          </div>
                          <div className="prose prose-purple max-w-none leading-relaxed">
                            <ReactMarkdown>
                              {generatedContent}
                            </ReactMarkdown>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="prose prose-purple max-w-none leading-relaxed">
                      <ReactMarkdown>
                        {generatedContent}
                      </ReactMarkdown>
                    </div>
                  )}
                  
                  {/* 一键转移到交错时空 */}
                  {generatedContent && !isGenerating && !isContinuing && (
                    <div className="mt-8 p-6 bg-gradient-to-r from-purple-50 to-yellow-50 border border-purple-200 rounded-xl">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h3 className="text-lg font-bold text-gray-900 mb-2 flex items-center gap-2">
                            ✨ 将这份心动分享到交错时空
                          </h3>
                          <p className="text-gray-600 text-sm mb-4">
                            让更多人见证你们的故事，或者悄悄收藏这份美好
                          </p>
                          
                          {/* 转移选项 */}
                          <div className="flex items-center gap-4">
                            <button
                              onClick={async () => {
                                if (!user?.id) {
                                  alert('请先登录');
                                  router.push('/auth');
                                  return;
                                }
                                
                                try {
                                  const response = await fetch('/api/content', {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                      userId: user.id,
                                      title: `${getIntentName(intent)}：${userPrompt.slice(0, 20)}...`,
                                      content: generatedContent,
                                      intent,
                                      universe: intent === 'real_person' ? 'real_person' : 'novel',
                                      isPublic: true
                                    })
                                  });
                                  
                                  const data = await response.json();
                                  if (data.ok) {
                                    alert('🎉 作品已发布到交错时空！');
                                    router.push(`/creative-space?section=${intent}`);
                                  } else {
                                    alert('发布失败：' + (data.error || '未知错误'));
                                  }
                                } catch (error) {
                                  alert('发布失败，请重试');
                                }
                              }}
                              className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#666BCE] to-[#C2A8F2] text-white rounded-xl font-medium hover:shadow-lg transition-all"
                            >
                              <Globe className="w-4 h-4" />
                              公开分享
                            </button>
                            
                            <button
                              onClick={async () => {
                                if (!user?.id) {
                                  alert('请先登录');
                                  router.push('/auth');
                                  return;
                                }
                                
                                try {
                                  const response = await fetch('/api/content', {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({
                                      userId: user.id,
                                      title: `${getIntentName(intent)}：${userPrompt.slice(0, 20)}...`,
                                      content: generatedContent,
                                      intent,
                                      universe: intent === 'real_person' ? 'real_person' : 'novel',
                                      isPublic: false
                                    })
                                  });
                                  
                                  const data = await response.json();
                                  if (data.ok) {
                                    alert('💾 作品已保存到私人收藏！');
                                    router.push('/user/my-works?tab=private');
                                  } else {
                                    alert('保存失败：' + (data.error || '未知错误'));
                                  }
                                } catch (error) {
                                  alert('保存失败，请重试');
                                }
                              }}
                              className="flex items-center gap-2 px-6 py-3 bg-white border-2 border-gray-200 text-gray-700 rounded-xl font-medium hover:border-purple-300 hover:text-purple-600 transition-all"
                            >
                              <Lock className="w-4 h-4" />
                              私人收藏
                            </button>
                          </div>
                        </div>
                        
                        <div className="text-6xl opacity-20">
                          🚀
                        </div>
                      </div>
                    </div>
                  )}

                  {/* 章节末尾的一键续写提示 */}
                  {generatedContent && !isGenerating && !isContinuing && (
                    <div className="mt-8 pt-6 border-t border-gray-200 text-center">
                      <div className="inline-flex items-center gap-2 text-lg">
                        <span style={{
                          background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                          backgroundClip: 'text',
                          fontWeight: 'bold'
                        }}>
                          意犹未尽？让故事继续流淌
                        </span>
                        <button
                          onClick={async () => {
                            if (!generatedContent) {
                              alert('没有可续写的内容');
                              return;
                            }
                            
                            if (isContinuing) {
                              return;
                            }
                            
                            setIsContinuing(true);
                            setError('');
                            
                            try {
                              // 构建续写请求
                              const allContent = chapters.length > 0 
                                ? chapters.map(ch => ch.content).join('\n\n') + '\n\n' + generatedContent
                                : generatedContent;
                              
                              const response = await fetch('/api/generate', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json',
                                  ...(user?.id ? { 'x-user-id': user.id } : {})
                                },
                                body: JSON.stringify({
                                  workTitle: workTitle,
                                  intent: 'continuation',
                                  userPrompt: `请基于以下内容续写下一章：\n\n${allContent}`,
                                  tier: tier,
                                  universe: searchParams.get('universe') || 'novel',
                                  isContinuation: true,
                                  previousContent: allContent
                                })
                              });
                              
                              const data = await response.json();
                              
                              if (data.success) {
                                // 将当前内容添加到章节列表
                                if (chapters.length === 0) {
                                  setChapters([{content: generatedContent, chapterNumber: 1}]);
                                }
                                
                                // 添加新章节
                                const newChapterNumber = currentChapter + 1;
                                setChapters(prev => [...prev, {content: data.data.content, chapterNumber: newChapterNumber}]);
                                setCurrentChapter(newChapterNumber);
                                setGeneratedContent(data.data.content);
                                
                                // 更新生成数据
                                setGenerationData(data.data);
                                if (data.data?.evidence?.items?.length) {
                                  setEvidencePack(data.data.evidence);
                                }
                                if (data.data?.grounding) setGrounding(data.data.grounding);
                                
                                // 平滑滚动到新内容
                                setTimeout(() => {
                                  contentEndRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                }, 100);
                              } else {
                                throw new Error(data.error || '续写失败');
                              }
                            } catch (error) {
                              console.error('续写错误:', error);
                              setError(error instanceof Error ? error.message : '续写失败，请重试');
                            } finally {
                              setIsContinuing(false);
                            }
                          }}
                          disabled={isGenerating || isContinuing || !generatedContent}
                          className="inline-flex items-center gap-2 px-4 py-2 rounded-lg hover:shadow-lg transition-all disabled:opacity-50 text-white font-medium border"
                          style={{
                            background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                            boxShadow: '0 4px 15px rgba(102, 107, 206, 0.3)'
                          }}
                        >
                          <BookOpen className="w-4 h-4" />
                          <span>{isContinuing ? '续写中...' : '一键续写'}</span>
                        </button>
                      </div>
                    </div>
                  )}
                  
                  {/* 滚动锚点 */}
                  <div ref={contentEndRef}></div>
                </div>
              </div>
            </div>

            {/* 成功提示 */}
            {!showPreview && (
              <div className="mt-6 bg-green-50 border border-green-200 rounded-xl p-4 flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div className="text-green-800">
                  恭喜！您已解锁完整内容，可以查看和使用全文了。
                </div>
              </div>
            )}

            {/* 一键转移到创作空间 */}
            {!showPreview && generatedContent && (
              <div className="mt-6 bg-gradient-to-r from-purple-50 to-yellow-50 border border-purple-200 rounded-xl p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-gray-900 mb-2 flex items-center gap-2">
                      ✨ 将这份心动分享到交错时空
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      让更多人见证你们的故事，或者悄悄收藏这份美好
                    </p>
                    
                    {/* 转移选项 */}
                    <div className="flex items-center gap-4">
                      <button
                        onClick={async () => {
                          if (!user?.id) {
                            alert('请先登录');
                            router.push('/auth');
                            return;
                          }
                          
                          try {
                            const response = await fetch('/api/content', {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({
                                userId: user.id,
                                title: `${getIntentName(intent)}：${userPrompt.slice(0, 20)}...`,
                                content: generatedContent,
                                intent,
                                universe: intent === 'real_person' ? 'real_person' : 'novel',
                                isPublic: true
                              })
                            });
                            
                            const data = await response.json();
                            if (data.ok) {
                              alert('🎉 作品已发布到交错时空！');
                              router.push('/creative-space');
                            } else {
                              alert('发布失败：' + (data.error || '未知错误'));
                            }
                          } catch (error) {
                            alert('发布失败，请重试');
                          }
                        }}
                        className="flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-[#666BCE] to-[#C2A8F2] text-white rounded-xl font-medium hover:shadow-lg transition-all"
                      >
                        <Globe className="w-4 h-4" />
                        公开分享
                      </button>
                      
                      <button
                        onClick={async () => {
                          if (!user?.id) {
                            alert('请先登录');
                            router.push('/auth');
                            return;
                          }
                          
                          try {
                            const response = await fetch('/api/content', {
                              method: 'POST',
                              headers: { 'Content-Type': 'application/json' },
                              body: JSON.stringify({
                                userId: user.id,
                                title: `${getIntentName(intent)}：${userPrompt.slice(0, 20)}...`,
                                content: generatedContent,
                                intent,
                                universe: intent === 'real_person' ? 'real_person' : 'novel',
                                isPublic: false
                              })
                            });
                            
                            const data = await response.json();
                            if (data.ok) {
                              alert('💾 作品已保存到私人收藏！');
                              router.push('/creative-space?tab=my-works');
                            } else {
                              alert('保存失败：' + (data.error || '未知错误'));
                            }
                          } catch (error) {
                            alert('保存失败，请重试');
                          }
                        }}
                        className="flex items-center gap-2 px-6 py-3 bg-white border-2 border-gray-200 text-gray-700 rounded-xl font-medium hover:border-purple-300 hover:text-purple-600 transition-all"
                      >
                        <Lock className="w-4 h-4" />
                        私人收藏
                      </button>
                    </div>
                  </div>
                  
                  <div className="text-6xl opacity-20">
                    🚀
                  </div>
                </div>
              </div>
            )}

            {/* 相关推荐 */}
            <div className="mt-12">
              <h2 className="text-2xl font-bold text-center mb-8 text-gray-800">
                🎯 为您推荐相关内容
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white rounded-xl p-6 shadow-lg card-hover">
                  <h3 className="font-semibold text-lg mb-2 text-purple-600">相似场景</h3>
                  <p className="text-gray-600 mb-4">基于当前情节生成类似的温馨场景</p>
                  <button 
                    onClick={() => router.push(`/intent?work=${encodeURIComponent(workTitle)}&suggestion=similar`)}
                    className="text-sm bg-purple-100 text-purple-600 px-4 py-2 rounded-full hover:bg-purple-200 transition-colors"
                  >
                    生成类似内容 →
                  </button>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg card-hover">
                  <h3 className="font-semibold text-lg mb-2 text-blue-600">续写推荐</h3>
                  <p className="text-gray-600 mb-4">如果这个故事有下集会怎样？</p>
                  <button 
                    onClick={() => router.push(`/intent?work=${encodeURIComponent(workTitle)}&suggestion=continue`)}
                    className="text-sm bg-blue-100 text-blue-600 px-4 py-2 rounded-full hover:bg-blue-200 transition-colors"
                  >
                    继续这个故事 →
                  </button>
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg card-hover">
                  <h3 className="font-semibold text-lg mb-2 text-teal-600">换个视角</h3>
                  <p className="text-gray-600 mb-4">从不同角色的视角重新演绎</p>
                  <button 
                    onClick={() => router.push(`/intent?work=${encodeURIComponent(workTitle)}&suggestion=perspective`)}
                    className="text-sm bg-teal-100 text-teal-600 px-4 py-2 rounded-full hover:bg-teal-200 transition-colors"
                  >
                    更换视角 →
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </main>
      <style jsx>{`
        .danmu-container {
          position: relative;
          width: 100%;
          height: 200px;
          overflow: hidden;
        }
        .danmu-item {
          position: absolute;
          white-space: nowrap;
          padding: 8px 18px;
          border-radius: 20px;
          font-size: 16px;
          font-weight: 700;
          animation-name: scroll-left;
          animation-timing-function: linear;
          animation-iteration-count: infinite;
        }
        @keyframes scroll-left {
          from { transform: translateX(100vw); }
          to   { transform: translateX(-100%); }
        }
      `}</style>
    </div>
  );
}

export default function GeneratePage() {
  return (
    <Suspense fallback={<div />}> 
      <GenerateClient />
    </Suspense>
  );
}
