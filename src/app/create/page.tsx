'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { ArrowRight, Sparkles, BookOpen, Zap } from 'lucide-react';
import ScreenshotButton from '@/components/UI/ScreenshotButton';

export default function WorkspacePage() {
  const router = useRouter();
  const [selectedIntent, setSelectedIntent] = useState<string>('');
  const [userPrompt, setUserPrompt] = useState<string>('');
  const [user, setUser] = useState<any>(null);
  const [showInspire, setShowInspire] = useState(false);
  const [cards, setCards] = useState<{title:string;prompt:string;directorPrompt?:string}[]>([]);
  const [loadingCards, setLoadingCards] = useState(false);
  const [showNoticeModal, setShowNoticeModal] = useState(false);
  const [firstNotice, setFirstNotice] = useState<any>(null);

  // 基础配置
  const intentMap: Record<string, {label: string; apiCat: string; universe?: string}> = {
    action: { label: '高能场面', apiCat: 'action' },
    detail: { label: '细节补完', apiCat: 'detail' },
    psych:  { label: '心理深挖', apiCat: 'psych' },
    side:   { label: '配角外传', apiCat: 'side' },
    au:     { label: '平行世界', apiCat: 'au' },
    real_person: { label: '真人宇宙', apiCat: 'realPerson', universe: 'real_person' },
  };

  // 加载公告
  const loadNotices = async () => {
    const hasReadFirstNotice = localStorage.getItem('hasReadFirstNotice');
    if (!hasReadFirstNotice) {
      try {
        const response = await fetch('/api/notice');
        const data = await response.json();
        if (data?.items && data.items.length > 0) {
          setFirstNotice(data.items[0]);
          setShowNoticeModal(true);
        }
      } catch (error) {
        console.error('获取公告失败:', error);
      }
    }
  };

  // 用户状态管理
  useEffect(() => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        setUser(JSON.parse(userData));
      } catch (e) {
        localStorage.removeItem('user');
      }
    }
    loadNotices();
  }, []);

  // 核心功能函数
  const handleGenerate = () => {
    if (!selectedIntent || !userPrompt.trim()) return;
    const intentConfig = intentMap[selectedIntent];
    const universe = intentConfig?.universe || 'novel';
    router.push(`/generate?work=${encodeURIComponent('逆袭')}&intent=${selectedIntent}&prompt=${encodeURIComponent(userPrompt)}&universe=${encodeURIComponent(universe)}`);
  };

  const openInspire = async () => {
    if (!selectedIntent) return;
    try {
      setLoadingCards(true);
      setShowInspire(true);
      const cat = intentMap[selectedIntent].apiCat;
      const r = await fetch(`/api/inspirations?cat=${encodeURIComponent(cat)}`);
      const j = await r.json();
      setCards(j?.data?.items || []);
    } finally {
      setLoadingCards(false);
    }
  };

  const handleReadNotice = () => {
    localStorage.setItem('hasReadFirstNotice', 'true');
    setShowNoticeModal(false);
  };

  // 渲染公告内容的 Markdown
  const renderNoticeContent = (content: string) => {
    const lines = content.split('\n');
    return lines.map((line, idx) => {
      if (line.startsWith('### ')) {
        return <h3 key={idx} className="text-xl font-bold mb-3 mt-4 cp-gradient-text">{line.replace('### ', '').trim()}</h3>;
      } else if (line.startsWith('## ')) {
        return <h2 key={idx} className="text-2xl font-bold mb-4 mt-6 cp-gradient-text">{line.replace('## ', '').trim()}</h2>;
      } else if (line.startsWith('# ')) {
        return <h1 key={idx} className="text-3xl font-bold mb-4 mt-6 cp-gradient-text">{line.replace('# ', '').trim()}</h1>;
      } else if (line.trim() === '------') {
        return <hr key={idx} className="my-6 border-gray-300" />;
      } else if (line.includes('**')) {
        const parts = line.split(/(\*\*[^*]+\*\*)/g);
        return (
          <p key={idx} className="mb-3 leading-relaxed" style={{color: '#323272'}}>
            {parts.map((part, i) => 
              part.startsWith('**') && part.endsWith('**') ? 
                <strong key={i} className="cp-gradient-text font-bold">{part.slice(2, -2)}</strong> : 
                part
            )}
          </p>
        );
      } else if (line.trim()) {
        return <p key={idx} className="mb-3 leading-relaxed" style={{color: '#323272'}}>{line}</p>;
      }
      return null;
    });
  };

  return (
    <div className="min-h-screen" style={{
      background: 'linear-gradient(135deg, #2D2562 0%, #4A3B7A 50%, #6B5B3D 100%)'
    }}>
      {/* 顶部导航（品牌） */}
      <nav className="border-b backdrop-blur-md sticky top-0 z-50 frosted-glass-card" style={{
        borderColor: 'rgba(102, 107, 206, 0.3)'
      }}>
        <div className="max-w-6xl mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Image src="/logo-final.svg" alt="逆线" width={112} height={112} />
            <span className="text-5xl font-extrabold cp-gradient-text">逆线</span>
          </div>
          
          <div className="flex items-center gap-6">
            <div className="flex items-center space-x-3 text-sm">
              <button 
                onClick={() => router.push('/notice')} 
                className="frosted-glass-button px-3 py-2 rounded-xl transition-all duration-300 hover:scale-105 font-medium"
                style={{
                  color: '#C2A8F2',
                  textShadow: '0 0 10px rgba(194, 168, 242, 0.3)'
                }}
              >公告</button>
              <button 
                onClick={() => router.push('/creative-space')} 
                className="frosted-glass-button px-3 py-2 rounded-xl transition-all duration-300 hover:scale-105 font-medium"
                style={{
                  color: '#FFD64F',
                  textShadow: '0 0 10px rgba(255, 214, 79, 0.3)'
                }}
              >交错时空</button>
              <button 
                onClick={() => router.push('/titles')} 
                className="frosted-glass-button px-3 py-2 rounded-xl transition-all duration-300 hover:scale-105 font-medium"
                style={{
                  color: '#666BCE',
                  textShadow: '0 0 10px rgba(102, 107, 206, 0.3)'
                }}
              >回响档案馆</button>
              <button 
                onClick={() => router.push('/recharge')} 
                className="frosted-glass-button px-3 py-2 rounded-xl transition-all duration-300 hover:scale-105 font-medium"
                style={{
                  color: '#C2A8F2',
                  textShadow: '0 0 10px rgba(194, 168, 242, 0.3)'
                }}
              >充值</button>
            </div>
            
            {user ? (
              <div className="flex items-center gap-2">
                <button onClick={()=>router.push('/me')} className="px-2 py-1 rounded-full text-xs text-gray-700 bg-gray-100 hover:bg-gray-200 max-w-[120px] truncate">
                  {user.name ? `${user.name}_${user.id?.slice(-10) || 'guest'}` : '游客'}
                </button>
                <span className="px-3 py-1 rounded-full text-xs text-white flex items-center justify-center min-w-[80px]" style={{ background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)' }}>
                  可用章节 {(user.credits ?? 0)}
                </span>
                <button onClick={async()=>{
                  try{
                    if (!user?.id) {
                      alert('用户信息无效，请重新登录');
                      return;
                    }
                    console.log('签到请求用户ID:', user.id);
                    const resp = await fetch('/api/auth/daily-signin', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ userId: user.id }) });
                    const data = await resp.json();
                    console.log('签到API响应:', data);
                    
                    if (data?.ok) {
                      if (data.alreadySigned) {
                        alert('今日已签到');
                      } else if (data.fragmentReward) {
                        const message = data.chapterAwarded 
                          ? '签到成功，获取1枚灵感碎片，3枚碎片已自动兑换1个章节！'
                          : data.message;
                        alert(message);
                        // 更新用户数据
                        const newUser = { 
                          ...user, 
                          credits: data.chapterAwarded ? (user.credits||0) + 1 : user.credits,
                          fragmentBalance: data.newFragmentBalance || (user.fragmentBalance||0)
                        };
                        setUser(newUser);
                        localStorage.setItem('user', JSON.stringify(newUser));
                      }
                    } else {
                      console.error('签到失败:', data);
                      alert(data?.message || data?.error || '签到失败');
                    }
                  } catch(error) {
                    console.error('签到异常:', error);
                    alert('签到失败，请稍后重试');
                  }
                }} className="text-xs text-[#666BCE] hover:opacity-80">每日签到</button>
                <button onClick={() => { localStorage.removeItem('user'); setUser(null); }} className="text-gray-600 hover:text-[#666BCE]">退出</button>
              </div>
            ) : (
              <button onClick={() => router.push('/auth')} className="frosted-glass-button px-4 py-1.5 rounded-full text-white">登录</button>
            )}
          </div>
        </div>
      </nav>

      {/* 顶部紫黄渐变横幅 */}
      <section className="h-[220px] flex items-center justify-center text-white relative overflow-hidden" style={{background: 'linear-gradient(135deg, #666BCE 0%, #666BCE 20%, #C2A8F2 60%, #FFD64F 100%)'}}>
        <div className="absolute inset-0" style={{background: 'radial-gradient(circle at 30% 40%, rgba(255,255,255,0.15) 0%, transparent 40%), radial-gradient(circle at 70% 60%, rgba(255,255,255,0.1) 0%, transparent 40%)'}} />
        <div className="relative z-10 text-center">
          <h1 className="text-3xl md:text-4xl font-extrabold drop-shadow">逆线 · 《逆爱》×《逆袭》创作圣地</h1>
          <p className="mt-3 text-sm md:text-base text-white/90">在无数逆转的线里，重逢唯一的你。</p>
        </div>
      </section>

      {/* 主要创作区域 */}
      <section id="create" className="max-w-5xl mx-auto px-4 py-16">
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-r from-[#666BCE] to-[#C2A8F2] text-white mb-3">
            <Sparkles className="w-6 h-6" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900">拨动命运的逆线</h2>
          <p className="text-gray-500 text-sm mt-1">仅针对《逆爱》电视剧 / 《逆袭》小说的同人创作</p>
        </div>

        {/* 意图选择：6 个 */}
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4 mb-6">
          {Object.entries(intentMap).map(([id, meta]) => (
            <button
              key={id}
              onClick={() => setSelectedIntent(id)}
              className={`p-4 rounded-xl border text-sm font-medium transition-all duration-300 hover:scale-105 hover:shadow-lg ${
                selectedIntent === id 
                  ? 'border-[#666BCE] shadow-lg text-white animate-pulse' 
                  : 'border-gray-200 hover:border-[#C2A8F2] text-gray-700 hover:text-[#666BCE]'
              }`}
              style={selectedIntent === id ? {
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 100%)',
                boxShadow: '0 0 20px rgba(102, 107, 206, 0.4)',
                textShadow: '0 0 10px rgba(0,0,0,0.6)'
              } : {}}
              onMouseEnter={(e) => {
                if (selectedIntent !== id) {
                  e.currentTarget.style.transform = 'scale(1.05) rotateY(5deg)';
                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 107, 206, 0.2)';
                }
              }}
              onMouseLeave={(e) => {
                if (selectedIntent !== id) {
                  e.currentTarget.style.transform = 'scale(1) rotateY(0deg)';
                  e.currentTarget.style.boxShadow = 'none';
                }
              }}
            >
              {meta.label}
            </button>
          ))}
                </div>

        {/* 用户提示输入 + 灵感小助手 */}
        <div className="bg-white rounded-2xl border border-gray-200 p-5">
          <div className="flex items-center justify-between mb-2">
            <div className="text-sm font-semibold text-gray-800">描述你想看的具体场景</div>
            <button
              onClick={openInspire}
              disabled={!selectedIntent}
              title={!selectedIntent? '请先选择一个创作场景':''}
              className={`px-3 py-1.5 rounded-full text-xs transition-all ${selectedIntent? 'text-white':'text-gray-400'} ${selectedIntent? 'bg-gradient-to-r from-[#666BCE] to-[#C2A8F2] hover:opacity-90':'bg-gray-100 cursor-not-allowed'}`}
            >
              💡 灵感小助手
            </button>
          </div>
          <textarea
            value={userPrompt}
            onChange={(e) => setUserPrompt(e.target.value)}
            className="w-full h-28 p-4 rounded-xl border-2 border-gray-100 focus:border-[#C2A8F2] focus:ring-4 focus:ring-[#C2A8F2]/30 outline-none"
            placeholder="输入你的脑洞，或点击💡灵感小助手获取神级提示词。你的描述越具体，AI的演技就越炸裂！"
          />
          <div className="mt-4 flex items-center justify-between">
            <span className="text-xs text-gray-500">你说我是谁老公？</span>
            <button onClick={handleGenerate} disabled={!selectedIntent || !userPrompt.trim()} className="inline-flex items-center gap-2 px-5 py-2 rounded-xl text-white bg-gradient-to-r from-[#666BCE] to-[#C2A8F2] disabled:opacity-50">
              <Zap className="w-4 h-4" /> 立即生成
            </button>
          </div>
        </div>

        {/* 灵感小助手 Modal */}
        {showInspire && (
          <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div className="absolute inset-0 bg-black/30" onClick={()=>setShowInspire(false)} />
            <div className="relative max-w-4xl w-[92%] md:w-[860px] rounded-3xl p-6 backdrop-blur-xl border" style={{
              background: 'rgba(255,255,255,0.6)',
              borderImage: 'linear-gradient(135deg, #666BCE, #C2A8F2, #FFD64F) 1'
            }}>
              <div className="flex items-center justify-between mb-4">
                <div className="text-lg font-bold text-gray-800">灵感小助手 · {intentMap[selectedIntent]?.label}</div>
                <button onClick={()=>setShowInspire(false)} className="text-gray-600 hover:text-[#666BCE]">关闭</button>
              </div>
              {loadingCards ? (
                <div className="text-center text-gray-600 py-10">加载中...</div>
              ) : (
                <div className="max-h-96 overflow-y-auto pr-2">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {cards.map((c, idx) => (
                      <button key={idx}
                        onClick={()=>{ setUserPrompt(c.directorPrompt || c.prompt); setShowInspire(false); }}
                        className="rounded-2xl p-4 text-left transition-transform hover:scale-[1.02] border"
                        style={{
                          background: 'rgba(255,255,255,0.55)',
                          borderImage: 'linear-gradient(135deg, #666BCE, #C2A8F2, #FFD64F) 1',
                          boxShadow: '0 8px 24px rgba(102,107,206,0.12)'
                        }}
                      >
                        <div className="text-sm font-semibold text-gray-800 mb-2">{c.title}</div>
                        <div className="text-xs text-gray-600 leading-relaxed line-clamp-4">{c.directorPrompt || c.prompt}</div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* 入口卡片：光影回廊 / 交错时空 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-10">
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-[#666BCE]/10 to-[#FFD64F]/10 border p-6">
            <h3 className="text-[#666BCE] font-semibold">光影回廊</h3>
            <p className="text-gray-600 text-sm mt-1">捕捉每个心动瞬间</p>
            <button onClick={() => router.push('/gallery')} className="mt-4 inline-flex items-center text-[#666BCE] text-sm">
              进入 <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
          <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-[#666BCE]/10 to-[#FFD64F]/10 border p-6">
            <h3 className="text-[#666BCE] font-semibold">🌈 回响时空</h3>
            <p className="text-gray-600 text-sm mt-1">与同好分享你的热爱，实时弹幕聊天</p>
            <button onClick={() => router.push('/forum')} className="mt-4 inline-flex items-center text-[#666BCE] text-sm">
              进入 <ArrowRight className="w-4 h-4 ml-1" />
            </button>
          </div>
        </div>
      </section>

      {/* 页脚 */}
      <footer className="py-10 text-center text-xs text-gray-400">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex items-center justify-center gap-2">
            <BookOpen className="w-4 h-4 text-gray-300" />
            <span>© 2024 逆线 · 专注《逆爱》/《逆袭》同人创作</span>
          </div>
        </div>
      </footer>

      {/* 公告弹窗 */}
      {showNoticeModal && firstNotice && (
        <div className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4">
          <div className="rounded-3xl p-8 max-w-2xl w-full max-h-[80vh] overflow-y-auto" style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 255, 0.95) 60%, rgba(254, 252, 232, 0.95) 100%)',
            backdropFilter: 'blur(15px)',
            border: '2px solid rgba(102, 107, 206, 0.2)',
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.3)'
          }}>
            <div className="flex items-center gap-4 mb-6">
              <div className="text-3xl">📢</div>
              <h2 className="text-2xl font-bold cp-gradient-text">
                {firstNotice.title}
              </h2>
            </div>
            
            <div className="mb-8 leading-relaxed">
              {renderNoticeContent(firstNotice.content)}
            </div>
            
            <div className="text-center">
              <button 
                onClick={handleReadNotice}
                className="px-8 py-3 rounded-xl text-white font-semibold text-lg hover:shadow-lg transform hover:scale-105 transition-all duration-200"
                style={{background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 60%, #FFD64F 100%)'}}
              >
                已阅
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 「逆线」共创发射台悬浮按钮 */}
      <div className="fixed bottom-8 right-8 z-50">
        <button
          onClick={() => router.push('/feedback')}
          className="group relative w-16 h-16 rounded-full transition-all duration-500 hover:scale-125 animate-pulse"
          style={{
            background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
            boxShadow: '0 8px 32px rgba(102, 107, 206, 0.4), 0 0 40px rgba(255, 214, 79, 0.3)'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.3) rotateY(15deg)';
            e.currentTarget.style.boxShadow = '0 12px 48px rgba(102, 107, 206, 0.6), 0 0 60px rgba(255, 214, 79, 0.5)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1) rotateY(0deg)';
            e.currentTarget.style.boxShadow = '0 8px 32px rgba(102, 107, 206, 0.4), 0 0 40px rgba(255, 214, 79, 0.3)';
          }}
        >
          <div className="text-2xl">🚀</div>
          
          {/* 悬浮提示 */}
          <div className="absolute right-20 top-1/2 transform -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none">
            <div className="bg-black/90 text-white px-4 py-2 rounded-lg whitespace-nowrap text-sm font-bold" style={{
              boxShadow: '0 8px 32px rgba(0,0,0,0.3)'
            }}>
              💫 共创发射台
            </div>
          </div>
        </button>
      </div>

      {/* 截图按钮 */}
      <ScreenshotButton />
    </div>
  );
}
