'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Image from 'next/image';
import { ArrowLeft, Lock, Mail, Key, User, Gift, Eye, EyeOff } from 'lucide-react';

function AuthInner(){
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteFromUrl = searchParams.get('invite') || '';

  const [isLogin, setIsLogin] = useState(true);
  const [mode, setMode] = useState<'otp'|'password'>('otp');
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');
  const [resendSec, setResendSec] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [inviteCode, setInviteCode] = useState(inviteFromUrl);
  const [devShownCode, setDevShownCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(()=>{
    if (resendSec<=0) return; const t = setInterval(()=>setResendSec(s=>s-1), 1000); return ()=>clearInterval(t);
  }, [resendSec]);

  // 响应式检测
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // CP应援色配色方案
  const colors = {
    primary: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
    secondary: 'linear-gradient(135deg, #666BCE 0%, #FFD64F 100%)',
    background: 'linear-gradient(135deg, #0F0B27 0%, #1A1A3A 100%)',
    cardBg: 'rgba(0, 0, 0, 0.35)',
    inputBg: 'rgba(255, 255, 255, 0.08)',
    inputBorder: 'rgba(255, 255, 255, 0.15)',
    text: 'rgba(255, 255, 255, 0.9)',
    textLight: 'rgba(255, 255, 255, 0.7)',
    textDim: 'rgba(255, 255, 255, 0.6)',
    error: 'rgba(239, 68, 68, 0.9)',
    success: 'rgba(34, 197, 94, 0.9)',
    purple: '#666BCE',
    gold: '#FFD64F'
  };

  const handleSubmit = async (e: React.FormEvent)=>{
    e.preventDefault(); setIsLoading(true); setError('');
    try{
      if (isLogin){
        if (mode==='otp'){
          if (!email.endsWith('@qq.com')) { setError('暂时仅支持 QQ 邮箱'); return; }
          if (!/^\d{6}$/.test(otp)) { setError('请输入6位验证码'); return; }
          const r = await fetch('/api/auth/login-email', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ mode:'otp', email, code: otp }) });
          const j = await r.json(); if (j?.success){ localStorage.setItem('user', JSON.stringify(j.user)); router.push('/'); } else setError(j?.error||'登录失败');
        } else {
          if (!email.endsWith('@qq.com')) { setError('暂时仅支持 QQ 邮箱'); return; }
          if (!password) { setError('请输入密码'); return; }
          const r = await fetch('/api/auth/login-email', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ mode:'password', email, password }) });
          const j = await r.json(); if (j?.success){ localStorage.setItem('user', JSON.stringify(j.user)); router.push('/'); } else setError(j?.error||'登录失败');
        }
      } else {
        if (mode==='otp'){
          if (!email.endsWith('@qq.com')) { setError('暂时仅支持 QQ 邮箱'); return; }
          if (!/^\d{6}$/.test(otp)) { setError('请输入6位验证码'); return; }
          if (password.length<6) { setError('密码至少6位'); return; }
          if (password!==confirmPassword) { setError('两次密码不一致'); return; }
          const inviteCodeToSend = (()=>{ try { const u=new URL(inviteCode); return u.searchParams.get('invite')||inviteCode; } catch(_){ const m=inviteCode.match(/invite=([^&]+)/); return m?decodeURIComponent(m[1]):inviteCode; } })();
          const r = await fetch('/api/auth/register-email', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ email, code: otp, password, name, inviteCode: inviteCodeToSend }) });
          const j = await r.json(); if (j?.success){ localStorage.setItem('user', JSON.stringify(j.user)); router.push('/'); } else setError(j?.error||'注册失败');
        } else {
          setError('注册请使用验证码模式');
        }
      }
    } finally { setIsLoading(false); }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: colors.background,
      position: 'relative',
      padding: '20px'
    }}>
      {/* 粒子背景效果 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        backgroundImage: 'radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%), radial-gradient(circle at 80% 20%, rgba(255, 214, 79, 0.3) 0%, transparent 50%)',
        pointerEvents: 'none'
      }} />

      {/* 主内容区 - 使用 Flexbox 而非 Grid */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '100vh',
        position: 'relative',
        zIndex: 10
      }}>
        <div style={{
          width: '100%',
          maxWidth: isMobile ? '95vw' : '480px',
          display: 'flex',
          flexDirection: 'column',
          gap: '24px'
        }}>
          {/* 返回按钮 */}
          <button 
            onClick={() => router.back()} 
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              background: 'none',
              border: 'none',
              color: colors.textLight,
              cursor: 'pointer',
              fontSize: '14px',
              transition: 'all 0.2s ease',
              alignSelf: 'flex-start'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = colors.gold;
              e.currentTarget.style.transform = 'translateX(-4px)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = colors.textLight;
              e.currentTarget.style.transform = 'translateX(0)';
            }}
          >
            <ArrowLeft style={{ width: '18px', height: '18px' }} />
            <span>返回首页</span>
          </button>

          {/* 主要表单卡片 */}
          <div style={{
            backdropFilter: 'blur(32px)',
            backgroundColor: colors.cardBg,
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '24px',
            padding: isMobile ? '32px 24px' : '40px 32px',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)',
            position: 'relative'
          }}>
            {/* Logo 和标题区域 */}
            <div style={{ textAlign: 'center', marginBottom: '32px' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '12px',
                marginBottom: '20px'
              }}>
                <Image src="/logo-final.svg" alt="逆线" width={48} height={48}/>
                <h1 style={{
                  fontSize: '2.5rem',
                  fontWeight: '900',
                  background: colors.primary,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  margin: 0
                }}>逆线</h1>
              </div>

              {isLogin ? (
                <>
                  <h2 style={{
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    background: colors.secondary,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    marginBottom: '8px',
                    lineHeight: 1.3
                  }}>欢迎回到命运的交叉点</h2>
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: colors.textLight,
                    margin: 0
                  }}>继续探索那条属于你的"逆线"</p>
                </>
              ) : (
                <>
                  <h2 style={{
                    fontSize: '1.25rem',
                    fontWeight: 'bold',
                    background: colors.secondary,
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    marginBottom: '8px',
                    lineHeight: 1.3
                  }}>我舍不得你，是所有"逆线"的起点。</h2>
                  <p style={{
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    color: colors.textLight,
                    margin: 0,
                    lineHeight: 1.4
                  }}>让我们一起在莫比乌斯环的无限循环里，亲手定义他们每一次心动的模样。</p>
                </>
              )}
            </div>

            {/* 登录/注册切换 */}
            <div style={{
              display: 'flex',
              backgroundColor: 'rgba(255, 255, 255, 0.1)',
              borderRadius: '16px',
              padding: '4px',
              marginBottom: '24px',
              border: '1px solid rgba(255, 255, 255, 0.1)'
            }}>
              <button
                onClick={() => setIsLogin(true)}
                style={{
                  flex: 1,
                  padding: '12px',
                  borderRadius: '12px',
                  fontSize: '14px',
                  fontWeight: '600',
                  border: 'none',
                  background: isLogin ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                  color: isLogin ? colors.text : colors.textLight,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  backdropFilter: isLogin ? 'blur(16px)' : 'none'
                }}
              >
                登录
              </button>
              <button
                onClick={() => setIsLogin(false)}
                style={{
                  flex: 1,
                  padding: '12px',
                  borderRadius: '12px',
                  fontSize: '14px',
                  fontWeight: '600',
                  border: 'none',
                  background: !isLogin ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                  color: !isLogin ? colors.text : colors.textLight,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  backdropFilter: !isLogin ? 'blur(16px)' : 'none'
                }}
              >
                注册
              </button>
            </div>

            {/* 登录模式选择 */}
            {isLogin && (
              <div style={{
                display: 'flex',
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderRadius: '16px',
                padding: '4px',
                marginBottom: '24px',
                border: '1px solid rgba(255, 255, 255, 0.1)'
              }}>
                <button
                  onClick={() => setMode('otp')}
                  style={{
                    flex: 1,
                    padding: '12px',
                    borderRadius: '12px',
                    fontSize: '14px',
                    fontWeight: '600',
                    border: 'none',
                    background: mode === 'otp' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                    color: mode === 'otp' ? colors.text : colors.textLight,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    backdropFilter: mode === 'otp' ? 'blur(16px)' : 'none'
                  }}
                >
                  验证码登录
                </button>
                <button
                  onClick={() => setMode('password')}
                  style={{
                    flex: 1,
                    padding: '12px',
                    borderRadius: '12px',
                    fontSize: '14px',
                    fontWeight: '600',
                    border: 'none',
                    background: mode === 'password' ? 'rgba(255, 255, 255, 0.2)' : 'transparent',
                    color: mode === 'password' ? colors.text : colors.textLight,
                    cursor: 'pointer',
                    transition: 'all 0.2s ease',
                    backdropFilter: mode === 'password' ? 'blur(16px)' : 'none'
                  }}
                >
                  密码登录
                </button>
              </div>
            )}

            {/* 表单区域 */}
            <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '20px' }}>
              {/* QQ 邮箱输入 */}
              <div>
                <label style={{
                  display: 'block',
                  fontSize: '14px',
                  fontWeight: '600',
                  color: colors.textLight,
                  marginBottom: '8px'
                }}>
                  <Mail style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                  QQ 邮箱
                </label>
                <div style={{ position: 'relative' }}>
                  <input 
                    type="email" 
                    value={email} 
                    onChange={e => setEmail(e.target.value)}
                    placeholder="例如：<EMAIL>"
                    style={{
                      width: '100%',
                      padding: '16px 20px',
                      borderRadius: '16px',
                      border: `1px solid ${colors.inputBorder}`,
                      backgroundColor: colors.inputBg,
                      color: colors.text,
                      fontSize: '16px',
                      outline: 'none',
                      transition: 'all 0.2s ease',
                      backdropFilter: 'blur(16px)',
                      boxSizing: 'border-box'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = colors.purple;
                      e.currentTarget.style.boxShadow = `0 0 0 2px rgba(102, 107, 206, 0.2)`;
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = colors.inputBorder;
                      e.currentTarget.style.boxShadow = 'none';
                    }}
                    required
                  />
                </div>
              </div>

              {/* 密码登录模式 */}
              {isLogin && mode === 'password' && (
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: colors.textLight,
                    marginBottom: '8px'
                  }}>
                    <Lock style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                    密码
                  </label>
                  <div style={{ position: 'relative' }}>
                    <input 
                      type={showPassword ? 'text' : 'password'}
                      value={password}
                      onChange={e => setPassword(e.target.value)}
                      placeholder="请输入密码"
                      style={{
                        width: '100%',
                        padding: '16px 50px 16px 20px',
                        borderRadius: '16px',
                        border: `1px solid ${colors.inputBorder}`,
                        backgroundColor: colors.inputBg,
                        color: colors.text,
                        fontSize: '16px',
                        outline: 'none',
                        transition: 'all 0.2s ease',
                        backdropFilter: 'blur(16px)',
                        boxSizing: 'border-box'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = colors.purple;
                        e.currentTarget.style.boxShadow = `0 0 0 2px rgba(102, 107, 206, 0.2)`;
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = colors.inputBorder;
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      style={{
                        position: 'absolute',
                        right: '16px',
                        top: '50%',
                        transform: 'translateY(-50%)',
                        background: 'none',
                        border: 'none',
                        color: colors.textLight,
                        cursor: 'pointer',
                        padding: '4px'
                      }}
                    >
                      {showPassword ? <EyeOff style={{ width: '20px', height: '20px' }} /> : <Eye style={{ width: '20px', height: '20px' }} />}
                    </button>
                  </div>
                  <div style={{ textAlign: 'right', marginTop: '8px' }}>
                    <a 
                      href="/auth/forgot" 
                      style={{
                        fontSize: '12px',
                        color: colors.purple,
                        textDecoration: 'none',
                        fontWeight: '600'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.textDecoration = 'underline'}
                      onMouseLeave={(e) => e.currentTarget.style.textDecoration = 'none'}
                    >
                      忘记密码？
                    </a>
                  </div>
                </div>
              )}

              {/* 验证码输入 */}
              {(!isLogin || mode === 'otp') && (
                <div>
                  <label style={{
                    display: 'block',
                    fontSize: '14px',
                    fontWeight: '600',
                    color: colors.textLight,
                    marginBottom: '8px'
                  }}>
                    <Key style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                    验证码
                  </label>
                  <div style={{ display: 'flex', gap: '12px' }}>
                    <input 
                      type="text" 
                      value={otp}
                      onChange={e => setOtp(e.target.value)}
                      placeholder="6位验证码"
                      maxLength={6}
                      style={{
                        flex: 1,
                        padding: '16px 20px',
                        borderRadius: '16px',
                        border: `1px solid ${colors.inputBorder}`,
                        backgroundColor: colors.inputBg,
                        color: colors.text,
                        fontSize: '16px',
                        outline: 'none',
                        transition: 'all 0.2s ease',
                        backdropFilter: 'blur(16px)',
                        boxSizing: 'border-box'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = colors.purple;
                        e.currentTarget.style.boxShadow = `0 0 0 2px rgba(102, 107, 206, 0.2)`;
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = colors.inputBorder;
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    />
                    <button 
                      type="button" 
                      disabled={resendSec > 0}
                      onClick={async () => {
                        if (!email.endsWith('@qq.com')) { alert('暂时仅支持 QQ 邮箱'); return; }
                        const r = await fetch('/api/auth/send-email-otp', { 
                          method: 'POST', 
                          headers: {'Content-Type': 'application/json'}, 
                          body: JSON.stringify({ email }) 
                        });
                        const j = await r.json();
                        if (j?.success) {
                          setResendSec(Number(j.cooldown) || 60);
                          if (j.devCode) { 
                            setOtp(String(j.devCode)); 
                            setDevShownCode(String(j.devCode)); 
                          }
                          alert(j.devCode ? `🧪 测试环境验证码：${j.devCode}` : '验证码已发送至 QQ 邮箱');
                        } else {
                          alert(j?.error || '发送失败');
                        }
                      }}
                      style={{
                        padding: '16px 20px',
                        borderRadius: '16px',
                        border: 'none',
                        background: resendSec > 0 
                          ? 'rgba(255, 255, 255, 0.1)' 
                          : colors.primary,
                        color: resendSec > 0 ? colors.textDim : '#000',
                        fontSize: '14px',
                        fontWeight: '600',
                        cursor: resendSec > 0 ? 'not-allowed' : 'pointer',
                        opacity: resendSec > 0 ? 0.6 : 1,
                        whiteSpace: 'nowrap',
                        minWidth: '120px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (resendSec <= 0) {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 107, 206, 0.4)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (resendSec <= 0) {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }
                      }}
                    >
                      {resendSec > 0 ? `重发(${resendSec}s)` : '获取验证码'}
                    </button>
                  </div>
                  {devShownCode && (
                    <div style={{ 
                      marginTop: '8px',
                      padding: '8px 12px',
                      backgroundColor: 'rgba(102, 107, 206, 0.1)',
                      borderRadius: '8px',
                      border: '1px solid rgba(102, 107, 206, 0.2)'
                    }}>
                      <div style={{
                        fontSize: '11px',
                        fontWeight: '600',
                        background: colors.secondary,
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent'
                      }}>
                        🧪 测试环境验证码: {devShownCode}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* 注册专用字段 */}
              {!isLogin && (
                <>
                  {/* 邀请码 */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: colors.textLight,
                      marginBottom: '8px'
                    }}>
                      <Gift style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                      邀请码（选填）
                    </label>
                    <input 
                      type="text" 
                      value={inviteCode}
                      onChange={e => setInviteCode(e.target.value)}
                      placeholder="如果有邀请码可填写"
                      style={{
                        width: '100%',
                        padding: '16px 20px',
                        borderRadius: '16px',
                        border: `1px solid ${colors.inputBorder}`,
                        backgroundColor: colors.inputBg,
                        color: colors.text,
                        fontSize: '16px',
                        outline: 'none',
                        transition: 'all 0.2s ease',
                        backdropFilter: 'blur(16px)',
                        boxSizing: 'border-box'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = colors.gold;
                        e.currentTarget.style.boxShadow = `0 0 0 2px rgba(255, 214, 79, 0.2)`;
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = colors.inputBorder;
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    />
                    <div style={{
                      marginTop: '6px',
                      fontSize: '11px',
                      fontWeight: '600',
                      background: colors.secondary,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      lineHeight: 1.4
                    }}>
                      好友相邀，双方各得 1 章节（截至 2025.08.19）
                    </div>
                  </div>

                  {/* 用户名 */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: colors.textLight,
                      marginBottom: '8px'
                    }}>
                      <User style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                      用户名（选填）
                    </label>
                    <input 
                      type="text" 
                      value={name}
                      onChange={e => setName(e.target.value)}
                      placeholder="用于展示的昵称"
                      style={{
                        width: '100%',
                        padding: '16px 20px',
                        borderRadius: '16px',
                        border: `1px solid ${colors.inputBorder}`,
                        backgroundColor: colors.inputBg,
                        color: colors.text,
                        fontSize: '16px',
                        outline: 'none',
                        transition: 'all 0.2s ease',
                        backdropFilter: 'blur(16px)',
                        boxSizing: 'border-box'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = colors.purple;
                        e.currentTarget.style.boxShadow = `0 0 0 2px rgba(102, 107, 206, 0.2)`;
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = colors.inputBorder;
                        e.currentTarget.style.boxShadow = 'none';
                      }}
                    />
                  </div>

                  {/* 设置密码 */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: colors.textLight,
                      marginBottom: '8px'
                    }}>
                      <Lock style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                      设置密码
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input 
                        type={showPassword ? 'text' : 'password'}
                        value={password}
                        onChange={e => setPassword(e.target.value)}
                        placeholder="至少6位"
                        style={{
                          width: '100%',
                          padding: '16px 50px 16px 20px',
                          borderRadius: '16px',
                          border: `1px solid ${colors.inputBorder}`,
                          backgroundColor: colors.inputBg,
                          color: colors.text,
                          fontSize: '16px',
                          outline: 'none',
                          transition: 'all 0.2s ease',
                          backdropFilter: 'blur(16px)',
                          boxSizing: 'border-box'
                        }}
                        onFocus={(e) => {
                          e.currentTarget.style.borderColor = colors.purple;
                          e.currentTarget.style.boxShadow = `0 0 0 2px rgba(102, 107, 206, 0.2)`;
                        }}
                        onBlur={(e) => {
                          e.currentTarget.style.borderColor = colors.inputBorder;
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        style={{
                          position: 'absolute',
                          right: '16px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          background: 'none',
                          border: 'none',
                          color: colors.textLight,
                          cursor: 'pointer',
                          padding: '4px'
                        }}
                      >
                        {showPassword ? <EyeOff style={{ width: '20px', height: '20px' }} /> : <Eye style={{ width: '20px', height: '20px' }} />}
                      </button>
                    </div>
                  </div>

                  {/* 确认密码 */}
                  <div>
                    <label style={{
                      display: 'block',
                      fontSize: '14px',
                      fontWeight: '600',
                      color: colors.textLight,
                      marginBottom: '8px'
                    }}>
                      <Lock style={{ width: '16px', height: '16px', display: 'inline', marginRight: '6px' }} />
                      确认密码
                    </label>
                    <div style={{ position: 'relative' }}>
                      <input 
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={confirmPassword}
                        onChange={e => setConfirmPassword(e.target.value)}
                        placeholder="再次输入密码"
                        style={{
                          width: '100%',
                          padding: '16px 50px 16px 20px',
                          borderRadius: '16px',
                          border: `1px solid ${colors.inputBorder}`,
                          backgroundColor: colors.inputBg,
                          color: colors.text,
                          fontSize: '16px',
                          outline: 'none',
                          transition: 'all 0.2s ease',
                          backdropFilter: 'blur(16px)',
                          boxSizing: 'border-box'
                        }}
                        onFocus={(e) => {
                          e.currentTarget.style.borderColor = colors.purple;
                          e.currentTarget.style.boxShadow = `0 0 0 2px rgba(102, 107, 206, 0.2)`;
                        }}
                        onBlur={(e) => {
                          e.currentTarget.style.borderColor = colors.inputBorder;
                          e.currentTarget.style.boxShadow = 'none';
                        }}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        style={{
                          position: 'absolute',
                          right: '16px',
                          top: '50%',
                          transform: 'translateY(-50%)',
                          background: 'none',
                          border: 'none',
                          color: colors.textLight,
                          cursor: 'pointer',
                          padding: '4px'
                        }}
                      >
                        {showConfirmPassword ? <EyeOff style={{ width: '20px', height: '20px' }} /> : <Eye style={{ width: '20px', height: '20px' }} />}
                      </button>
                    </div>
                  </div>
                </>
              )}

              {/* 错误提示 */}
              {error && (
                <div style={{
                  padding: '16px 20px',
                  borderRadius: '16px',
                  backgroundColor: 'rgba(239, 68, 68, 0.1)',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  backdropFilter: 'blur(16px)'
                }}>
                  <p style={{
                    color: colors.error,
                    fontSize: '14px',
                    fontWeight: '600',
                    margin: 0
                  }}>{error}</p>
                </div>
              )}

              {/* 提交按钮 */}
              <button 
                type="submit" 
                disabled={isLoading}
                style={{
                  width: '100%',
                  padding: '18px 24px',
                  borderRadius: '16px',
                  border: 'none',
                  background: colors.primary,
                  color: '#000',
                  fontSize: '16px',
                  fontWeight: 'bold',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  opacity: isLoading ? 0.7 : 1,
                  transition: 'all 0.3s ease',
                  boxShadow: '0 8px 25px rgba(102, 107, 206, 0.4)'
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.transform = 'translateY(-3px)';
                    e.currentTarget.style.boxShadow = '0 12px 35px rgba(102, 107, 206, 0.5)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(102, 107, 206, 0.4)';
                  }
                }}
              >
                {isLoading ? '处理中...' : 
                 isLogin ? (mode === 'otp' ? '验证码登录' : '密码登录') : '完成注册'}
              </button>

              {/* 底部提示 */}
              <div style={{
                textAlign: 'center',
                fontSize: '12px',
                color: colors.textDim,
                marginTop: '16px',
                lineHeight: 1.4
              }}>
                提示：短信与其他邮箱登录将很快恢复，当前阶段仅开放 QQ 邮箱
              </div>
            </form>
          </div>

          {/* 内测账户面板 */}
          <div style={{
            backdropFilter: 'blur(32px)',
            backgroundColor: 'rgba(102, 107, 206, 0.1)',
            border: '1px solid rgba(102, 107, 206, 0.3)',
            borderRadius: '20px',
            padding: '24px',
            marginTop: '20px'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              marginBottom: '16px'
            }}>
              <div style={{ fontSize: '1.5rem' }}>🧪</div>
              <h3 style={{
                fontSize: '1.125rem',
                fontWeight: 'bold',
                background: colors.secondary,
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                margin: 0
              }}>测试环境 - 内测账户</h3>
            </div>
            
            <div style={{ 
              display: 'flex',
              flexDirection: isMobile ? 'column' : 'row',
              gap: '16px'
            }}>
              {[
                { email: '<EMAIL>', code: '123456', credits: 1000 },
                { email: '<EMAIL>', code: '234567', credits: 500 },
                { email: '<EMAIL>', code: '345678', credits: 2000 }
              ].map((account, idx) => (
                <div key={idx} style={{
                  flex: 1,
                  padding: '16px',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  borderRadius: '12px',
                  border: '1px solid rgba(255, 255, 255, 0.1)'
                }}>
                  <div style={{
                    fontSize: '12px',
                    fontWeight: '600',
                    color: colors.gold,
                    marginBottom: '8px'
                  }}>内测账户 #{idx + 1}</div>
                  <div style={{
                    fontSize: '13px',
                    color: colors.text,
                    marginBottom: '4px'
                  }}>📧 {account.email}</div>
                  <div style={{
                    fontSize: '13px',
                    color: colors.text,
                    marginBottom: '4px'
                  }}>🔑 验证码: {account.code}</div>
                  <div style={{
                    fontSize: '13px',
                    color: colors.success
                  }}>💰 可用章节: {account.credits}</div>
                </div>
              ))}
            </div>
            
            <div style={{
              marginTop: '16px',
              padding: '12px 16px',
              backgroundColor: 'rgba(255, 214, 79, 0.1)',
              borderRadius: '8px',
              border: '1px solid rgba(255, 214, 79, 0.2)'
            }}>
              <div style={{
                fontSize: '11px',
                fontWeight: '600',
                color: colors.gold,
                marginBottom: '4px'
              }}>🔍 使用说明：</div>
              <div style={{
                fontSize: '11px',
                color: colors.textLight,
                lineHeight: 1.4
              }}>
                • 这些是预置的测试账户，可直接使用<br/>
                • 验证码固定不变，方便快速登录<br/>
                • 每个账户都有充足的章节用于AI生成测试
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AuthClient(){
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center text-gray-500">加载中...</div>}>
      <AuthInner />
    </Suspense>
  );
} 