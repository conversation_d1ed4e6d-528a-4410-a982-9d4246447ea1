"use client";
import { useState } from 'react';

export default function ForgotPasswordClient(){
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [resendSec, setResendSec] = useState(0);
  const [msg, setMsg] = useState('');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md bg-white rounded-2xl shadow-xl p-8">
        <h1 className="text-2xl font-extrabold bg-gradient-to-r from-[#666BCE] via-[#C2A8F2] to-[#FFD64F] bg-clip-text text-transparent text-center">找回密码</h1>

        <div className="mt-6 space-y-4">
          <div>
            <label className="block text-sm text-gray-700 mb-1">QQ 邮箱</label>
            <input type="email" value={email} onChange={e=>setEmail(e.target.value)} placeholder="例如：<EMAIL>" className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#C2A8F2] focus:border-[#666BCE] outline-none transition-all"/>
          </div>

          <div>
            <label className="block text-sm text-gray-700 mb-1">验证码</label>
            <div className="flex gap-2">
              <input value={code} onChange={e=>setCode(e.target.value)} placeholder="6位验证码" className="flex-1 px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#C2A8F2] focus:border-[#666BCE] outline-none transition-all"/>
              <button disabled={resendSec>0} onClick={async()=>{
                setMsg('');
                const r = await fetch('/api/auth/send-reset-otp', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ email }) });
                const j = await r.json();
                if (j?.success){ setResendSec(j.cooldown||60); alert('验证码已发送'); } else { alert(j?.error||'发送失败'); }
              }} className={`px-4 rounded-xl text-white ${resendSec>0?'opacity-60 cursor-not-allowed':'hover:opacity-90'}`} style={{background:'#666BCE'}}>{resendSec>0?`重新发送(${resendSec}s)`:'获取验证码'}</button>
            </div>
          </div>

          <div>
            <label className="block text-sm text-gray-700 mb-1">新密码</label>
            <input type="password" value={newPassword} onChange={e=>setNewPassword(e.target.value)} placeholder="至少6位" className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-[#C2A8F2] focus:border-[#666BCE] outline-none transition-all"/>
          </div>

          <button onClick={async()=>{
            setMsg('');
            const r = await fetch('/api/auth/reset-password', { method:'POST', headers:{'Content-Type':'application/json'}, body: JSON.stringify({ email, code, newPassword }) });
            const j = await r.json();
            if (j?.success){ setMsg('密码已重置，请返回登录'); } else { setMsg(j?.error||'重置失败'); }
          }} className="w-full text-white py-3 px-4 rounded-xl hover:shadow-lg transition-all" style={{background:'#666BCE'}}>确认重置</button>

          {msg && (<div className="text-center text-sm text-gray-600">{msg}</div>)}
        </div>
      </div>
    </div>
  );
} 