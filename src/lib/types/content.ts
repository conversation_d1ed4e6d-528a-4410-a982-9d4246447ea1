// 创作内容类型定义
export interface ContentCard {
  id: string;
  userId: string;
  title: string;
  content: string;
  summary: string; // 自动截取的前100字摘要
  intent: 'action' | 'detail' | 'psych' | 'side' | 'au' | 'real_person' | 'custom' | 'chat'; // 创作意图
  universe: 'novel' | 'real_person'; // 宇宙类型
  isPublic: boolean; // 是否公开
  tags: string[]; // 标签数组
  likeCount: number;
  commentCount: number;
  favoriteCount: number;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  author?: {
    nickname: string;
    avatar?: string;
    id: string;
    titles?: string[];
  };
  userState?: {
    liked: boolean;
    favorited: boolean;
  };
}

// 用户互动记录
export interface UserInteraction {
  id: string;
  userId: string;
  contentId: string;
  type: 'like' | 'comment' | 'favorite' | 'view';
  createdAt: string;
}

// 评论类型
export interface ContentComment {
  id: string;
  contentId: string;
  userId: string;
  userNickname: string;
  userAvatar?: string;
  content: string;
  likeCount: number;
  createdAt: string;
}

// 生成记录统计
export interface GenerationRecord {
  id: string;
  userId: string;
  intent: string;
  promptId?: string; // 如果使用了官方prompt
  universe: string;
  createdAt: string;
}

// 8个版块配置
export interface SectionConfig {
  id: string;
  intent: string;
  title: string;
  subtitle: string;
  color: string;
  gradient: string;
  icon: string;
} 