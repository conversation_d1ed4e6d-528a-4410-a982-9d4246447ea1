import fs from 'fs';
import path from 'path';

// 添加索引缓存
const indexCache: Record<string, IngestIndex | null> = {};
// 添加缓存过期时间（5分钟）
const CACHE_EXPIRY = 5 * 60 * 1000;
const cacheTimestamps: Record<string, number> = {};

export interface ChunkEntry {
  id: string;
  chapIndex: number;
  start: number;
  end: number;
  text: string;
  tokens: string[]; // bigrams
  tf: Record<string, number>;
  // 章节标注（可选）
  chapterNo?: number;
  chapterTitle?: string;
}

export interface IngestIndex {
  workTitle: string;
  sourceFile: string;
  totalLength: number;
  chunkSize: number;
  overlap: number;
  chunks: ChunkEntry[];
  idf: Record<string, number>;
  createdAt: string;
  // 章节映射（可选）
  chapters?: { no: number; title: string; start: number; end: number }[];
}

function ensureDataDir(): string {
  const dataDir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }
  return dataDir;
}

export function loadBookText(inputFilePath?: string): { text: string; filePath: string } {
  const defaultPath = path.join(process.cwd(), '《逆袭》作者：柴鸡蛋.txt');
  const filePath = inputFilePath ? path.resolve(process.cwd(), inputFilePath) : defaultPath;
  if (!fs.existsSync(filePath)) {
    throw new Error(`找不到书稿文件: ${filePath}`);
  }
  const raw = fs.readFileSync(filePath, 'utf8');
  // 保留换行，清理制表和多余空格，但不合并自然空格，避免破坏相邻词距离
  const text = raw
    .replace(/\r\n/g, '\n')
    .replace(/\u00A0/g, ' ')
    .replace(/[\t]+/g, ' ')
    .trim();
  return { text, filePath };
}

// 数字中文混合转数字（简单版，仅用于章节号）
function chineseNumberToInt(s: string): number | null {
  const map: Record<string, number> = { 一:1,二:2,三:3,四:4,五:5,六:6,七:7,八:8,九:9,十:10,百:100,千:1000,零:0,〇:0,两:2 };
  if (/^[0-9]+$/.test(s)) return parseInt(s, 10);
  // 极简实现：仅支持到三位结构
  let total = 0, num = 0, unit = 1;
  for (let i = s.length - 1; i >= 0; i--) {
    const ch = s[i];
    if (map[ch] === undefined) continue;
    const val = map[ch];
    if (val >= 10) { unit = val; if (num === 0) num = 1; total += num * unit; num = 0; unit = 1; }
    else { num = num + val * unit; }
  }
  total += num;
  return total || null;
}

// 粗略章节检测：匹配“第X章/卷/节 标题”，支持阿拉伯数字/中文数字与破折号、副标题
function detectChapters(text: string): { no: number; title: string; start: number }[] {
  const lines = text.split('\n');
  const results: { no: number; title: string; start: number }[] = [];
  let offset = 0;
  const chapterRegex = /^(第([一二三四五六七八九十百千0-9]+)[章卷节部回])[\s—–\-：:，,]*([^\n]*)/;
  const chapterEn = /^(Chapter\s+(\d+))[\s\.:：，,]*([^\n]*)/i;
  let lastNo = 0;
  for (const line of lines) {
    const m = line.match(chapterRegex);
    const e = m ? null : line.match(chapterEn);
    if (m || e) {
      const rawNo = (m ? m[2] : (e ? e[2] : '')) || '';
      const parsed = chineseNumberToInt(rawNo) || (++lastNo);
      lastNo = parsed;
      const title = (m ? m[3] : (e ? e[3] : ''))?.trim() || '';
      results.push({ no: parsed, title, start: offset });
    }
    // +1 for the newline that was split
    offset += line.length + 1;
  }
  if (results.length === 0) {
    results.push({ no: 1, title: '全文', start: 0 });
  }
  return results;
}

export function splitToChunks(text: string, opts: { size?: number; overlap?: number } = {}): ChunkEntry[] {
  const size = opts.size ?? 800;
  const overlap = opts.overlap ?? 150;
  const chunks: ChunkEntry[] = [];
  let start = 0;
  let chapIndex = 0;

  while (start < text.length) {
    const end = Math.min(start + size, text.length);
    const slice = text.slice(start, end).trim();
    if (slice.length > 0) {
      const tokens = toBigrams(slice);
      const tf: Record<string, number> = {};
      tokens.forEach(t => { tf[t] = (tf[t] || 0) + 1; });
      chunks.push({
        id: `${chapIndex}-${start}`,
        chapIndex,
        start,
        end,
        text: slice,
        tokens,
        tf
      });
      chapIndex += 1;
    }
    if (end >= text.length) break;
    const next = end - overlap;
    start = next > start ? next : start + 1;
  }
  return chunks;
}

export function buildIdf(chunks: ChunkEntry[]): Record<string, number> {
  const df: Record<string, number> = {};
  const N = chunks.length;
  chunks.forEach(ch => {
    const seen: Record<string, boolean> = {};
    ch.tokens.forEach(t => { if (!seen[t]) { df[t] = (df[t] || 0) + 1; seen[t] = true; } });
  });
  const idf: Record<string, number> = {};
  Object.entries(df).forEach(([t, d]) => {
    idf[t] = Math.log((N + 1) / (d + 1)) + 1;
  });
  return idf;
}

export function toBigrams(text: string): string[] {
  const cleaned = text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '');
  const tokens: string[] = [];
  for (let i = 0; i < cleaned.length - 1; i++) {
    tokens.push(cleaned.slice(i, i + 2));
  }
  return tokens;
}

export function ingestBookIndex(workTitle: string, inputFilePath?: string, options?: { size?: number; overlap?: number }): IngestIndex {
  const { text, filePath } = loadBookText(inputFilePath);
  const chapters = detectChapters(text);
  const chunks = splitToChunks(text, options);
  // 标注每个chunk所属章节
  chunks.forEach(ch => {
    let assigned = chapters[0];
    for (const c of chapters) {
      if (c.start <= ch.start) assigned = c; else break;
    }
    ch.chapterNo = assigned?.no;
    ch.chapterTitle = assigned?.title || '';
  });
  // 计算chapter end
  const chapterWithEnd = chapters.map((c, idx) => ({
    no: c.no,
    title: c.title,
    start: c.start,
    end: idx < chapters.length - 1 ? chapters[idx + 1].start - 1 : text.length - 1
  }));

  const idf = buildIdf(chunks);
  const index: IngestIndex = {
    workTitle,
    sourceFile: filePath,
    totalLength: text.length,
    chunkSize: options?.size ?? 800,
    overlap: options?.overlap ?? 150,
    chunks,
    idf,
    createdAt: new Date().toISOString(),
    chapters: chapterWithEnd
  };
  const dataDir = ensureDataDir();
  const outPath = path.join(dataDir, `${workTitle}-index.json`);
  fs.writeFileSync(outPath, JSON.stringify(index));
  return index;
}

export function loadIngestIndex(workTitle: string): IngestIndex | null {
  try {
    // 检查缓存
    const now = Date.now();
    const cacheTime = cacheTimestamps[workTitle] || 0;
    
    // 如果缓存存在且未过期，直接返回缓存
    if (indexCache[workTitle] && (now - cacheTime) < CACHE_EXPIRY) {
      return indexCache[workTitle];
    }
    
    const dataDir = ensureDataDir();
    const p = path.join(dataDir, `${workTitle}-index.json`);
    if (!fs.existsSync(p)) return null;
    const content = fs.readFileSync(p, 'utf8');
    const index = JSON.parse(content) as IngestIndex;
    
    // 更新缓存
    indexCache[workTitle] = index;
    cacheTimestamps[workTitle] = now;
    
    return index;
  } catch (error) {
    console.error(`加载索引文件失败 (${workTitle}):`, error);
    return null;
  }
}