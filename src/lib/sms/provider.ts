export interface SmsSendResult {
  ok: boolean;
  message?: string;
  devCode?: string;
}

export interface SmsProvider {
  name: string;
  sendVerificationCode(phone: string, code: string): Promise<SmsSendResult>;
}

class DevProvider implements SmsProvider {
  name = 'dev';
  async sendVerificationCode(phone: string, code: string): Promise<SmsSendResult> {
    return { ok: true, message: `DEV 模式：验证码已生成`, devCode: code };
  }
}

class ConsoleProvider implements SmsProvider {
  name = 'console';
  async sendVerificationCode(phone: string, code: string): Promise<SmsSendResult> {
    console.log(`[SMS][console] phone=${phone}, code=${code}`);
    return { ok: true, message: '已写入控制台' };
  }
}

class AliyunProvider implements SmsProvider {
  name = 'aliyun';
  async sendVerificationCode(_phone: string, _code: string): Promise<SmsSendResult> {
    // 预留：集成阿里云短信 SDK
    // 读取环境变量：ALIYUN_ACCESS_KEY_ID, ALIYUN_ACCESS_KEY_SECRET, ALIYUN_SIGN_NAME, ALIYUN_TEMPLATE_CODE
    return { ok: false, message: '阿里云短信未配置（占位实现）' };
  }
}

class TencentProvider implements SmsProvider {
  name = 'tencent';
  async sendVerificationCode(_phone: string, _code: string): Promise<SmsSendResult> {
    // 预留：集成腾讯云短信 SDK
    // 读取环境变量：TENCENT_SECRET_ID, TENCENT_SECRET_KEY, TENCENT_SDK_APP_ID, TENCENT_SIGN_NAME, TENCENT_TEMPLATE_ID
    return { ok: false, message: '腾讯云短信未配置（占位实现）' };
  }
}

export function getSmsProvider(): SmsProvider {
  const prov = (process.env.SMS_PROVIDER || 'dev').toLowerCase();
  if (prov === 'dev') return new DevProvider();
  if (prov === 'console') return new ConsoleProvider();
  if (prov === 'aliyun') return new AliyunProvider();
  if (prov === 'tencent') return new TencentProvider();
  return new DevProvider();
} 