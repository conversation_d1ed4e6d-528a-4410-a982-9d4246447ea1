// AI服务配置和调用 - 精简版
interface AIProvider {
  name: string;
  apiKey: string;
  baseURL: string;
  model: string;
}

interface GenerationRequest {
  workTitle: string;
  intent: 'sequel' | 'au' | 'style' | 'detail' | 'action' | 'psych' | 'side';
  userPrompt: string;
  workInfo?: any;
}

// AI服务提供商配置
export const AI_PROVIDERS: Record<string, AIProvider> = {
  deepseek: {
    name: 'DeepSeek',
    apiKey: process.env.DEEPSEEK_API_KEY || '',
    baseURL: 'https://api.deepseek.com',
    model: 'deepseek-chat'
  },
  kimi: {
    name: '<PERSON>i',
    apiKey: process.env.KIMI_API_KEY || '',
    baseURL: 'https://api.moonshot.cn/v1',
    model: 'moonshot-v1-8k'
  },
  gemini: {
    name: 'Google Gemini',
    apiKey: process.env.GEMINI_API_KEY || '',
    baseURL: 'https://generativelanguage.googleapis.com/v1beta',
    model: 'gemini-1.5-pro'
  }
};



// 文本分块和向量化（简化版本）
export function chunkText(text: string, chunkSize: number = 500, overlap: number = 50): string[] {
  console.log('📝 开始文本分块，原文长度:', text.length);
  
  // 参数验证
  if (!text || typeof text !== 'string') {
    console.log('⚠️ 无效的文本内容');
    return [];
  }
  
  if (chunkSize <= 0) {
    console.log('⚠️ chunkSize 必须大于0，使用默认值500');
    chunkSize = 500;
  }
  
  if (overlap < 0) {
    console.log('⚠️ overlap 不能为负数，使用默认值50');
    overlap = 50;
  }
  
  // 如果重叠大于等于块大小，调整重叠值
  if (overlap >= chunkSize) {
    overlap = Math.floor(chunkSize / 2);
    console.log('⚠️ overlap 过大，调整为:', overlap);
  }
  
  const chunks: string[] = [];
  let start = 0;
  
  while (start < text.length) {
    const end = Math.min(start + chunkSize, text.length);
    const chunk = text.substring(start, end);
    
    if (chunk.trim().length > 0) {
      chunks.push(chunk.trim());
    }
    
    // 防止无限循环：确保start值增加
    const nextStart = end - overlap;
    if (nextStart <= start) {
      start = start + 1; // 最小步进
    } else {
      start = nextStart;
    }
    
    // 如果已经到了文本末尾，退出循环
    if (end >= text.length) {
      break;
    }
  }
  
  console.log('✅ 文本分块完成，共', chunks.length, '块');
  return chunks;
}

// RAG - 根据用户查询检索相关上下文
export function retrieveRelevantContext(chunks: string[], userQuery: string, topK: number = 3): string[] {
  console.log('🔍 开始检索相关上下文，查询:', userQuery);
  
  // 简化版本：基于关键词匹配（实际应该用向量相似度）
  const queryWords = userQuery.toLowerCase().split(/\s+/);
  
  const scoredChunks = chunks.map(chunk => {
    const chunkLower = chunk.toLowerCase();
    let score = 0;
    
    // 计算匹配分数
    queryWords.forEach(word => {
      if (!word) return;
      try {
        const re = new RegExp(word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        const matches = (chunkLower.match(re) || []).length;
        score += matches;
      } catch {
        // 忽略非法正则
      }
    });
    
    return { chunk, score };
  });
  
  // 按分数排序，取前topK个
  const relevantChunks = scoredChunks
    .sort((a, b) => b.score - a.score)
    .slice(0, topK)
    .map(item => item.chunk);
  
  console.log('✅ 检索完成，找到', relevantChunks.length, '个相关片段');
  return relevantChunks;
}

// 构建最终的AI提示词
export function buildFinalPrompt(request: GenerationRequest, context: string[]): string {
  console.log('🔧 开始构建最终Prompt');
  
  const { workTitle, intent, userPrompt, workInfo } = request;
  
  // 更具创意和个性化的Prompt构建
  let finalPrompt = '';
  
  // 1. 角色设定（更生动）
  finalPrompt += `你是一位才华横溢的同人作家，擅长创作富有感情和文采的文学作品。`;
  
  // 2. 具体创作任务
  const intentInstructions = {
    sequel: `请为《${workTitle}》创作一篇精彩的番外篇`,
    au: `请创作一个《${workTitle}》的现代AU（平行宇宙）故事`,
    style: `请模仿《${workTitle}》的文风创作一个新故事`,
    detail: `请深入展开《${workTitle}》中的细节场景`
  };
  
  finalPrompt += `\n\n【创作任务】\n${intentInstructions[intent]}，主题是："${userPrompt}"\n`;
  
  // 3. 角色信息（更具体）
  if (workInfo && workInfo.characters) {
    finalPrompt += `\n【主要角色】\n`;
    Object.entries(workInfo.characters).forEach(([name, desc]) => {
      finalPrompt += `• ${name}：${desc}\n`;
    });

    // 不OOC与边界
    finalPrompt += `\n【不OOC清单】\n`;
    finalPrompt += `• 对话需符合各自语气与表达方式\n`;
    finalPrompt += `• 行为符合其价值观与底线，不为推动情节而降智\n`;
    finalPrompt += `• 冲突的解决符合人物成长轨迹，避免突兀转性\n`;
  }
  
  // 4. 创作风格指导
  finalPrompt += `\n【创作要求】\n`;
  finalPrompt += `1. 📝 文笔优美，富有感情色彩和文学性\n`;
  finalPrompt += `2. 🎭 人物对话生动自然，符合角色性格\n`;
  finalPrompt += `3. 🌟 情节有趣且有深度，避免老套剧情\n`;
  finalPrompt += `4. 💫 善用细节描写，营造氛围感\n`;
  finalPrompt += `5. 📖 篇幅控制在1800字左右\n`;
  finalPrompt += `6. 🎨 根据用户要求"${userPrompt}"展开具体情节\n\n`;

  // 创作流程与自检
  finalPrompt += `【创作流程】\n`;
  finalPrompt += `• 请先在内部拟定简要大纲（不输出），再输出正文\n`;
  finalPrompt += `• 输出完成后，自检是否存在OOC或风格不一致，必要时对相关段落进行微调\n\n`;
  
  // 参考资料（如果有）
  if (context.length > 0) {
    finalPrompt += `【参考资料（Context）】\n`;
    context.slice(0, 2).forEach((chunk) => {
      finalPrompt += `${chunk.substring(0, 500)}...\n\n`;
    });
  }
  
  // 明确规则，强制参考资料
  finalPrompt += `【规则（Rules）】\n`;
  finalPrompt += `1. 你必须严格模仿“参考资料”中的写作风格、叙事节奏和人物对话方式。\n`;
  finalPrompt += `2. 你的所有情节，都必须基于“参考资料”中展现的人物性格，绝不能OOC。\n`;
  finalPrompt += `3. 不要凭空捏造与“参考资料”无关的核心设定。\n`;
  finalPrompt += `4. 本次创作聚焦“池骋”与“吴所畏”的二人互动；若参考资料出现与斗蛇/观战/下注等无关场景，请忽略那些段落，不要写入。\n\n`;
  
  // 6. 开始创作指令
  finalPrompt += `现在请开始创作，直接输出故事内容，不要添加任何解释或说明：\n\n`;
  
  console.log('✅ Prompt构建完成，长度:', finalPrompt.length);
  console.log('📋 关键信息: 主题=', userPrompt, '意图=', intent);
  
  // 健康检查需要：打印完整 final_prompt
  try {
    console.log('===== FINAL_PROMPT (traditional) START =====');
    console.log(finalPrompt);
    console.log('===== FINAL_PROMPT (traditional) END =====');
  } catch {}
  
  return finalPrompt;
}

// 调用不同的AI服务（带自动切换）- 优先使用Kimi
export async function callAI(prompt: string, provider: string = 'deepseek'): Promise<string> {
  console.log('🤖 开始调用AI服务，首选:', provider);
  
  // 调整优先级：DeepSeek -> Kimi -> Gemini
  const providers = ['deepseek', 'kimi', 'gemini'];
  // 始终按我们的优先列表从头开始尝试
  const startIndex = 0;
  
  console.log('📋 AI服务调用顺序:', providers);
  
  // 按指定顺序尝试调用AI服务
  for (let i = 0; i < providers.length; i++) {
    const currentProvider = providers[(startIndex + i) % providers.length];
    const config = AI_PROVIDERS[currentProvider];
    
    console.log(`\n🎯 [${i+1}/${providers.length}] 尝试调用 ${currentProvider}...`);
    
    if (!config) {
      console.log(`❌ ${currentProvider} 配置不存在`);
      continue;
    }
    
    if (!config.apiKey) {
      console.log(`⚠️ ${currentProvider} 未配置API密钥，跳过`);
      console.log(`   期望的API密钥格式: ${currentProvider === 'gemini' ? 'AIza...' : 'sk-...'}`);
      continue;
    }
    
    console.log(`✅ ${config.name} API密钥已配置: ${config.apiKey.substring(0, 8)}...`);
    
    try {
      console.log(`📡 向 ${config.baseURL} 发送请求...`);
      console.log(`📝 Prompt长度: ${prompt.length} 字符`);
      
      let result = '';
      
      switch (currentProvider) {
        case 'gemini':
          result = await callGemini(prompt, config);
          break;
        case 'kimi':
          result = await callKimi(prompt, config);
          break;
        case 'deepseek':
          result = await callDeepSeek(prompt, config);
          break;
        default:
          throw new Error(`未实现的AI服务: ${currentProvider}`);
      }
      
      console.log(`🎉 ${config.name} 调用成功！`);
      console.log(`📄 生成内容长度: ${result.length} 字符`);
      console.log(`🔍 内容预览: ${result.substring(0, 100)}...`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ ${config.name} 调用失败:`, error);
      const err = error as { name?: string; message?: string };
      console.error(`   错误类型: ${err?.name || 'UnknownError'}`);
      console.error(`   错误详情: ${err?.message || 'No message'}`);
      
      if (i === providers.length - 1) {
        // 如果所有服务都失败了
        const finalError = new Error(`所有AI服务都不可用。最后错误: ${err?.message || 'Unknown'}`);
        console.error('🚨 所有AI服务调用失败，抛出最终错误');
        throw finalError;
      }
      console.log(`🔄 ${config.name}失败，切换到下一个AI服务...`);
    }
  }
  
  throw new Error('没有可用的AI服务');
}

async function callGemini(prompt: string, config: AIProvider): Promise<string> {
  const url = `${config.baseURL}/models/${config.model}:generateContent?key=${config.apiKey}`;
  const response = await fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      contents: [{ parts: [{ text: prompt }] }],
      generationConfig: { temperature: 0.2, topP: 0.8, topK: 40, maxOutputTokens: 8192 }
    }),
  });
  const ctype = response.headers.get('content-type') || '';
  if (!response.ok || !ctype.includes('application/json')) {
    const text = await response.text().catch(() => '');
    throw new Error(`Gemini API请求失败: ${response.status} ${response.statusText} body=${text.slice(0,200)}`);
  }
  try {
  const data = await response.json();
  if (data.candidates && data.candidates[0] && data.candidates[0].content) {
    return data.candidates[0].content.parts[0].text;
  }
  throw new Error('Gemini API响应格式错误');
  } catch (e:any) {
    throw new Error(`Gemini 响应解析失败: ${e?.message||String(e)}`);
  }
}

async function callKimi(prompt: string, config: AIProvider): Promise<string> {
  console.log('📞 调用Kimi API...');
  console.log(`🔗 请求URL: ${config.baseURL}/chat/completions`);
  const requestBody = { model: config.model, messages: [{ role: 'user', content: prompt }], temperature: 0.2, max_tokens: 8192 };
  console.log('📦 请求体:', JSON.stringify(requestBody, null, 2));
  const response = await fetch(`${config.baseURL}/chat/completions`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${config.apiKey}` },
    body: JSON.stringify(requestBody),
  });
  const ctype = response.headers.get('content-type') || '';
  if (!response.ok || !ctype.includes('application/json')) {
    const text = await response.text().catch(() => '');
    throw new Error(`Kimi API请求失败: ${response.status} ${response.statusText} body=${text.slice(0,200)}`);
  }
  try {
  const data = await response.json();
  if (data.choices && data.choices[0] && data.choices[0].message) {
    const result = data.choices[0].message.content;
    console.log('✅ Kimi响应解析成功，内容长度:', result.length);
    return result;
  }
    throw new Error(`Kimi API响应格式错误: ${JSON.stringify(data).slice(0,200)}`);
  } catch (e:any) {
    throw new Error(`Kimi 响应解析失败: ${e?.message||String(e)}`);
  }
}

async function callDeepSeek(prompt: string, config: AIProvider): Promise<string> {
  console.log('📞 调用DeepSeek API...');
  console.log(`🔗 请求URL: ${config.baseURL}/chat/completions`);
  const requestBody = { model: config.model, messages: [{ role: 'user', content: prompt }], temperature: 0.2, max_tokens: 8192 };
  console.log('📦 请求体:', JSON.stringify(requestBody, null, 2));
  const response = await fetch(`${config.baseURL}/chat/completions`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${config.apiKey}` },
    body: JSON.stringify(requestBody),
  });
  const ctype = response.headers.get('content-type') || '';
  if (!response.ok || !ctype.includes('application/json')) {
    const text = await response.text().catch(() => '');
    throw new Error(`DeepSeek API请求失败: ${response.status} ${response.statusText} body=${text.slice(0,200)}`);
  }
  try {
  const data = await response.json();
  if (data.choices && data.choices[0] && data.choices[0].message) {
    const result = data.choices[0].message.content;
    console.log('✅ DeepSeek响应解析成功，内容长度:', result.length);
    return result;
  }
    throw new Error(`DeepSeek API响应格式错误: ${JSON.stringify(data).slice(0,200)}`);
  } catch (e:any) {
    throw new Error(`DeepSeek 响应解析失败: ${e?.message||String(e)}`);
  }
}

// 主要的生成函数
export async function generateContent(request: GenerationRequest, aiProvider: string = 'kimi'): Promise<string> {
  console.log('🚀 开始AI内容生成流程');
  console.log('📝 请求参数:', JSON.stringify(request, null, 2));
  
  try {
    // 1. 如果有URL，先抓取内容
    let rawContent = '';
    if (request.workTitle.startsWith('http')) {
      rawContent = await scrapeWebContent(request.workTitle);
    } else {
      // 使用预设的作品信息
      if (request.workInfo && request.workInfo.sampleText) {
        rawContent = request.workInfo.sampleText;
      }
    }
    
    // 2. 文本分块
    let chunks: string[] = [];
    if (rawContent) {
      chunks = chunkText(rawContent);
    }
    
    // 3. RAG检索
    let relevantContext: string[] = [];
    if (chunks.length > 0) {
      relevantContext = retrieveRelevantContext(chunks, request.userPrompt);
    }
    
    // 4. 构建最终Prompt
    const finalPrompt = buildFinalPrompt(request, relevantContext);
    
    // 5. 调用AI
    const generatedContent = await callAI(finalPrompt, aiProvider);
    
    console.log('✅ 内容生成完成');
    return generatedContent;
    
  } catch (error) {
    console.error('❌ 内容生成失败:', error);
    const err = error as Error;
    throw new Error(err?.message || '内容生成失败');
  }
}