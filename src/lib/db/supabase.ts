import { createClient, SupabaseClient } from '@supabase/supabase-js';

const SUPABASE_URL = process.env.SUPABASE_URL || '';
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || '';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

let anonClient: SupabaseClient | null = null;
let serviceClient: SupabaseClient | null = null;

export function getSupabaseClient(): SupabaseClient | null {
  try {
    if (!SUPABASE_URL || !SUPABASE_ANON_KEY) return null;
    if (!anonClient) {
      anonClient = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
        auth: { persistSession: false }
      });
    }
    return anonClient;
  } catch {
    return null;
  }
}

export function getServiceSupabaseClient(): SupabaseClient | null {
  try {
    if (!SUPABASE_URL || !SUPABASE_SERVICE_ROLE_KEY) return null;
    if (!serviceClient) {
      serviceClient = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
        auth: { persistSession: false }
      });
    }
    return serviceClient;
  } catch {
    return null;
  }
} 