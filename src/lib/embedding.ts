const GEMINI_API_KEY = process.env.GEMINI_API_KEY || '';

async function embedOne(text: string): Promise<number[]> {
	if (!GEMINI_API_KEY) throw new Error('Missing GEMINI_API_KEY');
	const url = 'https://generativelanguage.googleapis.com/v1beta/models/text-embedding-004:embedContent?key=' + GEMINI_API_KEY;
	const payload = { content: { parts: [{ text }] } } as any;
	const resp = await fetch(url, {
		method: 'POST',
		headers: { 'Content-Type': 'application/json' },
		body: JSON.stringify(payload),
	});
	if (!resp.ok) {
		const t = await resp.text().catch(()=> '');
		throw new Error(`embedContent failed: ${resp.status} ${resp.statusText} ${t.slice(0,200)}`);
	}
	const data = await resp.json();
	const emb = (data && (data.embedding || (data.embeddings && data.embeddings[0]))) || {};
	const vec = emb.values || emb.value || emb.valuesArray || [];
	if (!Array.isArray(vec) || vec.length === 0) throw new Error('embedContent invalid response');
	return vec as number[];
}

export async function embedTextsGoogle(texts: string[], concurrency = 4): Promise<number[][]> {
	const results: number[][] = new Array(texts.length);
	let i = 0;
	async function worker() {
		while (true) {
			const idx = i++;
			if (idx >= texts.length) break;
			try {
				results[idx] = await embedOne(texts[idx]);
			} catch (e:any) {
				console.error('embed error:', e?.message || String(e));
				results[idx] = [] as unknown as number[];
			}
		}
	}
	const workers = Array.from({ length: Math.min(concurrency, texts.length || 1) }, () => worker());
	await Promise.all(workers);
	return results;
}

export async function embedQuery(text: string): Promise<number[]> {
	const [v] = await embedTextsGoogle([text], 1);
	return v;
} 