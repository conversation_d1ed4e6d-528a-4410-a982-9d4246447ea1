// 智能小说检索和内容库系统
interface NovelData {
  title: string;
  author: string;
  genre: string;
  tags: string[];
  summary: string;
  sampleText: string;
  characters: { [name: string]: string };
  writingStyle: string;
  popularity: number;
}

// 精选小说内容库
export const NOVEL_DATABASE: NovelData[] = [
  {
    title: "天官赐福",
    author: "墨香铜臭",
    genre: "古风仙侠",
    tags: ["仙侠", "古风", "情感", "成长", "救赎"],
    summary: "太子谢怜第三次飞升上仙，却意外遇到了命中注定的人",
    sampleText: `
      那一年，谢怜十七岁，是金枝玉叶的太子殿下，意气风发，朝气蓬勃。
      他相信自己是要拯救苍生的神，要成为万民爱戴的仙人。那时候的他，眼中有光，心中有梦，以为自己无所不能。
      
      "我要成为最强的神仙，保护所有人！"少年太子站在高台上，对着满城百姓如是说道。
      
      台下的百姓欢声雷动，而不远处，一个白衣少年默默注视着他，眼中含着难以言喻的温柔。
      
      谢怜当时并不知道，那个少年会成为他生命中最重要的存在...
    `,
    characters: {
      "谢怜": "温和善良的太子，后成为神官，经历坎坷但内心依然纯净",
      "花城": "神秘强大的鬼王，深爱谢怜，愿为他付出一切"
    },
    writingStyle: "现代白话，情感细腻，对话生动，擅长通过细节展现人物内心",
    popularity: 95
  },
  {
    title: "魔道祖师",
    author: "墨香铜臭", 
    genre: "古风修真",
    tags: ["修真", "古风", "悬疑", "友情", "成长"],
    summary: "魏无羡重生后与蓝忘机重逢，共同解开多年前的谜团",
    sampleText: `
      夷陵老祖魏无羡，死后十三年，被人献舍重生。
      
      醒来第一眼看到的，就是一双浅色的眼眸，冷如秋水，美如寒星。
      "蓝忘机？"魏无羡几乎不敢置信，"你怎么会在这里？"
      
      蓝忘机没有说话，只是伸出手来，将他从地上扶起。那手心温热，传递着十三年来从未断绝的牵挂。
      
      "魏婴，"蓝忘机终于开口，声音一如从前的清冷，却带着难以察觉的颤抖，"你回来了。"
    `,
    characters: {
      "魏无羡": "洒脱不羁的修士，聪明机智，为了正义不惜承担骂名",
      "蓝忘机": "清冷内敛的蓝氏二公子，内心火热，对魏无羡情深如海"
    },
    writingStyle: "古典与现代结合，悬疑推理强，人物对话富有个性",
    popularity: 98
  },
  {
    title: "默读",
    author: "priest",
    genre: "现代悬疑",
    tags: ["悬疑", "推理", "现代", "心理", "成长"],
    summary: "刑警队长骆闻舟与心理咨询师费渡合作破案，彼此救赎",
    sampleText: `
      费渡站在窗前，夕阳斜照在他的侧脸上，在那张精致得近乎妖异的面容上投下阴影。
      
      "你知道最可怕的不是黑暗本身，"他轻声说道，"而是习惯了黑暗的人，突然看到了光。"
      
      骆闻舟走到他身后，看着费渡颤抖的肩膀。这个表面上永远优雅从容的男人，此刻却显得如此脆弱。
      
      "那就让光一直亮着。"骆闻舟伸出手，轻抚费渡的后颈，"我陪你。"
    `,
    characters: {
      "费渡": "心理咨询师，外表优雅内心复杂，拥有黑暗的过去",
      "骆闻舟": "刑警队长，正义感强，愿意成为费渡的光"
    },
    writingStyle: "心理描写深刻，氛围营造出色，文字含蓄而有力",
    popularity: 92
  },
  {
    title: "长相思",
    author: "桐华",
    genre: "古风言情", 
    tags: ["古风", "言情", "神话", "情感", "成长"],
    summary: "神农之女小夭历经磨难寻找归属，在三个男子间徘徊",
    sampleText: `
      小夭站在桃花树下，微风吹过，粉色的花瓣如雨般飘落。
      
      "相柳说过，他愿意陪我到天涯海角。璟说过，他会给我一个家。防风邶说过，他只想让我快乐。"小夭喃喃自语，眼中含泪，"可是为什么，我谁也选不了？"
      
      桃花依旧飘落，落在她的肩头，落在她的心里。有些选择，并不是因为不够爱，而是因为爱得太深，深到不忍伤害任何一个人。
      
      "也许，我注定要一个人走完这一生。"
    `,
    characters: {
      "小夭": "神农之女，聪慧坚强，渴望真情却饱受情感困扰",
      "相柳": "九头妖蛇，外冷内热，深爱小夭却不善表达",
      "涂山璟": "温润如玉的公子，对小夭一往情深"
    },
    writingStyle: "古典优美，情感丰富，善用环境描写烘托情绪",
    popularity: 88
  }
];

// 智能关键词提取
export function extractKeywords(text: string): string[] {
  const keywords: string[] = [];
  
  // 情感词汇
  const emotions = ['爱情', '友情', '亲情', '思念', '离别', '重逢', '误会', '和解', '感动', '心痛', '甜蜜', '温暖'];
  // 情节元素
  const plotElements = ['相遇', '分别', '重逢', '误会', '表白', '救赎', '成长', '冒险', '秘密', '真相'];
  // 环境场景
  const scenes = ['校园', '古代', '现代', '仙界', '人间', '江湖', '朝堂', '家庭', '职场'];
  // 人物关系
  const relationships = ['师父', '徒弟', '朋友', '恋人', '夫妻', '兄弟', '姐妹', '仇人', '知己'];
  
  const allKeywords = [...emotions, ...plotElements, ...scenes, ...relationships];
  
  allKeywords.forEach(keyword => {
    if (text.includes(keyword)) {
      keywords.push(keyword);
    }
  });
  
  return keywords;
}

// 基于语义的智能检索
export function semanticSearch(
  userQuery: string,
  database: NovelData[] = NOVEL_DATABASE,
  topK: number = 3
): {
  results: Array<{
    novel: NovelData;
    score: number;
    matchReasons: string[];
  }>;
  suggestions: string[];
} {
  console.log('🔍 开始语义搜索:', userQuery);
  
  const queryKeywords = extractKeywords(userQuery.toLowerCase());
  const scoredResults: Array<{
    novel: NovelData;
    score: number;
    matchReasons: string[];
  }> = [];
  
  database.forEach(novel => {
    let score = 0;
    const matchReasons: string[] = [];
    
    // 1. 标题匹配（高权重）
    if (novel.title.toLowerCase().includes(userQuery.toLowerCase())) {
      score += 50;
      matchReasons.push('标题匹配');
    }
    
    // 2. 标签匹配（高权重）
    novel.tags.forEach(tag => {
      if (userQuery.toLowerCase().includes(tag.toLowerCase())) {
        score += 30;
        matchReasons.push(`标签匹配: ${tag}`);
      }
    });
    
    // 3. 关键词匹配（中权重）
    queryKeywords.forEach(keyword => {
      if (novel.summary.includes(keyword) || 
          novel.sampleText.includes(keyword) ||
          novel.tags.some(tag => tag.includes(keyword))) {
        score += 20;
        matchReasons.push(`关键词匹配: ${keyword}`);
      }
    });
    
    // 4. 类型匹配（中权重）
    if (novel.genre.toLowerCase().includes(userQuery.toLowerCase()) ||
        userQuery.toLowerCase().includes(novel.genre.toLowerCase())) {
      score += 25;
      matchReasons.push('类型匹配');
    }
    
    // 5. 作者匹配（低权重）
    if (novel.author.toLowerCase().includes(userQuery.toLowerCase())) {
      score += 15;
      matchReasons.push('作者匹配');
    }
    
    // 6. 内容语义匹配（模糊匹配）
    const contentMatch = calculateContentSimilarity(userQuery, novel.sampleText);
    score += contentMatch * 10;
    if (contentMatch > 3) {
      matchReasons.push('内容语义相似');
    }
    
    // 7. 流行度加权
    score += novel.popularity * 0.1;
    
    if (score > 0) {
      scoredResults.push({
        novel,
        score,
        matchReasons
      });
    }
  });
  
  // 按分数排序
  const sortedResults = scoredResults
    .sort((a, b) => b.score - a.score)
    .slice(0, topK);
  
  // 生成搜索建议
  const suggestions = generateSearchSuggestions(userQuery, database);
  
  console.log('✅ 检索完成，找到', sortedResults.length, '个相关结果');
  
  return {
    results: sortedResults,
    suggestions
  };
}

// 计算内容相似度（简化版）
function calculateContentSimilarity(query: string, content: string): number {
  const queryWords = query.toLowerCase().split(/\s+/);
  const contentWords = content.toLowerCase().split(/\s+/);
  
  let matches = 0;
  queryWords.forEach(queryWord => {
    if (contentWords.some(contentWord => 
        contentWord.includes(queryWord) || queryWord.includes(contentWord))) {
      matches++;
    }
  });
  
  return matches;
}

// 生成搜索建议
function generateSearchSuggestions(query: string, database: NovelData[]): string[] {
  const suggestions: Set<string> = new Set();
  
  // 基于数据库内容生成建议
  database.forEach(novel => {
    novel.tags.forEach(tag => {
      if (tag.includes(query) || query.includes(tag)) {
        suggestions.add(`试试搜索: ${tag}`);
      }
    });
    
    if (novel.author.includes(query)) {
      suggestions.add(`${novel.author}的其他作品`);
    }
  });
  
  // 通用搜索建议
  const commonSuggestions = [
    '古风仙侠小说',
    '现代都市情感',
    '悬疑推理故事', 
    '校园青春文学',
    '历史架空小说'
  ];
  
  commonSuggestions.forEach(suggestion => {
    if (Math.random() > 0.5) { // 随机添加一些通用建议
      suggestions.add(suggestion);
    }
  });
  
  return Array.from(suggestions).slice(0, 5);
}

// 基于检索结果生成内容
export async function generateFromSearch(
  userQuery: string,
  searchResults: any[],
  customPrompt: string = ''
): Promise<{
  content: string;
  usedReferences: string[];
  metadata: any;
}> {
  console.log('📚 基于检索结果生成内容...');
  
  if (searchResults.length === 0) {
    throw new Error('没有找到相关的参考内容');
  }
  
  // 选择最佳匹配的小说作为主要参考
  const primaryReference = searchResults[0].novel;
  const usedReferences = searchResults.map(r => r.novel.title);
  
  // 构建参考上下文
  let referenceContext = '';
  searchResults.slice(0, 2).forEach(result => {
    referenceContext += `\n【参考作品：${result.novel.title}】\n`;
    referenceContext += `作者风格：${result.novel.writingStyle}\n`;
    referenceContext += `文本片段：${result.novel.sampleText.substring(0, 300)}...\n`;
  });
  
  // 构建最终提示词
  const finalPrompt = `
你是一位专业的文学创作者，请基于以下参考作品的风格创作一篇高质量的故事。

【创作主题】${customPrompt || userQuery}

【主要参考风格】
作品：《${primaryReference.title}》
作者：${primaryReference.author}
风格特色：${primaryReference.writingStyle}
主要人物：${Object.entries(primaryReference.characters).map(([name, desc]) => `${name} - ${desc}`).join('；')}

${referenceContext}

【创作要求】
1. 模仿参考作品的文风和叙事风格
2. 保持文字的文学性和感染力  
3. 人物对话要符合角色特点
4. 篇幅控制在1500-2000字
5. 情节要有起伏，情感要真挚动人

请直接开始创作，不要添加任何解释：
`;

  try {
    // 使用增强AI服务生成内容
    const { generateHighQualityContent } = require('./enhanced-ai-service');
    
    const result = await generateHighQualityContent(finalPrompt, {
      authorStyle: primaryReference.author,
      referenceText: primaryReference.sampleText
    });
    
    return {
      content: result.content,
      usedReferences,
      metadata: {
        searchQuery: userQuery,
        primaryReference: primaryReference.title,
        matchScores: searchResults.map(r => r.score),
        styleAnalysis: result.styleAnalysis
      }
    };
    
  } catch (error) {
    console.error('❌ 基于检索的生成失败:', error);
    throw error;
  }
}

// 扩展小说数据库（用户可以添加自己的内容）
export function addNovelToDatabase(novelData: Partial<NovelData>): void {
  const newNovel: NovelData = {
    title: novelData.title || '未知标题',
    author: novelData.author || '佚名',
    genre: novelData.genre || '其他',
    tags: novelData.tags || [],
    summary: novelData.summary || '',
    sampleText: novelData.sampleText || '',
    characters: novelData.characters || {},
    writingStyle: novelData.writingStyle || '现代白话',
    popularity: novelData.popularity || 50
  };
  
  NOVEL_DATABASE.push(newNovel);
  console.log('✅ 新小说已添加到数据库:', newNovel.title);
}