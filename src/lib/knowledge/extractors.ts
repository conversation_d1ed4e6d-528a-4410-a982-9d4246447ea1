import fs from 'fs';
import path from 'path';
import { IngestIndex, loadIngestIndex } from '../ingest/text-ingest';

export interface CharacterEntry { name: string; count: number; aliases: string[] }
export interface EventEntry { chapIndex: number; summary: string; characters: string[] }
export interface DialogueEntry { chapIndex: number; speaker?: string; text: string }

const CORE_NAMES = ['吴所畏','池骋', '郭城宇', '姜小帅', '岳悦', '汪硕', '汪朕', '刚子', '小天'];

export function extractKnowledge(workTitle: string) {
  const index = loadIngestIndex(workTitle);
  if (!index) throw new Error('未找到索引，请先 /api/ingest');

  const chars = extractCharacters(index);
  const events = extractEvents(index, chars.map(c=>c.name));
  const dialogues = extractDialogues(index, chars.map(c=>c.name));

  const dir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
  fs.writeFileSync(path.join(dir, `${workTitle}-characters.json`), JSON.stringify(chars, null, 2));
  fs.writeFileSync(path.join(dir, `${workTitle}-events.json`), JSON.stringify(events, null, 2));
  fs.writeFileSync(path.join(dir, `${workTitle}-dialogues.json`), JSON.stringify(dialogues, null, 2));

  return { characters: chars.length, events: events.length, dialogues: dialogues.length };
}

export function extractCharacters(index: IngestIndex): CharacterEntry[] {
  const freq: Record<string, number> = {};
  const nameRegex = /[\u4e00-\u9fa5]{2,4}/g;
  index.chunks.forEach(ch => {
    const set = new Set((ch.text.match(nameRegex) || []));
    set.forEach(n => { freq[n] = (freq[n] || 0) + 1; });
  });
  // 选出高频姓名，并包含核心人物
  const entries = Object.entries(freq)
    .map(([name, count]) => ({ name, count, aliases: [] as string[] }))
    .filter(e => e.count >= 3)
    .sort((a,b)=> b.count - a.count);
  CORE_NAMES.forEach(n => { if (!entries.find(e => e.name===n)) entries.unshift({ name: n, count: freq[n]||0, aliases: []}); });
  return entries.slice(0, 50);
}

export function extractEvents(index: IngestIndex, names: string[]): EventEntry[] {
  const events: EventEntry[] = [];
  const verbHints = /(打球|比赛|走|看|说|打电话|相遇|拥抱|离开|回头|沉默|微笑|皱眉|哭|笑|推|拉|抱|吻|坐|站|跑|跳)/;
  index.chunks.forEach(ch => {
    const sentences = ch.text.split(/[。！？!?]/).map(s=>s.trim()).filter(Boolean);
    sentences.forEach(s => {
      if (verbHints.test(s)) {
        const involved = names.filter(n => s.includes(n)).slice(0, 4);
        if (involved.length) {
          events.push({ chapIndex: ch.chapIndex, summary: s.slice(0, 60), characters: involved });
        }
      }
    });
  });
  // 去重/合并相邻章节事件
  const uniq: EventEntry[] = [];
  const seen = new Set<string>();
  events.forEach(e => {
    const key = e.chapIndex+':'+e.summary;
    if (!seen.has(key)) { uniq.push(e); seen.add(key); }
  });
  return uniq.slice(0, 3000);
}

export function extractDialogues(index: IngestIndex, names: string[]): DialogueEntry[] {
  const list: DialogueEntry[] = [];
  const dlgRegex = /[“"「『]([^”"」』]{2,200})[”"」』]/g;
  index.chunks.forEach(ch => {
    const m = ch.text.matchAll(dlgRegex);
    for (const x of m) {
      const text = x[1];
      const speaker = names.find(n => text.includes(n)) || undefined;
      list.push({ chapIndex: ch.chapIndex, speaker, text });
    }
  });
  return list.slice(0, 5000);
} 