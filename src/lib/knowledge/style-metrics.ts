import fs from 'fs';
import path from 'path';

export interface StyleMetrics {
  avgSentenceLen: number;
  p50SentenceLen: number;
  p90SentenceLen: number;
  dialogueRatio: number; // 含引号句占比
  exclamationRate: number; // ！比例
  shortSentenceRatio: number; // <15字占比
  firstPersonRatio: number; // 我/我们 出现率
}

export function computeStyleMetricsFromText(text: string): StyleMetrics {
  const norm = text.replace(/\s+/g, ' ').trim();
  const sentences = norm.split(/[。！？!?…]+/).map(s => s.trim()).filter(Boolean);
  const lens = sentences.map(s => s.length);
  const avg = lens.length ? lens.reduce((a,b)=>a+b,0)/lens.length : 0;
  const sorted = [...lens].sort((a,b)=>a-b);
  const p = (arr:number[], q:number)=> arr.length ? arr[Math.min(arr.length-1, Math.floor(q*(arr.length-1)))] : 0;
  const p50 = p(sorted, 0.5);
  const p90 = p(sorted, 0.9);
  const dialogueCount = (norm.match(/[“"「『].+?[”"」』]/g) || []).length;
  const sentenceCount = Math.max(1, sentences.length);
  const exclamationCount = (text.match(/！|!/g) || []).length;
  const shortCount = sentences.filter(s => s.length < 15).length;
  const firstPersonCount = (text.match(/\b我(们)?\b/g) || []).length;

  return {
    avgSentenceLen: +avg.toFixed(2),
    p50SentenceLen: p50,
    p90SentenceLen: p90,
    dialogueRatio: +(dialogueCount / sentenceCount).toFixed(2),
    exclamationRate: +(exclamationCount / Math.max(1, text.length)).toFixed(4),
    shortSentenceRatio: +(shortCount / sentenceCount).toFixed(2),
    firstPersonRatio: +(firstPersonCount / Math.max(1, text.length/6)).toFixed(4),
  };
}

export function saveStyleMetrics(workTitle: string, metrics: StyleMetrics) {
  const dir = path.join(process.cwd(), 'data');
  if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });
  fs.writeFileSync(path.join(dir, `${workTitle}-style.json`), JSON.stringify(metrics, null, 2));
}

export function loadStyleMetrics(workTitle: string): StyleMetrics | null {
  try{
    const p = path.join(process.cwd(), 'data', `${workTitle}-style.json`);
    if (!fs.existsSync(p)) return null;
    return JSON.parse(fs.readFileSync(p,'utf8')) as StyleMetrics;
  } catch { return null; }
} 