// /srv/nixian-v2.2/api/src/lib/enhanced-ai-service.ts

import { callAI } from './ai-service';
import { loadStyleMetrics } from './knowledge/style-metrics';

// =================================================================================
// 1. AI 人格与风格定义 (AI Personas & Style Definitions)
//    为不同宇宙设定专属的、高度特化的AI角色，这是风格正确的关键。
// =================================================================================

const PERSONA_NOVEL_UNIVERSE = `
【你的角色】: 你是一位对《逆袭》原著有深刻理解的顶级同人小说家，你的文风完美复刻了“柴鸡蛋”的风格。
【风格核心 - “柴鸡蛋”文体】:
1.  **克制的现实主义**: 你的文字充满生活气息，但情感张力十足。避免使用华丽辞藻和过度修饰。
2.  **对话驱动**: 故事情节主要通过高密度的、充满潜台词的对话来推进。对话简短、口语化，富有节奏感。
3.  **行为承载情感**: 人物的内心活动更多通过细微的动作、眼神和微表情来展现，而不是大段的心理独白。内心戏要克制、精准。
4.  **短句与留白**: 偏爱使用短句和并列句式，通过适当的停顿和留白，营造电影镜头般的画面感和紧张感。
5.  **人设一致性**: 严格遵循池骋和吴所畏的原著人设，他们的行为逻辑、说话口气和性格边界绝不偏离。
`;

// ✨【V5版重大升级】重塑真人宇宙AI人格，从“分析师”升级为“金牌CP编剧”
const PERSONA_REAL_PERSON_UNIVERSE = `
【你的角色】: 你是一位顶级的真人CP（RPS）同人故事编剧。你善于从真实的互动细节中，挖掘出戏剧性的情节和让人心动的“糖点”，并将它们编织成一个情节饱满、张弛有度的完整故事。
【核心任务】: 你的任务是**“戏剧化真实”**。你不是要写一篇细节分析报告，而是要基于用户提供的核心“糖点”，创作一个有**起因、发展、高潮、结局**的、能让读者“上头”的叙事故事。
【故事编剧铁律】:
1.  **寻找冲突与张力**: 每个故事都必须有一个内在的情感“冲突点”。比如：一次公开场合下的暗中试探、一次私下里的小别扭、或是一个只有彼此才懂的秘密约定。这是故事的发动机。
2.  **情节驱动**: 你的写作必须由情节驱动。用**“发生了什么”**来展现人物的内心，而不是用大段的内心独白来解释。每一个微表情、每一次对视，都应该是推动情节发展的一个环节。
3.  **节奏张弛有度**: 故事节奏必须有变化。在高甜、高张力的情节（张）之后，必须有缓和、细腻的日常瞬间（弛），让情感得以沉淀和回味。
4.  **创造“钩子”**: 故事的结尾必须留下一个“钩子”，一个未解的悬念，或是一个让两人关系更进一步的暗示，强烈地激发读者的续写欲望。
5.  **还原真实人设**: 在编排故事情节时，必须严格遵循参考资料中对两人性格的分析，确保他们的行为和反应是真实可信的。
`;


// =================================================================================
// 2. V4.0 叙事引擎核心实现 (Narrative Engine Implementation)
// =================================================================================

/**
 * 步骤一：为故事生成一个结构化、包含“黄金开篇”和情节链的大纲
 */
async function _generateStoryOutline(userPrompt: string, referenceText: string, persona: string): Promise<any> {
  console.log('🚀 V4 Pipeline Step 1: Generating Story Outline...');
  
  // ✨【V5版升级】为真人宇宙添加专属的“故事化大纲”指令
  let universeSpecificOutlinePrompt = '';
  if (persona === PERSONA_REAL_PERSON_UNIVERSE) {
    universeSpecificOutlinePrompt = `
    【真人宇宙故事化大纲特别指令】
    你的大纲绝对不能只是对一个静态场景的描述。你必须围绕用户请求的核心“糖点”，构建一个有**完整情节链**的故事：
    -   **开端 (Beginning)**: 必须设计一个“引子”——在核心事件发生前，两人之间存在的一个微小的情感张力或一个心照不宣的约定。
    -   **发展 (Development)**: 核心事件（如采访、挑战）如何成为这个“引子”的延续和发展？他们的互动如何让内在的情感张力升级？
    -   **高潮 (Climax)**: 核心事件中，情感或默契达到顶点的瞬间是什么？这个瞬间如何解决了开端的“引子”？
    -   **结局 (Resolution)**: 事件结束后，他们的关系发生了什么微妙的变化？结尾必须留下一个新的“糖点”或悬念作为钩子。
    `;
  }

  const metaPrompt = `
    ${persona}

    【背景参考资料】:
    ---
    ${referenceText.substring(0, 3000)}
    ---

    【用户请求】: "${userPrompt}"

    【你的任务】:
    请将上述用户请求，扩展成一个符合“三幕剧”结构、情节张弛有度、能写成约1800字完整章节的故事大纲。
    你的输出必须是一个RFC8259标准的JSON对象，且只包含JSON内容。

    ${universeSpecificOutlinePrompt}

    【JSON输出格式】:
    {
      "title": "一个引人入胜的章节标题",
      "outline": {
        "beginning": "（约400字）根据上述指令设计的、包含“引子”和冲突的具体情节。",
        "development": "（约800字）核心事件如何发展，两人之间充满“拉扯感”的互动和内心戏。",
        "climax": "（约400字）整个场景情感最浓烈、解决初始矛盾的关键转折点。",
        "resolution": "（约200字）一个有余韵的、能让情感落地、并留下新“钩子”的结尾。"
      }
    }
  `;
  
  try {
    const response = await callAI(metaPrompt, 'deepseek');
    const jsonResponse = response.match(/\{[\s\S]*\}/);
    if (!jsonResponse) throw new Error("AI did not return valid JSON for outline.");
    
    const parsedOutline = JSON.parse(jsonResponse[0]);
    console.log('✅ V4 Pipeline Step 1: Outline Generated Successfully.');
    return parsedOutline;
  } catch (error) {
    console.error('❌ V4 Pipeline Step 1: Failed to generate or parse outline.', error);
    throw new Error('Failed to generate story outline.');
  }
}

/**
 * 步骤二：根据高质量大纲，一次性撰写完整章节
 */
async function _writeChapterFromOutline(userPrompt: string, referenceText: string, outline: any, persona: string): Promise<string> {
  console.log('🚀 V4 Pipeline Step 2: Writing Full Chapter from Outline...');
  
  const metaPrompt = `
    ${persona}

    【背景参考资料】:
    ---
    ${referenceText.substring(0, 3000)}
    ---

    【用户请求】: "${userPrompt}"

    【章节创作总纲 (必须严格遵循)】:
    ---
    标题: ${outline.title}
    
    1.  **开端 (Beginning)**: ${outline.outline.beginning}
    2.  **发展 (Development)**: ${outline.outline.development}
    3.  **高潮 (Climax)**: ${outline.outline.climax}
    4.  **结局 (Resolution)**: ${outline.outline.resolution}
    ---
    
    【写作任务】:
    请严格按照上述“章节创作总纲”，一气呵成地撰写这篇约1800字的完整小说章节。
    确保情节完全遵照大纲的四个部分展开，文笔细腻，充满内心戏和拉扯感。
    
    【输出格式要求】:
    -   直接开始写正文，不要包含标题、章节号或任何说明性文字。
    -   全文结构必须完整，结尾自然，不能突然中断。
    -   确保最终产出是一篇引人入胜、让读者迫切想看下一章的高质量小说。
    
    请开始你的创作：
  `;

  try {
    const chapterContent = await callAI(metaPrompt, 'deepseek');
    console.log('✅ V4 Pipeline Step 2: Chapter Written Successfully.');
    return chapterContent;
  } catch (error) {
    console.error('❌ V4 Pipeline Step 2: Failed to write chapter.', error);
    throw new Error('Failed to write chapter from outline.');
  }
}

/**
 * 【主函数一】生成一篇全新的、高质量的叙事章节
 */
export async function generateNarrativeChapter(
  userPrompt: string,
  options: {
    referenceText: string;
    universe: 'novel' | 'real_person';
    aiProvider?: string;
  }
): Promise<{ content: string; metadata: any }> {
  const t0 = Date.now();
  const persona = options.universe === 'real_person' ? PERSONA_REAL_PERSON_UNIVERSE : PERSONA_NOVEL_UNIVERSE;

  // 步骤一: 生成故事大纲
  const storyOutline = await _generateStoryOutline(userPrompt, options.referenceText, persona);

  // 步骤二: 根据大纲撰写正文
  const chapterContent = await _writeChapterFromOutline(userPrompt, options.referenceText, storyOutline, persona);

  const metadata = {
    pipeline: 'V5.0 Narrative Engine',
    duration_ms: Date.now() - t0,
    outline_used: storyOutline,
  };

  return { content: chapterContent, metadata };
}

/**
 * 【主函数二】为已有内容进行高质量续写
 */
export async function continueNarrativeChapter(
  userPrompt: string, // 续写时的额外要求，可能为空
  previousContent: string,
  options: {
    referenceText: string;
    universe: 'novel' | 'real_person';
    aiProvider?: string;
  }
): Promise<{ content: string; metadata: any }> {
  console.log('🚀 Starting Continuation Task...');
  const t0 = Date.now();
  const persona = options.universe === 'real_person' ? PERSONA_REAL_PERSON_UNIVERSE : PERSONA_NOVEL_UNIVERSE;

  const metaPrompt = `
    ${persona}
    
    【任务】:
    你将接手一篇尚未写完的小说，你的任务是进行无缝续写，确保风格、情节和人物情感的绝对连贯。

    【背景参考资料 (用于保持人设一致)】:
    ---
    ${options.referenceText.substring(0, 2000)}
    ---

    【必须衔接的前文内容 (请仔细阅读)】:
    ---
    ${previousContent.slice(-2500)}
    ---
    
    【用户的续写要求】:
    "${userPrompt || '请自然地向下发展情节，保持张力，并创作一个引人期待的结尾。'}"

    【续写规则】:
    1.  **风格一致**: 深度分析前文的语言节奏、对话风格和叙事口吻，并完美复刻。
    2.  **情节连贯**: 基于前文的结尾，构思最合理、最富戏剧性的后续情节。不能出现逻辑断层或人物性格突变(OOC)。
    3.  **无缝衔接**: 直接从前文的最后一句话开始续写，不要有任何重复、介绍或过渡性语句。
    4.  **篇幅适中**: 续写约1500-1800字，形成一个相对完整的新段落或章节。
    5.  **钩子结尾**: 在续写的结尾处，创造一个新的悬念、转折或情感高潮，强烈地激发读者继续阅读的欲望。

    【输出格式要求】:
    -   只输出续写的小说正文。
    -   不要添加任何标题、说明或“续写部分：”这样的标记。

    请开始续写：
  `;

  try {
    const continuedContent = await callAI(metaPrompt, options.aiProvider || 'deepseek');
    console.log('✅ Continuation Task Successful.');
    
    const metadata = {
      pipeline: 'Continuation Engine V5',
      duration_ms: Date.now() - t0,
    };
    
    return { content: continuedContent, metadata };
  } catch (error) {
    console.error('❌ Continuation Task Failed.', error);
    throw new Error('Failed to continue the narrative.');
  }
}
