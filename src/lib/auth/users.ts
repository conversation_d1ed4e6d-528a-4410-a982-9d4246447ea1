import { readJson, writeJson } from '@/lib/persistence/jsondb';
import crypto from 'crypto';

const FILE = 'users.json';

export type SubscriptionPlan = 'writer'|'patron';

export type SubscriptionInfo = {
  plan: SubscriptionPlan;
  periodMonths: number;
  startedAt: string;      // ISO
  expiresAt: string;      // ISO
  walletCredits: number;  // remaining in subscription bucket
  perMonthBonus: number;  // bonus to grant each month
  bonusMonthsRemaining: number; // months left to grant
  bonusNextAt: string;    // ISO, next grant time (first day of next month 00:00)
};

export interface UserRecord {
  id: string;            // 真实ID: u_phone or e_email (后端使用)
  displayId?: string;    // 前端显示ID: 随机数字 (保护隐私)
  contactType: 'phone'|'email';
  contact: string;       // phone number or email (管理员可见)
  name: string;
  passwordHash?: string; // sha256 hex
  credits: number;       // 灵感点数 (10灵感 = 1章节)
  createdAt: string;
  lastLoginAt?: string;  // ISO of last successful login
  subscription?: SubscriptionInfo; // 可选订阅信息
  inviteCode?: string;   // 随机邀请码（用于分享注册）
  // 头衔和活动统计系统
  fragmentBalance?: number; // 灵感碎片余额
  fragmentCount?: number; // 累计获得碎片总数 (用于碎片获取统计)
  unlockedTitles?: string[]; // 已解锁的头衔ID数组
  equippedTitles?: string[]; // 当前佩戴的头衔ID数组(最多3个)
  inviteCount?: number; // 邀请人数统计
  generateCount?: number; // 生成章节统计
  checkinStreak?: number; // 连续签到天数
  lastCheckinDate?: string; // 上次签到日期
  totalRecharge?: number; // 累计充值金额
  donationCount?: number; // 投喂次数
  // 星轨召唤券系统
  gachaTickets?: number; // 星轨召唤券数量
  gachaHistory?: Array<{ // 抽卡历史记录
    pool: string; // 卡池类型
    rarity: string; // 稀有度
    titleId: string; // 获得的头衔ID
    timestamp: string; // 抽卡时间
  }>;
}

function sha256(input: string): string {
  return crypto.createHash('sha256').update(input).digest('hex');
}

function generateInviteCode(): string {
  // 生成 20 字符的 base36 邀请码
  return (Date.now().toString(36) + crypto.randomBytes(8).toString('hex')).slice(0, 20);
}

function generateDisplayId(): string {
  // 生成类似 me86osv658f449d6bc60 的随机显示ID
  const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 20; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

export async function loadUsers(): Promise<UserRecord[]> {
  return readJson<UserRecord[]>(FILE, []);
}

export async function saveUsers(users: UserRecord[]): Promise<void> {
  await writeJson(FILE, users);
}

// 格式化用户数据用于前端显示
export function formatUserForFrontend(user: UserRecord): any {
  return {
    ...user,
    name: `${user.name}#${user.displayId || user.id.split('_')[1]?.slice(-8) || 'unknown'}`, // 显示为 用户名#随机ID
    id: user.displayId || user.id // 前端使用displayId作为主要ID
  };
}

// 头衔管理函数
export async function grantTitle(userId: string, titleId: string): Promise<boolean> {
  const users = await readJson<UserRecord[]>(FILE, []);
  const userIndex = users.findIndex(u => u.id === userId);
  if (userIndex === -1) return false;

  const user = users[userIndex];
  if (!user.unlockedTitles) user.unlockedTitles = [];
  
  // 检查是否已经拥有该头衔
  if (user.unlockedTitles.includes(titleId)) return false;
  
  user.unlockedTitles.push(titleId);
  users[userIndex] = user;
  await writeJson(FILE, users);
  return true;
}

export async function equipTitle(userId: string, titleIds: string[]): Promise<boolean> {
  const users = await readJson<UserRecord[]>(FILE, []);
  const userIndex = users.findIndex(u => u.id === userId);
  if (userIndex === -1) return false;

  const user = users[userIndex];
  if (!user.unlockedTitles) user.unlockedTitles = [];
  
  // 检查用户是否拥有这些头衔，并限制最多3个
  const validTitles = titleIds.filter(id => user.unlockedTitles!.includes(id)).slice(0, 3);
  user.equippedTitles = validTitles;
  
  users[userIndex] = user;
  await writeJson(FILE, users);
  return true;
}

// Function removed to avoid import errors - will implement title granting logic later

export async function incrementUserStat(userId: string, stat: 'inviteCount' | 'generateCount' | 'checkinStreak'): Promise<void> {
  const users = await readJson<UserRecord[]>(FILE, []);
  const userIndex = users.findIndex(u => u.id === userId);
  if (userIndex === -1) return;

  const user = users[userIndex];
  if (!user[stat]) user[stat] = 0;
  user[stat] = (user[stat] || 0) + 1;
  
  users[userIndex] = user;
  await writeJson(FILE, users);
}

export async function findUserByContact(contactType: 'phone'|'email', contact: string): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  return users.find(u => u.contactType === contactType && u.contact === contact);
}

export async function getUserById(userId: string): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  return users.find(u => u.id === userId);
}

export async function getUserByInviteCode(code: string): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  return users.find(u => (u.inviteCode || '') === code);
}

export async function ensureInviteCode(userId: string): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  if (!users[idx].inviteCode) {
    users[idx].inviteCode = generateInviteCode();
    await saveUsers(users);
  }
  return users[idx];
}

export async function createUser(contactType: 'phone'|'email', contact: string, name: string, password?: string): Promise<UserRecord> {
  const users = await loadUsers();
  const exists = users.find(u => u.contactType === contactType && u.contact === contact);
  if (exists) return exists;
  const isPhoneContact = contactType === 'phone';
  const newUser: UserRecord = {
    id: isPhoneContact ? `u_${contact}` : `e_${contact}`,
    displayId: generateDisplayId(),
    contactType: isPhoneContact ? 'phone' : 'email',
    contact,
    name,
    passwordHash: password ? sha256(password) : '',
    credits: 30, // 新用户注册送30灵感 (相当于3章节)
    createdAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
    inviteCode: generateInviteCode(),
    // 新增字段
    fragmentBalance: 0,
    fragmentCount: 0,
    unlockedTitles: [],
    equippedTitles: [],
    inviteCount: 0,
    generateCount: 0,
    checkinStreak: 0,
    totalRecharge: 0,
    donationCount: 0,
    // 星轨召唤券系统
    gachaTickets: 0,
    gachaHistory: []
  };
  users.push(newUser);
  await saveUsers(users);
  return newUser;
}

export async function setPassword(contactType: 'phone'|'email', contact: string, password: string): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.contactType === contactType && u.contact === contact);
  if (idx === -1) return undefined;
  users[idx].passwordHash = sha256(password);
  await saveUsers(users);
  return users[idx];
}

export async function verifyPassword(contactType: 'phone'|'email', contact: string, password: string): Promise<UserRecord | undefined> {
  const u = await findUserByContact(contactType, contact);
  if (!u || !u.passwordHash) return undefined;
  return u.passwordHash === sha256(password) ? u : undefined;
}

export async function addCredits(userId: string, delta: number): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  users[idx].credits = Math.max(0, (users[idx].credits || 0) + delta);
  await saveUsers(users);
  return users[idx];
}

export async function setLastLogin(userId: string): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  users[idx].lastLoginAt = new Date().toISOString();
  await saveUsers(users);
  return users[idx];
}

// ===== 订阅工具 =====
function addMonths(date: Date, months: number): Date {
  const d = new Date(date);
  const day = d.getDate();
  d.setMonth(d.getMonth() + months);
  if (d.getDate() < day) d.setDate(0);
  return d;
}

function firstDayOfNextMonth(date: Date): Date {
  const d = new Date(date);
  d.setDate(1);
  d.setMonth(d.getMonth() + 1);
  d.setHours(0,0,0,0);
  return d;
}

export function planConfig(plan: SubscriptionPlan): { months: number; base: number; perMonthBonus: number; gachaTickets: number } {
  if (plan === 'writer') return { months: 1, base: 3000, perMonthBonus: 0, gachaTickets: 1 }; // 产粮·执笔者: 每月3000灵感，送1张星轨召唤券
  if (plan === 'patron') return { months: 1, base: 10000, perMonthBonus: 0, gachaTickets: 3 }; // 产粮·太太: 每月10000灵感，送3张星轨召唤券
  return { months: 1, base: 0, perMonthBonus: 0, gachaTickets: 0 };
}

export async function grantSubscription(userId: string, plan: SubscriptionPlan): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  const { months, base, perMonthBonus, gachaTickets } = planConfig(plan);
  const now = new Date();
  const expires = addMonths(now, months);
  const nextBonus = firstDayOfNextMonth(now);
  users[idx].subscription = {
    plan,
    periodMonths: months,
    startedAt: now.toISOString(),
    expiresAt: expires.toISOString(),
    walletCredits: base,
    perMonthBonus,
    bonusMonthsRemaining: plan === 'writer' ? 1 : (plan === 'patron' ? 1 : (plan === 'quarter' ? 3 : 12)),
    bonusNextAt: nextBonus.toISOString(),
  };
  users[idx].credits = Math.max(0, (users[idx].credits || 0) + base);
  
  // 为产粮·太太用户添加"逆命红线"头衔
  if (plan === 'patron') {
    if (!users[idx].unlockedTitles) users[idx].unlockedTitles = [];
    // 添加"逆命红线"头衔 (需要在titles.ts中定义)
    const fateRedThreadTitleId = 'patron_special_fate_red_thread';
    if (!users[idx].unlockedTitles.includes(fateRedThreadTitleId)) {
      users[idx].unlockedTitles.push(fateRedThreadTitleId);
    }
  }
  
  // 发放星轨召唤券
  users[idx].gachaTickets = Math.max(0, (users[idx].gachaTickets || 0) + gachaTickets);
  
  await saveUsers(users);
  return users[idx];
}

export async function applySubscriptionTick(userId: string, nowDate?: Date): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  const now = nowDate || new Date();
  const u = users[idx];
  if (!u.subscription) return u;
  const expiresAt = new Date(u.subscription.expiresAt);
  if (now > expiresAt) {
    const remaining = Math.max(0, u.subscription.walletCredits || 0);
    if (remaining > 0) {
      u.credits = Math.max(0, (u.credits || 0) - remaining);
    }
    u.subscription = undefined;
    await saveUsers(users);
    return u;
  }
  let changed = false;
  while (u.subscription.bonusMonthsRemaining > 0) {
    const nextAt = new Date(u.subscription.bonusNextAt);
    if (now >= nextAt) {
      u.credits = Math.max(0, (u.credits || 0) + u.subscription.perMonthBonus);
      u.subscription.bonusMonthsRemaining -= 1;
      u.subscription.bonusNextAt = addMonths(nextAt, 1).toISOString();
      changed = true;
    } else {
      break;
    }
  }
  if (changed) await saveUsers(users);
  return u;
}

export async function spendCredits(userId: string, amount: number): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  const u = users[idx];
  let remaining = amount;
  if (u.subscription && u.subscription.walletCredits > 0) {
    const used = Math.min(u.subscription.walletCredits, remaining);
    u.subscription.walletCredits -= used;
    u.credits = Math.max(0, (u.credits || 0) - used);
    remaining -= used;
  }
  if (remaining > 0) {
    u.credits = Math.max(0, (u.credits || 0) - remaining);
    remaining = 0;
  }
  await saveUsers(users);
  return u;
}

export async function adminAdjustCredits(userId: string, delta: number, allowNegative: boolean = true): Promise<UserRecord | undefined> {
  const users = await loadUsers();
  const idx = users.findIndex(u => u.id === userId);
  if (idx === -1) return undefined;
  const current = users[idx].credits || 0;
  const next = allowNegative ? current + delta : Math.max(0, current + delta);
  users[idx].credits = next;
  await saveUsers(users);
  return users[idx];
}