export interface Title {
  id: string;
  name: string;
  category: '成就之路' | '传教士之路' | '守护者之路' | '随机掉落';
  color: string;
  rarity: 'R' | 'SR' | 'SSR' | 'GUARANTEED'; // GUARANTEED for purchase-based titles
  unlockCondition: string;
  description: string;
  requirementText: string;
}

// 守护者之路头衔 - 产粮·执笔者用户 (10个)
export const WEEK_CARD_TITLES: Title[] = [
  { id: 'writer_1', name: '⭐月下守护者', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '初入守护者行列的纪念', requirementText: '首次订阅解锁' },
  { id: 'writer_2', name: '✨灵感捕手', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '善于捕捉创作灵感', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_3', name: '🖋️文字工匠', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '精雕细琢每个文字', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_4', name: '📖故事收集者', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '热爱收集精彩故事', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_5', name: '🎯创作目标达成者', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '坚持完成创作目标', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_6', name: '🌟剧情大师', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '擅长编织精彩剧情', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_7', name: '🎭角色塑造师', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '精心塑造每个角色', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_8', name: '💫创意火花', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '点燃无限创意火花', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_9', name: '📚章节守护者', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '守护每章节的完整性', requirementText: '购买产粮·执笔者解锁' },
  { id: 'writer_10', name: '🏆执笔者荣誉会员', category: '守护者之路', color: '#DDD6FE', rarity: 'GUARANTEED', unlockCondition: 'writer_card', description: '执笔者的荣誉象征', requirementText: '购买产粮·执笔者解锁' },
];

// 守护者之路头衔 - 产粮·太太用户 (10个)
export const MONTH_CARD_TITLES: Title[] = [
  { id: 'patron_1', name: '👑产粮太太', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '最尊贵的产粮支持者', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_2', name: '💎荣耀守护者', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '获得最高荣耀的守护者', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_3', name: '🌟无限创作家', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '享受无限创作的自由', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_4', name: '🎖️终身共建者', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '与逆线永恒相伴的建设者', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_5', name: '🏅官方认证"厨子"', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '官方认证的CP狂热粉', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_6', name: '🎪CP超话大咖', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '超话中的意见领袖', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_7', name: '🎬制片人', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '参与作品的创作决策', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_8', name: '📚故事收藏家', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '收藏无数精彩故事', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_9', name: '💫灵感源泉', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '成为创作的灵感源泉', requirementText: '购买产粮·太太解锁' },
  { id: 'patron_10', name: '🏆产粮女王/国王', category: '守护者之路', color: '#A855F7', rarity: 'GUARANTEED', unlockCondition: 'patron_card', description: '产粮界的至高荣誉', requirementText: '购买产粮·太太解锁' },
];

// 传教士之路头衔 (5个)
export const INVITATION_TITLES: Title[] = [
  { id: 'invite_30', name: '🔥"小醋包"饲养员', category: '传教士之路', color: '#FF6B35', rarity: 'GUARANTEED', unlockCondition: 'invite_30', description: '源自池骋的爱蛇，成功邀请30人', requirementText: '邀请30人解锁' },
  { id: 'invite_60', name: '🚀"逆袭"首席军师', category: '传教士之路', color: '#06B6D4', rarity: 'GUARANTEED', unlockCondition: 'invite_60', description: '向姜小帅致敬，成功邀请60人', requirementText: '邀请60人解锁' },
  { id: 'invite_100', name: '👑"雷朋"显微镜学家', category: '传教士之路', color: '#8B5CF6', rarity: 'GUARANTEED', unlockCondition: 'invite_100', description: '圈内人才懂的终极荣誉，成功邀请100人', requirementText: '邀请100人解锁' },
  { id: 'invite_150', name: '🪐"逆爱"金牌制片人', category: '传教士之路', color: '#1E40AF', rarity: 'GUARANTEED', unlockCondition: 'invite_150', description: '你就是让故事延续的人，成功邀请150人', requirementText: '邀请150人解锁' },
  { id: 'invite_200', name: '✨"驰畏"宿命连接者', category: '传教士之路', color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', rarity: 'GUARANTEED', unlockCondition: 'invite_200', description: '至高荣誉，你连接了更多人的爱，成功邀请200人', requirementText: '邀请200人解锁' },
];

// R池头衔 - 普通卡池 (25个)
export const R_RANDOM_TITLES: Title[] = [
  { id: 'r_1', name: 'AI首席调教师', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '每个创作者的第一个身份', requirementText: 'R池随机获得' },
  { id: 'r_2', name: '"滚"字幸存者', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '吴所畏的经典台词，很有趣', requirementText: 'R池随机获得' },
  { id: 'r_3', name: '京味儿品鉴师', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '对原著京腔风格的致敬', requirementText: 'R池随机获得' },
  { id: 'r_4', name: '三轮车老司机', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '吴所畏的经典座驾', requirementText: 'R池随机获得' },
  { id: 'r_5', name: '诊所VIP病患', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '副CP展丞的爱情发生地', requirementText: 'R池随机获得' },
  { id: 'r_6', name: '"小辣椒"守护者', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '展轩对刘轩丞的爱称', requirementText: 'R池随机获得' },
  { id: 'r_7', name: 'BUG头号捕手', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '给热心用户的奖励', requirementText: 'R池随机获得' },
  { id: 'r_8', name: '服务器续命官', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '给付费用户的趣味爱称', requirementText: 'R池随机获得' },
  { id: 'r_9', name: '弹幕护卫队', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '维护社区氛围的荣誉', requirementText: 'R池随机获得' },
  { id: 'r_10', name: '官方认证"厨子"', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '同人圈对产粮大手的爱称', requirementText: 'R池随机获得' },
  { id: 'r_11', name: '磕糖练习生', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '每个CP粉的必经之路', requirementText: 'R池随机获得' },
  { id: 'r_12', name: '剧组场记员', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '形容考据派粉丝', requirementText: 'R池随机获得' },
  { id: 'r_13', name: '"霸总"行为分析师', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '对展轩人设的趣味分析', requirementText: 'R池随机获得' },
  { id: 'r_14', name: '"老妈子"式男友观察员', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '同样是展丞CP的梗', requirementText: 'R池随机获得' },
  { id: 'r_15', name: '"导演挠头"见证官', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '看过所有花絮的证明', requirementText: 'R池随机获得' },
  { id: 'r_16', name: '"为爱发电"预备役', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '对所有粉丝的爱称', requirementText: 'R池随机获得' },
  { id: 'r_17', name: '"意难平"终结者', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '用AI创作，抚平所有遗憾', requirementText: 'R池随机获得' },
  { id: 'r_18', name: '逆线爆肝王', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '给最投入的创作者', requirementText: 'R池随机获得' },
  { id: 'r_19', name: '头号产粮官', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '对创作的最高认可', requirementText: 'R池随机获得' },
  { id: 'r_20', name: '交错时空第一批居民', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '给早期核心用户的纪念', requirementText: 'R池随机获得' },
  { id: 'r_21', name: '"我恨你"破译员', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '反话都懂，才是真爱', requirementText: 'R池随机获得' },
  { id: 'r_22', name: '"吃醋"一级品鉴师', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '善于发现各种酸味儿', requirementText: 'R池随机获得' },
  { id: 'r_23', name: '"KTV情歌"点唱机', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '剧中经典KTV场景', requirementText: 'R池随机获得' },
  { id: 'r_24', name: '"探监"常客', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '原著经典桥段', requirementText: 'R池随机获得' },
  { id: 'r_25', name: '"砸场子"第一排观众', category: '随机掉落', color: '#10B981', rarity: 'R', unlockCondition: 'random_draw', description: '原著经典桥段', requirementText: 'R池随机获得' },
];

// SR池头衔 - 稀有卡池 (25个)
export const SR_RANDOM_TITLES: Title[] = [
  { id: 'sr_1', name: '黑伞下的守护者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '池骋雨夜寻吴所畏，最经典的守护场面', requirementText: 'SR池随机获得' },
  { id: 'sr_2', name: '"大宝"天天见', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '最朴实无华，却最深入人心的日常糖', requirementText: 'SR池随机获得' },
  { id: 'sr_3', name: '黄蟒幸存者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '池骋的宠物\'小醋包\'，吴所畏的噩梦', requirementText: 'SR池随机获得' },
  { id: 'sr_4', name: '浴室失控记录官', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '两人第一次真正意义上的失控与突破', requirementText: 'SR池随机获得' },
  { id: 'sr_5', name: '解领带一级暧昧师', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '原著中充满张力的暧昧动作', requirementText: 'SR池随机获得' },
  { id: 'sr_6', name: '糖人儿摊主', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '吴所畏送给池骋的定情信物', requirementText: 'SR池随机获得' },
  { id: 'sr_7', name: '"十块钱"零花钱掌管者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '池骋对吴所畏的霸道式宠溺', requirementText: 'SR池随机获得' },
  { id: 'sr_8', name: '篮球场"臀部"评论员', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '名场面，懂的都懂', requirementText: 'SR池随机获得' },
  { id: 'sr_9', name: '"未完成的惩罚"监督员', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '同上，极致的拉扯感', requirementText: 'SR池随机获得' },
  { id: 'sr_10', name: '"别怕，有我"聆听者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '池骋最动人的承诺', requirementText: 'SR池随机获得' },
  { id: 'sr_11', name: '宣示主权第一目击者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '池骋的强烈占有欲', requirementText: 'SR池随机获得' },
  { id: 'sr_12', name: '"服软"见证官', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '强者只为一人低头', requirementText: 'SR池随机获得' },
  { id: 'sr_13', name: '"不舍"情感代言人', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '原著核心情感之一', requirementText: 'SR池随机获得' },
  { id: 'sr_14', name: '《逆袭》持股人', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '对原著热爱到极致的证明', requirementText: 'SR池随机获得' },
  { id: 'sr_15', name: '时间线修正者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '用AI改变那些意难平的过去', requirementText: 'SR池随机获得' },
  { id: 'sr_16', name: '羁绊缔造者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '你创造了他们更深的羁绊', requirementText: 'SR池随机获得' },
  { id: 'sr_17', name: '莫比乌斯环旅客', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '象征无限循环的爱，本站核心图腾', requirementText: 'SR池随机获得' },
  { id: 'sr_18', name: '灵魂速记员', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '能写出他们灵魂深处对话的你', requirementText: 'SR池随机获得' },
  { id: 'sr_19', name: '驰畏爱情保安', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '守护这份原著爱情的忠诚卫士', requirementText: 'SR池随机获得' },
  { id: 'sr_20', name: '"逆"行者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '逆流而上，也要爱', requirementText: 'SR池随机获得' },
  { id: 'sr_21', name: '"袭"警专业户', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '对池骋警察身份的调侃', requirementText: 'SR池随机获得' },
  { id: 'sr_22', name: '"我的人"宣告者', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '池骋的经典台词', requirementText: 'SR池随机获得' },
  { id: 'sr_23', name: '"是我入戏太深共情者"', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '', requirementText: 'SR池随机获得' },
  { id: 'sr_24', name: 'CP超话大咖', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '超话中的意见领袖', requirementText: 'SR池随机获得' },
  { id: 'sr_25', name: '剧情大师', category: '随机掉落', color: '#EC4899', rarity: 'SR', unlockCondition: 'random_draw', description: '擅长编织精彩剧情', requirementText: 'SR池随机获得' },
];

// SSR池头衔 - 超稀有卡池 (25个)
export const SSR_RANDOM_TITLES: Title[] = [
  { id: 'ssr_1', name: '秩序之外的贪恋者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '源自雷朋分析，点明关系的本质', requirementText: 'SSR池随机获得' },
  { id: 'ssr_2', name: '心音同步者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '能听到他们心跳共鸣的你', requirementText: 'SSR池随机获得' },
  { id: 'ssr_3', name: '"画痣"一级美术师', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: 'TXN的小动作，最亲密的证明', requirementText: 'SSR池随机获得' },
  { id: 'ssr_4', name: '"半搂不搂"批评家', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '对两人微妙距离的精准吐槽', requirementText: 'SSR池随机获得' },
  { id: 'ssr_5', name: '25年夏天的记录官', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '那个不可复制的、限定的夏天', requirementText: 'SSR池随机获得' },
  { id: 'ssr_6', name: '安全距离失效者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '他们之间，没有安全距离', requirementText: 'SSR池随机获得' },
  { id: 'ssr_7', name: '"是我入戏太深"共情者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '花絮经典台词，所有粉丝的心声', requirementText: 'SSR池随机获得' },
  { id: 'ssr_8', name: '褪黑素的回响', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '梓瑜的新歌，最新的糖', requirementText: 'SSR池随机获得' },
  { id: 'ssr_9', name: '《大城小爱》跑调王', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '剧中的名曲，也是现实的梗', requirementText: 'SSR池随机获得' },
  { id: 'ssr_10', name: '"口水"事件见证人', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '花絮里的高能互动', requirementText: 'SSR池随机获得' },
  { id: 'ssr_11', name: '"神同步"挑战王', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '两人多次的默契行为', requirementText: 'SSR池随机获得' },
  { id: 'ssr_12', name: '心脏触碰者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '花絮名场面', requirementText: 'SSR池随机获得' },
  { id: 'ssr_13', name: '空心人的例外', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '经典分析，你是我的唯一例外', requirementText: 'SSR池随机获得' },
  { id: 'ssr_14', name: '恃宠而骄第一名', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '一人在闹，一人在笑的完美诠释', requirementText: 'SSR池随机获得' },
  { id: 'ssr_15', name: '"不道歉"特权拥有者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '花絮名场面', requirementText: 'SSR池随机获得' },
  { id: 'ssr_16', name: '"永恒"的反对者', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '对"没有什么是永恒"的深情回应', requirementText: 'SSR池随机获得' },
  { id: 'ssr_17', name: '"显微镜"一级磕学家', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '用8倍镜观察细节的你', requirementText: 'SSR池随机获得' },
  { id: 'ssr_18', name: '雷朋首席分析师', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '对真人CP关系最深刻的洞察者', requirementText: 'SSR池随机获得' },
  { id: 'ssr_19', name: '"逆线"荣誉站长', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '对本站有巨大贡献的终极荣誉', requirementText: 'SSR池随机获得' },
  { id: 'ssr_20', name: '池畏宇宙奠基人', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '你，和我们一起，创造了这个宇宙', requirementText: 'SSR池随机获得' },
  { id: 'ssr_21', name: '"25雷朋"认证爱过', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '', requirementText: 'SSR池随机获得' },
  { id: 'ssr_22', name: '"无安全距离"体验官', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '体验无安全距离的感觉', requirementText: 'SSR池随机获得' },
  { id: 'ssr_23', name: '创意大师', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '创意领域的专家', requirementText: 'SSR池随机获得' },
  { id: 'ssr_24', name: '故事收藏家', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '收藏无数精彩故事', requirementText: 'SSR池随机获得' },
  { id: 'ssr_25', name: '灵感源泉', category: '随机掉落', color: '#EF4444', rarity: 'SSR', unlockCondition: 'random_draw', description: '成为创作的灵感源泉', requirementText: 'SSR池随机获得' },
];

// 合并所有头衔
export const ALL_TITLES: Title[] = [
  ...WEEK_CARD_TITLES,
  ...MONTH_CARD_TITLES,
  ...INVITATION_TITLES,
  ...R_RANDOM_TITLES,
  ...SR_RANDOM_TITLES,
  ...SSR_RANDOM_TITLES,
];

// 获取头衔的函数
export function getTitleById(id: string): Title | undefined {
  return ALL_TITLES.find(title => title.id === id);
}

export function getTitlesByCategory(category: '成就之路' | '传教士之路' | '守护者之路' | '随机掉落'): Title[] {
  return ALL_TITLES.filter(title => title.category === category);
}

export function getTitlesByRarity(rarity: 'R' | 'SR' | 'SSR' | 'GUARANTEED'): Title[] {
  return ALL_TITLES.filter(title => title.rarity === rarity);
}

// 总数统计：执笔者10 + 太太10 + 邀请5 + R级25 + SR级25 + SSR级25 = 100个头衔