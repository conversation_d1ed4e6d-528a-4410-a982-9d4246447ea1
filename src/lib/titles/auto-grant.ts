import { readJson, writeJ<PERSON> } from '@/lib/persistence/jsondb';
import { WEEK_CARD_TITLES, MONTH_CARD_TITLES, INVITATION_TITLES, R_RANDOM_TITLES, SR_RANDOM_TITLES, SSR_RANDOM_TITLES, getTitleById } from './titles';
import type { SubscriptionPlan } from '@/lib/auth/users';

const USERS_FILE = 'users.json';

interface UserRecord {
  id: string;
  credits?: number;
  unlockedTitles?: string[];
  inviteCount?: number;
  generateCount?: number;
  totalRecharge?: number;
  checkinStreak?: number;
  lastCheckinDate?: string;
  subscription?: SubscriptionPlan;
  subscriptionEndDate?: string;
  donationCount?: number;
  fragmentBalance?: number;
  fragmentCount?: number;
  gachaTickets?: number; // 星轨召唤券
  gachaHistory?: Array<{ // 抽卡历史记录
    pool: string;
    rarity: string;
    titleId: string;
    timestamp: string;
  }>;
}

// 自动授予头衔的主函数
export async function autoGrantTitles(userId: string, trigger: 'purchase' | 'invite' | 'generate' | 'checkin' | 'recharge', data?: any): Promise<string[]> {
  const users = await readJson<UserRecord[]>(USERS_FILE, []);
  const userIndex = users.findIndex(u => u.id === userId);
  
  if (userIndex === -1) return [];
  
  const user = users[userIndex];
  const newTitles: string[] = [];
  
  try {
    switch (trigger) {
      case 'purchase':
        newTitles.push(...await handlePurchaseTitles(user, data));
        break;
      case 'invite':
        newTitles.push(...await handleInviteTitles(user));
        break;
      case 'generate':
        newTitles.push(...await handleGenerateTitles(user));
        break;
      case 'checkin':
        newTitles.push(...await handleCheckinTitles(user));
        break;
      case 'recharge':
        newTitles.push(...await handleRechargeTitles(user, data));
        break;
    }

    // 更新用户头衔
    if (newTitles.length > 0) {
      user.unlockedTitles = [...(user.unlockedTitles || []), ...newTitles];
      users[userIndex] = user;
      await writeJson(USERS_FILE, users);
    }
  } catch (error) {
    console.error('Error in autoGrantTitles:', error);
  }

  return newTitles;
}

// 处理购买相关头衔
async function handlePurchaseTitles(user: UserRecord, orderData: any): Promise<string[]> {
  const newTitles: string[] = [];
  
  if (!orderData?.subscriptionPlan) return newTitles;
  
  const plan = orderData.subscriptionPlan;
  
  // 根据订阅计划授予对应头衔和星轨召唤券
  switch (plan) {
    case 'writer': // 产粮·执笔者
      newTitles.push(...WEEK_CARD_TITLES.map(t => t.id));
      // 每月赠送1张星轨召唤券
      user.gachaTickets = (user.gachaTickets || 0) + 1;
      break;
    case 'patron': // 产粮·太太
      newTitles.push(...MONTH_CARD_TITLES.map(t => t.id));
      // 每月赠送3张星轨召唤券
      user.gachaTickets = (user.gachaTickets || 0) + 3;
      break;
  }
  
  return newTitles;
}

// 处理邀请相关头衔
async function handleInviteTitles(user: UserRecord): Promise<string[]> {
  const newTitles: string[] = [];
  const inviteCount = user.inviteCount || 0;
  
  // 基础奖励：邀请1人，双方各得30灵感
  // 这个逻辑在注册API中处理
  
  // 里程碑奖励
  if (inviteCount >= 30 && !user.unlockedTitles?.includes('invite_30')) {
    newTitles.push('invite_30'); // 🔥"小醋包"饲养员
    user.credits = (user.credits || 0) + 150; // 30人奖励150灵感（原15章）
  }
  
  if (inviteCount >= 60 && !user.unlockedTitles?.includes('invite_60')) {
    newTitles.push('invite_60'); // 🚀"逆袭"首席军师
    user.credits = (user.credits || 0) + 350; // 60人奖励350灵感（原35章）
  }
  
  if (inviteCount >= 100 && !user.unlockedTitles?.includes('invite_100')) {
    newTitles.push('invite_100'); // 👑"雷朋"显微镜学家
    user.credits = (user.credits || 0) + 700; // 100人奖励700灵感（原70章）
  }
  
  if (inviteCount >= 150 && !user.unlockedTitles?.includes('invite_150')) {
    newTitles.push('invite_150'); // 🪐"逆爱"金牌制片人
    user.credits = (user.credits || 0) + 1200; // 150人奖励1200灵感（原120章）
  }
  
  if (inviteCount >= 200 && !user.unlockedTitles?.includes('invite_200')) {
    newTitles.push('invite_200'); // ✨"池畏"宿命连接者
    user.credits = (user.credits || 0) + 10000; // 200人奖励10000灵感（等值一张季卡）
    // 额外奖励3张星轨召唤券
    user.gachaTickets = (user.gachaTickets || 0) + 3;
  }
  
  return newTitles;
}

// 处理生成相关头衔
async function handleGenerateTitles(user: UserRecord): Promise<string[]> {
  const newTitles: string[] = [];
  const generateCount = user.generateCount || 0;
  
  // 每生成20章可获得1次R级抽奖机会（这里转换为星轨召唤券）
  if (generateCount > 0 && generateCount % 20 === 0) {
    user.gachaTickets = (user.gachaTickets || 0) + 1;
    console.log(`User ${user.id} earned 1 gacha ticket for generating ${generateCount} chapters`);
  }
  
  return newTitles;
}

// 处理签到相关头衔
async function handleCheckinTitles(user: UserRecord): Promise<string[]> {
  const newTitles: string[] = [];
  const checkinStreak = user.checkinStreak || 0;
  
  // 连续签到7天可获得1张星轨召唤券
  if (checkinStreak >= 7 && checkinStreak % 7 === 0) {
    user.gachaTickets = (user.gachaTickets || 0) + 1;
    console.log(`User ${user.id} earned 1 gacha ticket for ${checkinStreak} consecutive check-ins`);
  }
  
  return newTitles;
}

// 处理充值相关头衔
async function handleRechargeTitles(user: UserRecord, rechargeData: any): Promise<string[]> {
  const newTitles: string[] = [];
  
  // 每充值8元灵感加油包可获得1张星轨召唤券
  if (rechargeData?.amountCny === 8) {
    user.gachaTickets = (user.gachaTickets || 0) + 1;
    console.log(`User ${user.id} earned 1 gacha ticket for recharging ¥8`);
  }
  
  return newTitles;
}

// 在订单完成时检查并授予头衔
export async function checkAndGrantTitlesOnOrderComplete(userId: string, orderData: any): Promise<string[]> {
  return await autoGrantTitles(userId, 'purchase', orderData);
}

// 增加邀请计数并检查头衔
export async function incrementInviteCount(userId: string): Promise<string[]> {
  const users = await readJson<UserRecord[]>(USERS_FILE, []);
  const userIndex = users.findIndex(u => u.id === userId);
  
  if (userIndex === -1) return [];
  
  users[userIndex].inviteCount = (users[userIndex].inviteCount || 0) + 1;
  await writeJson(USERS_FILE, users);
  
  return autoGrantTitles(userId, 'invite');
}

// 增加生成计数并检查头衔
export async function incrementGenerateCount(userId: string): Promise<string[]> {
  const users = await readJson<UserRecord[]>(USERS_FILE, []);
  const userIndex = users.findIndex(u => u.id === userId);
  
  if (userIndex === -1) return [];
  
  users[userIndex].generateCount = (users[userIndex].generateCount || 0) + 1;
  await writeJson(USERS_FILE, users);
  
  return autoGrantTitles(userId, 'generate');
}