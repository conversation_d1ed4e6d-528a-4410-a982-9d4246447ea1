import { IngestIndex, ChunkEntry, toBigrams, loadIngestIndex } from '../ingest/text-ingest';
import { expandAliasesInQuery, normalizeChinese, extractCoreNamesFromQuery, expandIntentKeywords } from './aliases';

export interface EvidenceItem {
  chunkId: string;
  text: string;
  chapIndex: number;
  start: number;
  end: number;
  score: number;
  // 新增：章节标注
  chapterNo?: number;
  chapterTitle?: string;
  // 邻接窗口
  prev?: { chapterNo?: number; chapterTitle?: string; text: string };
  next?: { chapterNo?: number; chapterTitle?: string; text: string };
}

export interface EvidencePack {
  workTitle: string;
  query: string;
  items: EvidenceItem[];
}

function tfidfVector(tokens: string[], idf: Record<string, number>): Record<string, number> {
  const tf: Record<string, number> = {};
  tokens.forEach(t => { tf[t] = (tf[t] || 0) + 1; });
  const vec: Record<string, number> = {};
  Object.entries(tf).forEach(([t, f]) => { vec[t] = f * (idf[t] || 0); });
  return vec;
}

function cosineSim(a: Record<string, number>, b: Record<string, number>): number {
  let dot = 0, na = 0, nb = 0;
  for (const [k, v] of Object.entries(a)) {
    if (b[k]) dot += v * b[k];
    na += v * v;
  }
  for (const v of Object.values(b)) nb += v * v;
  if (!na || !nb) return 0;
  return dot / (Math.sqrt(na) * Math.sqrt(nb));
}

export function searchEvidence(workTitle: string, userQuery: string, topK: number = 8): EvidencePack {
  const index = loadIngestIndex(workTitle);
  if (!index) {
    return { workTitle, query: userQuery, items: [] };
  }
  const qTokens = toBigrams(userQuery);
  const qVec = tfidfVector(qTokens, index.idf);

  // 预先构造规范化文本，便于鲁棒的“包含”判断
  const normChunks = index.chunks.map((ch: ChunkEntry) => ({
    ...ch,
    normText: normalizeChinese(ch.text),
  }));

  const scoredRaw = normChunks.map((ch) => ({
    chunkId: ch.id,
    chapIndex: ch.chapIndex,
    start: ch.start,
    end: ch.end,
    rawText: ch.text,
    normText: ch.normText,
    score: cosineSim(qVec, tfidfVector(ch.tokens, index.idf)),
    chapterNo: ch.chapterNo,
    chapterTitle: ch.chapterTitle
  }));

  // 别名/规范化后的精确短语列表（如匹配到“大黄龙”或其变体）
  const exactKeywords = expandAliasesInQuery(userQuery);
  // 用户提示中的场景/道具/地点等意图关键词
  const intentKeywords = expandIntentKeywords(userQuery);
  // 角色名硬匹配提升
  const roleHints = extractCoreNamesFromQuery(userQuery);

  scoredRaw.forEach(s => {
    roleHints.forEach(nm => { if (nm && s.rawText.includes(nm)) s.score += 0.4; });
    exactKeywords.forEach(kwNorm => { if (kwNorm && s.normText.includes(kwNorm)) s.score += 6; });
    intentKeywords.forEach(kwNorm => { if (kwNorm && s.normText.includes(kwNorm)) s.score += 3; });
  });

  // 过滤策略：优先级从高到低逐级回退
  // 1) 同时包含“精确短语(别名)”与“意图关键词”的块
  let pool = scoredRaw.filter(s => exactKeywords.length && intentKeywords.length && exactKeywords.some(kw => s.normText.includes(kw)) && intentKeywords.some(kw => s.normText.includes(kw)));
  // 2) 若为空，退化为包含精确短语的块
  if (pool.length === 0 && exactKeywords.length) {
    pool = scoredRaw.filter(s => exactKeywords.some(kw => s.normText.includes(kw)));
  }
  // 3) 若仍为空，退化为包含意图关键词的块
  if (pool.length === 0 && intentKeywords.length) {
    pool = scoredRaw.filter(s => intentKeywords.some(kw => s.normText.includes(kw)));
  }
  // 4) 若还为空，使用全部
  if (pool.length === 0) pool = scoredRaw;

  const topBase = pool.length > 0 ? pool : scoredRaw;
  const top = topBase.sort((a, b) => b.score - a.score).slice(0, topK);
  const items = top.map(s => {
    const prev = index.chunks.find(c => c.chapIndex === s.chapIndex - 1);
    const next = index.chunks.find(c => c.chapIndex === s.chapIndex + 1);
    const text = [prev?.text, s.rawText, next?.text].filter(Boolean).join('\n');
    return {
      chunkId: s.chunkId,
      chapIndex: s.chapIndex,
      start: s.start,
      end: s.end,
      text,
      score: s.score,
      chapterNo: s.chapterNo,
      chapterTitle: s.chapterTitle,
      prev: prev ? { chapterNo: prev.chapterNo, chapterTitle: prev.chapterTitle, text: prev.text } : undefined,
      next: next ? { chapterNo: next.chapterNo, chapterTitle: next.chapterTitle, text: next.text } : undefined
    } as EvidenceItem;
  });

  return { workTitle, query: userQuery, items };
}

// 保留原有的后备方法（用于非核心人名场景）
function extractPossibleNames(text: string): string[] {
  const m = text.match(/[\u4e00-\u9fa5]{2,4}/g);
  if (!m) return [];
  return Array.from(new Set(m)).slice(0, 4);
} 