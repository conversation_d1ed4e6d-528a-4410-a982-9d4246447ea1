export const ACTION_TIE_WORDS: string[] = ['缠','勒','绞','脖','颈','喉','咬','七寸','缠住','贴颈','勒紧','松口','松开','束缚','缠绕'];

// 与火/点烟/打火相关的动作词
export const ACTION_FIRE_WORDS: string[] = ['火','火柴','打火','打火机','点烟','点火','火苗','烟','香烟','烟头','Zippo','zippo'];

// 罕见实体/别名词典（可逐步扩充）
export const ENTITY_ALIASES: Record<string, string[]> = {
  '大黄龙': ['大黄龙', '大黄 龙', '大 黄龙', '大 黄 龙', '黄龙', '宠物蛇', '小醋包'],
  '小醋包': ['小醋包', '醋包', '宠物蛇'],
};

// 场景/道具/地点等意图关键词别名（用于从用户提示提取关键约束）
export const SCENE_INTENT_ALIASES: Record<string, string[]> = {
  '打火机': ['打火机', '打火器', '火机', 'Zippo', 'zippo'],
  '火柴': ['火柴', '火柴盒'],
  '点烟': ['点烟', '点火', '打火', '点着', '火苗'],
  '香烟': ['香烟', '烟', '中南海', '烟盒'],
  '便利店': ['便利店', '超市', '收银台', '收银员'],
  // 蛇相关动作/物件（贴近“缠脖/七寸/解开/蛇箱/冷冻箱”等原文描写）
  '缠脖': ['缠脖', '贴颈', '勒紧', '缠住', '束缚'],
  '七寸': ['七寸', '掐七寸'],
  '蛇箱': ['蛇箱', '冷冻箱'],
  '解开': ['解开', '松口', '松开'],
};

// 常见角色（基础兜底；优先使用 characters.json 里的名字）
export const CORE_NAMES: string[] = ['吴所畏', '池骋', '汪朕', '岳悦', '郭城宇', '汪硕', '刚子', '小天', '姜小帅'];

// 负面/排除关键词（用于向量检索后加权惩罚或过滤）
export const EXCLUDE_TERMS: string[] = ['斗蛇','斗场','观战','下注','眼镜王蛇'];

// 全角转半角 + 去除空白与常见标点，用于“包含匹配”的鲁棒规范化
export function normalizeChinese(input: string): string {
  if (!input) return '';
  let s = input
    .replace(/\u3000/g, ' ') // 全角空格
    .replace(/[\uFF01-\uFF5E]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) - 0xFEE0)) // 全角标点转半角
    .toLowerCase();
  // 去空白
  s = s.replace(/\s+/g, '');
  // 去常见标点/连字符/破折号等
  s = s.replace(/[~·•—–\-＿_\.,:：，、！!？\?\(\)\[\]{}"“”‘’'`]/g, '');
  return s;
}

// 展开查询中的别名命中，返回“规范化后的短语列表”用于精确包含匹配
export function expandAliasesInQuery(query: string): string[] {
  const normQ = normalizeChinese(query);
  const results: string[] = [];
  for (const [canonical, variants] of Object.entries(ENTITY_ALIASES)) {
    const all = Array.from(new Set([canonical, ...variants]));
    const normalizedAll = all.map(normalizeChinese);
    if (normalizedAll.some(v => normQ.includes(v))) {
      results.push(...normalizedAll);
    }
  }
  // 去重
  return Array.from(new Set(results));
}

// 展开场景/意图关键词（道具/地点/动作），用于严格过滤与加权
export function expandIntentKeywords(query: string): string[] {
  const normQ = normalizeChinese(query);
  const results: string[] = [];
  for (const [canonical, variants] of Object.entries(SCENE_INTENT_ALIASES)) {
    const all = Array.from(new Set([canonical, ...variants]));
    const normalizedAll = all.map(normalizeChinese);
    if (normalizedAll.some(v => normQ.includes(v))) {
      results.push(...normalizedAll);
    }
  }
  return Array.from(new Set(results));
}

export function extractCoreNamesFromQuery(query: string, knownNames: string[] = CORE_NAMES): string[] {
  const names: string[] = [];
  for (const nm of knownNames) {
    if (!nm) continue;
    if (query.includes(nm)) names.push(nm);
  }
  return Array.from(new Set(names));
} 