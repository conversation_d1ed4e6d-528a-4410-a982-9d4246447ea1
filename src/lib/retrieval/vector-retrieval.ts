import { getSupabaseClient, getServiceSupabaseClient } from '@/lib/db/supabase';
import { normalizeChinese, CORE_NAMES, EXCLUDE_TERMS, expandIntentKeywords } from './aliases';
import { embedQuery } from '@/lib/embedding';
import { ENTITY_ALIASES } from './aliases';

export interface RetrievedChunk {
	id: number;
	chap_index: number;
	chapter_no?: number | null;
	chapter_title?: string | null;
	start: number;
	end: number;
	chunk_text: string;
	similarity: number;
}

export interface RetrieveOptions {
	workTitle: string;
	query: string;
	topK?: number;
	oversample?: number;
}

function envNumber(key: string, fallback: number): number {
	const v = process.env[key];
	if (!v) return fallback;
	const n = Number(v);
	return Number.isFinite(n) ? n : fallback;
}

function envList(key: string, def: string[]): string[] {
	const v = process.env[key];
	if (!v) return def;
	return v.split(',').map(s => s.trim()).filter(Boolean);
}

function rewriteQuery(original: string): string {
	const forceNames = ['池骋', '吴所畏'];
	// 命中“大黄龙”时展开同义（宠物蛇/小醋包/黄蟒）
	const expansions: string[] = [];
	const q = original || '';
	// 扫描实体别名
	for (const [canonical, variants] of Object.entries(ENTITY_ALIASES)) {
		const hits = [canonical, ...variants].filter(v => q.includes(v));
		if (hits.length) {
			expansions.push(canonical, ...variants);
		}
	}
	// 意图关键词（缠脖/七寸/蛇箱/松口 等）
	const intentNorms = expandIntentKeywords(q);
	const intentReadable = intentNorms; // 直接拼接规范化短语即可
	// 合成：原问题 + 强制人名 + 意图词 + 别名扩展
	const tokens = [q, ...forceNames, ...intentReadable, ...expansions];
	const rewritten = Array.from(new Set(tokens.filter(Boolean))).join(' ');
	if (process.env.LOG_RERANK === 'true') {
		try { console.log('🔧 QueryRewrite:', rewritten.slice(0, 300)); } catch {}
	}
	return rewritten;
}

export async function retrieveFromSupabase(opts: RetrieveOptions): Promise<RetrievedChunk[]> {
	const supa = getServiceSupabaseClient() || getSupabaseClient();
	if (!supa) {
		console.log('⚠️ Supabase客户端未初始化');
		return [];
	}
	const topK = opts.topK ?? 12;
	const oversample = opts.oversample ?? topK * 3;
	try {
		const useRewrite = (process.env.RAG_QUERY_REWRITE || 'true') !== 'false';
		const qText = useRewrite ? rewriteQuery(opts.query) : opts.query;
		const qVec = await embedQuery(qText);
		if (!qVec || !qVec.length) {
			console.log('⚠️ 查询向量化失败');
			return [];
		}
		console.log('🔍 正在调用Supabase RPC函数 match_work_chunks...');
		const { data, error } = await supa.rpc('match_work_chunks', {
			work_title: opts.workTitle,
			query_embedding: qVec,
			match_count: oversample,
		});
		if (error) { 
			console.error('Supabase RPC error:', error.message);
			console.error('Error details:', error);
			return []; 
		}
		console.log('✅ Supabase RPC调用成功，返回数据条数:', (data || []).length);
		return (data || []) as RetrievedChunk[];
	} catch (e:any) {
		console.log('retrieveFromSupabase error:', e?.message || String(e));
		// 添加详细的错误信息
		if (e?.cause) {
			console.log('Error cause:', e.cause);
		}
		return [];
	}
}

export function rerankHeuristically(query: string, items: RetrievedChunk[]): RetrievedChunk[] {
	const qn = normalizeChinese(query);
	const names = ['池骋','吴所畏'];
	const intents = expandIntentKeywords(query); // 已做规范化
	const excludes = Array.from(new Set([...EXCLUDE_TERMS, ...envList('RAG_EXCLUDE_TERMS', [])])).map(normalizeChinese);

	const NAME_PAIR_BONUS = envNumber('RAG_NAME_PAIR_BONUS', 0.40);
	const INTENT_BONUS = envNumber('RAG_INTENT_BONUS', 0.20);
	const NEG_PENALTY = envNumber('RAG_NEG_PENALTY', 0.50);
	const TOPK = envNumber('RAG_TOPK', 8);

	// 硬过滤层级
	const nm1 = items.filter(it => {
		const t = it.chunk_text || '';
		return names.every(n => t.includes(n));
	});
	const nm2 = items.filter(it => {
		const tn = normalizeChinese(it.chunk_text || '');
		return names.some(n => (it.chunk_text || '').includes(n)) && intents.some(i => tn.includes(i));
	});
	const nm3 = items.filter(it => {
		const tn = normalizeChinese(it.chunk_text || '');
		return intents.some(i => tn.includes(i));
	});
	let pool: RetrievedChunk[] = nm1.length ? nm1 : nm2.length ? nm2 : nm3.length ? nm3 : items;

	const scored = pool.map(it => {
		const tn = normalizeChinese(it.chunk_text || '');
		let score = it.similarity || 0;
		// 人名双共现
		if (names.every(n => (it.chunk_text || '').includes(n))) score += NAME_PAIR_BONUS;
		// 意图命中加分（取一次即可）
		if (intents.some(i => tn.includes(i))) score += INTENT_BONUS;
		// 负向命中扣分
		if (excludes.some(ex => tn.includes(ex))) score -= NEG_PENALTY;
		return { it, score };
	}).sort((a,b) => b.score - a.score)
	 .map(x => x.it)
	 .slice(0, TOPK);

	if (process.env.LOG_RERANK === 'true') {
		try {
			const preview = scored.slice(0, 5).map((it, idx) => ({ idx, chap_index: it.chap_index, sim: it.similarity, textPreview: (it.chunk_text||'').slice(0, 80) }));
			console.log('🔬 重排预览(top5):', JSON.stringify(preview));
		} catch {}
	}
	return scored;
} 