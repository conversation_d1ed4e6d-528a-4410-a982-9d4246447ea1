import { getServiceSupabaseClient } from '@/lib/db/supabase';
import { expandAliasesInQuery, normalizeChinese, extractCoreNamesFromQuery, expandIntentKeywords } from './aliases';

export interface EvidenceItem {
  id: number;
  work_title: string;
  chapter_no?: number;
  chapter_title?: string;
  chunk_text: string;
  score: number;
}

export interface EvidencePack {
  workTitle: string;
  query: string;
  items: EvidenceItem[];
}

export async function searchEvidenceSupabase(workTitle: string, userQuery: string, topK: number = 8): Promise<EvidencePack> {
  const supabase = getServiceSupabaseClient();
  
  if (!supabase) {
    console.error('Supabase client not available');
    return { workTitle, query: userQuery, items: [] };
  }

  try {
    // 首先尝试使用中文配置进行全文搜索
    let data: any[] | null = null;
    let error: any = null;
    
    try {
      console.log(`🔍 尝试使用chinese配置搜索: ${userQuery}`);
      const result = await supabase
        .from('book_chunks')
        .select('id, work_title, chapter_no, chapter_title, chunk_text')
        .eq('work_title', workTitle)
        .textSearch('fts', userQuery, {
          type: 'websearch',
          config: 'chinese'
        })
        .limit(topK * 2); // 获取更多结果以便后续处理
      
      data = result.data;
      error = result.error;
      
      if (error) {
        throw new Error(error.message);
      }
    } catch (chineseConfigError: any) {
      // 如果中文配置不存在，则使用默认配置
      console.warn('Chinese text search configuration not found, falling back to default configuration:', chineseConfigError.message);
      const result = await supabase
        .from('book_chunks')
        .select('id, work_title, chapter_no, chapter_title, chunk_text')
        .eq('work_title', workTitle)
        .textSearch('fts', userQuery, {
          type: 'websearch'
          // 不指定config参数，使用默认配置
        })
        .limit(topK * 2); // 获取更多结果以便后续处理
      
      data = result.data;
      error = result.error;
    }

    if (error) {
      console.error('Error searching evidence from Supabase:', error);
      return { workTitle, query: userQuery, items: [] };
    }

    // 应用额外的过滤和评分逻辑
    const scoredItems = (data || []).map(item => {
      let score = 1.0;
      
      // 应用别名/规范化后的精确短语匹配提升
      const exactKeywords = expandAliasesInQuery(userQuery);
      const intentKeywords = expandIntentKeywords(userQuery);
      const roleHints = extractCoreNamesFromQuery(userQuery);
      
      const normText = normalizeChinese(item.chunk_text);
      
      roleHints.forEach(nm => { 
        if (nm && item.chunk_text.includes(nm)) score += 0.4; 
      });
      
      exactKeywords.forEach(kwNorm => { 
        if (kwNorm && normText.includes(kwNorm)) score += 6; 
      });
      
      intentKeywords.forEach(kwNorm => { 
        if (kwNorm && normText.includes(kwNorm)) score += 3; 
      });

      return {
        ...item,
        score
      };
    });

    // 按分数排序并取前topK个
    const topItems = scoredItems
      .sort((a, b) => b.score - a.score)
      .slice(0, topK);

    return { 
      workTitle, 
      query: userQuery, 
      items: topItems 
    };
  } catch (error) {
    console.error('Error in searchEvidenceSupabase:', error);
    return { workTitle, query: userQuery, items: [] };
  }
}
