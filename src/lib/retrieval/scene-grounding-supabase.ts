import fs from 'fs';
import path from 'path';
import { loadIngestIndex } from '../ingest/text-ingest';
import { searchEvidenceSupabase } from './supabase-search';
import { ACTION_TIE_WORDS, expandAliasesInQuery, normalizeChinese, CORE_NAMES, ACTION_FIRE_WORDS, expandIntentKeywords } from './aliases';

export interface GroundedAnchor {
  chapterNo: number;
  chapterTitle?: string;
  chapIndex: number;
  snippet: string;
  prevText?: string;
  nextText?: string;
  score: number;
}

export interface PersonaCard {
  name: string;
  facts: string[]; // 简短事实/约束
  phrases: string[]; // 常用表达/口头禅
}

export interface SceneGroundingResult {
  anchor?: GroundedAnchor;
  involvedNames: string[];
  personas: PersonaCard[];
}

function extractNames(text: string): string[] {
  // 先用核心名单精确匹配，避免被切碎
  const hits = CORE_NAMES.filter(n => text.includes(n));
  if (hits.length) return Array.from(new Set(hits));
  // 兜底：n-gram 中文片段
  const m = text.match(/[\u4e00-\u9fa5]{2,4}/g) || [];
  return Array.from(new Set(m)).slice(0, 4);
}

export async function groundSceneSupabase(workTitle: string, userPrompt: string): Promise<SceneGroundingResult> {
  const names = extractNames(userPrompt);
  
  // 使用Supabase进行证据检索
  const pack = await searchEvidenceSupabase(workTitle, userPrompt, 16);

  // 载入结构化知识
  const dataDir = path.join(process.cwd(), 'data');
  let events: any[] = [];
  let dialogues: any[] = [];
  let characters: any[] = [];
  try { events = JSON.parse(fs.readFileSync(path.join(dataDir, `${workTitle}-events.json`), 'utf8')); } catch {}
  try { dialogues = JSON.parse(fs.readFileSync(path.join(dataDir, `${workTitle}-dialogues.json`), 'utf8')); } catch {}
  try { characters = JSON.parse(fs.readFileSync(path.join(dataDir, `${workTitle}-characters.json`), 'utf8')); } catch {}

  // 精确短语锚点（别名+规范化包含）
  const exact: { chapIndex: number; pos: number; text: string }[] = [];
  const exactNorms = expandAliasesInQuery(userPrompt); // 如命中大黄龙/小醋包等
  const intentNorms = expandIntentKeywords(userPrompt); // 如打火机/火柴/便利店等
  const allExact = Array.from(new Set([...exactNorms, ...intentNorms]));
  
  // 注意：这里我们不再使用本地索引进行精确匹配，而是依赖Supabase的全文搜索结果

  // 若命中精确短语，按“接近缠/勒/脖/颈以及火相关词”的最小距离排序
  const proximityWords = Array.from(new Set([...ACTION_TIE_WORDS, ...ACTION_FIRE_WORDS]));
  if (pack.items.length > 0) {
    // 使用Supabase返回的结果作为基础
    const exactItems = pack.items.filter(item => {
      const normText = normalizeChinese(item.chunk_text);
      return allExact.some(kw => normText.includes(kw));
    });
    
    if (exactItems.length > 0) {
      exactItems.sort((a, b) => {
        const textA = a.chunk_text;
        const textB = b.chunk_text;
        
        const distA = proximityWords
          .map(w => Math.abs(textA.indexOf(w)))
          .filter(d => d >= 0)
          .reduce((m, d) => Math.min(m, d), 1e9);
        const distB = proximityWords
          .map(w => Math.abs(textB.indexOf(w)))
          .filter(d => d >= 0)
          .reduce((m, d) => Math.min(m, d), 1e9);
        return distA - distB;
      });
    }
  }

  // 倒排：事件/对话按姓名共现计数
  const nameSet = new Set(names);
  const eventByChap = new Map<number, number>();
  for (const e of events) {
    const text = `${e.summary || ''}`;
    const hasAny = names.length === 0 ? 0 : names.some(n => text.includes(n)) ? 1 : 0;
    if (hasAny) eventByChap.set(e.chapIndex, (eventByChap.get(e.chapIndex) || 0) + 1);
  }
  const dialogByChap = new Map<number, number>();
  for (const d of dialogues) {
    const hit = (d.speaker && nameSet.has(d.speaker)) || names.some(n => (d.text || '').includes(n));
    if (hit) dialogByChap.set(d.chapIndex, (dialogByChap.get(d.chapIndex) || 0) + 1);
  }

  // 评分融合：精确短语优先 -> tf-idf（已有 pack.score） + 事件 + 对话 + 姓名 + 意图词
  const base = pack.items;
  const hasExact = base.some(item => {
    const normText = normalizeChinese(item.chunk_text);
    return allExact.some(kw => normText.includes(kw));
  });
  
  const scored = base.map(item => {
    const intentHit = intentNorms.length ? intentNorms.some(kw => normalizeChinese(item.chunk_text).includes(kw)) : false;
    const exactBonus = hasExact ? 12 : 0;
    const nameHits = names.filter(n => item.chunk_text.includes(n)).length;
    // 注意：这里我们没有章节索引信息，所以事件和对话计数可能不准确
    const evt = 0; // eventByChap.get(item.chapIndex) || 0;
    const dlg = 0; // dialogByChap.get(item.chapIndex) || 0;
    const final = item.score * 0.45 + evt * 0.2 + dlg * 0.15 + nameHits * 0.05 + exactBonus + (intentHit ? 0.1 : 0);
    return { item, final };
  }).sort((a, b) => b.final - a.final);

  const top = scored[0]?.item;
  let anchor: GroundedAnchor | undefined;
  if (top) {
    anchor = {
      chapterNo: top.chapter_no || 0,
      chapterTitle: top.chapter_title || '',
      chapIndex: top.id, // 使用ID作为章节索引
      snippet: top.chunk_text || '',
      prevText: '', // Supabase版本中没有相邻文本信息
      nextText: '', // Supabase版本中没有相邻文本信息
      score: scored[0].final
    };
  }

  // 人物卡（说话风格指纹 + 基础事实）
  const personas: PersonaCard[] = [];
  const nameToPhrases = new Map<string, string[]>();
  const nameToFacts = new Map<string, string[]>();

  if (dialogues.length) {
    for (const n of names) {
      const lines = dialogues.filter((d: any) => d.speaker === n).slice(0, 10).map((d: any) => (d.text || '').slice(0, 30));
      if (lines.length) nameToPhrases.set(n, Array.from(new Set(lines)).slice(0, 5));
    }
  }
  const factKeywords = ['诊所','医院','牙','篮球','校园','公司','家里','写真','海边','雨夜','争执','复诊','手术','离婚','警方'];
  for (const n of names) {
    const related = events.filter((e: any) => `${e.summary || ''}`.includes(n)).slice(0, 12).map((e: any) => e.summary as string);
    const facts: string[] = [];
    for (const s of related) {
      for (const kw of factKeywords) {
        if (s.includes(kw)) { facts.push(`${kw}相关`); }
      }
    }
    if (facts.length) nameToFacts.set(n, Array.from(new Set(facts)).slice(0, 6));
  }

  for (const n of names) {
    if (!n) continue;
    personas.push({
      name: n,
      facts: nameToFacts.get(n) || [],
      phrases: nameToPhrases.get(n) || []
    });
  }

  return { anchor, involvedNames: names, personas };
}