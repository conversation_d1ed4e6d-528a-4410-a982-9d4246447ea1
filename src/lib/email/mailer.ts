// @ts-nocheck
import nodemailer from 'nodemailer';

export async function sendEmailOtp(to: string, code: string) {
  const user = process.env.QQ_EMAIL_USER || '';
  const pass = process.env.QQ_EMAIL_AUTH_CODE || '';
  if (!user || !pass) {
    return { ok: false, message: '邮箱未配置（缺少 QQ_EMAIL_USER / QQ_EMAIL_AUTH_CODE）' };
  }

  const mail = {
    from: `逆线 <${user}>`,
    to,
    subject: '逆线登录/注册验证码',
    text: `您的验证码是：${code}（5分钟内有效）`,
    html: `<div style="font-size:14px;color:#333">您的验证码是：<b style="font-size:18px">${code}</b>（5分钟内有效）</div>`
  };

  // Try SMTPS 465 first, then fallback to SMTP 587 STARTTLS
  const attempts = [
    { host: 'smtp.qq.com', port: 465, secure: true },
    { host: 'smtp.qq.com', port: 587, secure: false },
  ];

  let lastError: any = null;
  for (const cfg of attempts) {
    try {
      const transporter = nodemailer.createTransport({
        ...cfg,
        auth: { user, pass },
        tls: { ciphers: 'TLSv1.2', rejectUnauthorized: true },
      });
      const info = await transporter.sendMail(mail);
      return { ok: true, message: '邮件已发送', id: info.messageId, via: `${cfg.port}${cfg.secure?' SMTPS':' STARTTLS'}` };
    } catch (e: any) {
      lastError = e;
    }
  }

  // Richer error details
  const err = lastError || {};
  const codeStr = (err.code || err.responseCode || '').toString();
  const resp = typeof err.response === 'string' ? err.response : (err.response && err.response.toString ? err.response.toString() : '');
  return { ok: false, message: err?.message || String(err), code: codeStr, response: resp };
} 