import fs from 'fs/promises';
import path from 'path';

const DATA_DIR = path.join(process.cwd(), 'data');

async function ensureDataDir() {
  try { await fs.mkdir(DATA_DIR, { recursive: true }); } catch {}
}

export async function readJson<T>(fileName: string, fallback: T): Promise<T> {
  await ensureDataDir();
  const filePath = path.join(DATA_DIR, fileName);
  try {
    const buf = await fs.readFile(filePath);
    return JSON.parse(buf.toString()) as T;
  } catch {
    return fallback;
  }
}

export async function writeJson(fileName: string, data: any): Promise<void> {
  await ensureDataDir();
  const filePath = path.join(DATA_DIR, fileName);
  const json = JSON.stringify(data, null, 2);
  await fs.writeFile(filePath, json, 'utf8');
} 