// 核心作品标题归一化
export function canonicalizeWorkTitle(title: string, universe: string = 'novel'): string {
    if (universe === 'real_person') {
        return '逆爱真人宇宙';
    }
    // 对于小说宇宙，进行更详细的归一化
    const raw = (title || '').trim().replace(/\s+/g, '');
    if (raw.includes('逆袭') || raw.includes('逆愛') || raw.includes('池骋') || raw.includes('吴所畏')) {
        return '逆袭';
    }
    return '逆袭'; // 默认到逆袭
}