'use client';

import React, { useState, useEffect } from 'react';

const Key: React.FC<{label: string; desc: string; onPress?: () => void; onRelease?: () => void}> = ({ label, desc, onPress, onRelease }) => (
  <div 
    style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      borderRadius: '6px',
      backgroundColor: 'rgba(255, 255, 255, 0.05)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      padding: '6px 10px',
      cursor: onPress ? 'pointer' : 'default',
      userSelect: 'none'
    }}
    onTouchStart={onPress}
    onTouchEnd={onRelease}
    onMouseDown={onPress}
    onMouseUp={onRelease}
    onMouseLeave={onRelease}
  >
    <span 
      style={{
        minWidth: '28px',
        height: '28px',
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '4px',
        backgroundColor: 'rgba(255, 255, 255, 0.07)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '12px',
        fontWeight: '600',
        letterSpacing: '0.05em'
      }}
    >
      {label}
    </span>
    <span style={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '12px' }}>{desc}</span>
  </div>
);

interface FlightHintProps { 
  particleCount?: number;
}

const FlightHint: React.FC<FlightHintProps> = ({ particleCount }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [pressedKeys, setPressedKeys] = useState<Set<string>>(new Set());

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768 || 'ontouchstart' in window);
    };
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const simulateKeyPress = (code: string) => {
    if (pressedKeys.has(code)) return;
    setPressedKeys(prev => new Set(prev).add(code));
    const event = new KeyboardEvent('keydown', { code, bubbles: true });
    window.dispatchEvent(event);
  };

  const simulateKeyRelease = (code: string) => {
    setPressedKeys(prev => {
      const newSet = new Set(prev);
      newSet.delete(code);
      return newSet;
    });
    const event = new KeyboardEvent('keyup', { code, bubbles: true });
    window.dispatchEvent(event);
  };

  const handleClick = () => {
    if (isMobile) {
      // 移动端点击锁定鼠标（启用飞行模式）
      document.body.requestPointerLock();
    }
  };

  return (
    <div 
      id="navigation-controls" 
      className="hide-on-screenshot"
      style={{
        position: 'fixed',
        bottom: '20px',
        left: '20px',
        zIndex: 40,
        userSelect: 'none'
      }}
    >
      <div 
        style={{
          backdropFilter: 'blur(16px)',
          backgroundColor: 'rgba(0, 0, 0, 0.35)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          padding: '12px',
          width: '260px',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.35)',
          cursor: isMobile ? 'pointer' : 'default'
        }}
        onClick={handleClick}
      >
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '8px' }}>
          <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontSize: '14px', fontWeight: '600' }}>
            {isMobile ? 'Touch Controls' : 'Navigation Controls'}
          </div>
          {typeof particleCount === 'number' && (
            <div 
              style={{
                fontSize: '10px',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              {particleCount.toLocaleString()} Fans Active
            </div>
          )}
        </div>
        
        {isMobile ? (
          <>
            <div 
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: '8px'
              }}
            >
              <Key 
                label="↑" 
                desc="Forward" 
                onPress={() => simulateKeyPress('KeyW')}
                onRelease={() => simulateKeyRelease('KeyW')}
              />
              <Key 
                label="↓" 
                desc="Backward" 
                onPress={() => simulateKeyPress('KeyS')}
                onRelease={() => simulateKeyRelease('KeyS')}
              />
              <Key 
                label="←" 
                desc="Left" 
                onPress={() => simulateKeyPress('KeyA')}
                onRelease={() => simulateKeyRelease('KeyA')}
              />
              <Key 
                label="→" 
                desc="Right" 
                onPress={() => simulateKeyPress('KeyD')}
                onRelease={() => simulateKeyRelease('KeyD')}
              />
              <Key 
                label="⬆" 
                desc="Up" 
                onPress={() => simulateKeyPress('Space')}
                onRelease={() => simulateKeyRelease('Space')}
              />
              <Key 
                label="⬇" 
                desc="Down" 
                onPress={() => simulateKeyPress('ShiftLeft')}
                onRelease={() => simulateKeyRelease('ShiftLeft')}
              />
            </div>
            <div style={{ marginTop: '8px', fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>
              点击控制面板启用飞行模式，使用方向键控制移动。
            </div>
          </>
        ) : (
          <>
            <div 
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(2, 1fr)',
                gap: '8px'
              }}
            >
              <Key label="W" desc="Forward" />
              <Key label="S" desc="Backward" />
              <Key label="A" desc="Left" />
              <Key label="D" desc="Right" />
              <Key label="Space" desc="Up" />
              <Key label="Shift" desc="Down" />
              <Key label="Mouse" desc="Look" />
              <Key label="Click" desc="Lock" />
            </div>
            <div style={{ marginTop: '8px', fontSize: '10px', color: 'rgba(255, 255, 255, 0.6)' }}>
              Click anywhere to capture the mouse and start flying.
            </div>
          </>
        )}
        
        <div 
          style={{
            marginTop: '8px',
            height: '1px',
            background: 'linear-gradient(to right, rgba(102, 107, 206, 0.4) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 214, 79, 0.4) 100%)'
          }}
        />
      </div>
    </div>
  );
};

export default FlightHint;
