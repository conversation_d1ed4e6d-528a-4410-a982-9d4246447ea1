'use client';

import React, { useRef, useMemo, Suspense } from 'react';
import * as THREE from 'three';

// 动态导入Canvas以避免SSR问题
let Canvas: any = null;
let useFrame: any = null;
let useThree: any = null;

// 在客户端加载时动态导入
if (typeof window !== 'undefined') {
  import('@react-three/fiber').then((mod) => {
    Canvas = mod.Canvas;
    useFrame = mod.useFrame;
    useThree = mod.useThree;
  });
}

interface ThreeSceneProps {
  count: number;
}

// 简化版粒子系统
const SimpleParticleSystem: React.FC<{ count: number }> = ({ count }) => {
  const geometry = useMemo(() => {
    const geom = new THREE.BufferGeometry();
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    
    for (let i = 0; i < count; i++) {
      positions[i * 3] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 2000;
      
      colors[i * 3] = 0.4 + Math.random() * 0.6; // R
      colors[i * 3 + 1] = 0.6 + Math.random() * 0.4; // G  
      colors[i * 3 + 2] = 1.0; // B
    }
    
    geom.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geom.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    return geom;
  }, [count]);
  
  return (
    <points geometry={geometry}>
      <pointsMaterial 
        size={3}
        vertexColors
        transparent
        opacity={0.8}
      />
    </points>
  );
};

const ThreeScene: React.FC<ThreeSceneProps> = ({ count }) => {
  // 如果Canvas还没加载，显示加载状态
  if (!Canvas) {
    return (
      <div className="w-full h-screen bg-gradient-to-br from-[#0a0a1a] via-[#1a0a2a] to-[#0a0a1a] flex items-center justify-center">
        <div className="text-white text-xl animate-pulse">
          3D引擎加载中...
        </div>
      </div>
    );
  }
  
  return (
    <div className="w-full h-screen bg-gradient-to-br from-[#0a0a1a] via-[#1a0a2a] to-[#0a0a1a]">
      <Suspense fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-white text-xl animate-pulse">
            渲染中...
          </div>
        </div>
      }>
        <Canvas
          camera={{ position: [0, 0, 100], fov: 75 }}
          gl={{ antialias: true }}
        >
          <fog attach="fog" args={['#0a0a1a', 50, 200]} />
          <SimpleParticleSystem count={Math.min(count, 10000)} />
        </Canvas>
      </Suspense>
    </div>
  );
};

export default ThreeScene;