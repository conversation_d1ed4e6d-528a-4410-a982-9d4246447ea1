'use client';

import React, { useEffect, useState } from 'react';

// 简化的CSS粒子效果
const CSSParticleUniverse: React.FC<{ count?: number }> = ({ count = 100 }) => {
  const [isClient, setIsClient] = useState(false);
  const [particles, setParticles] = useState<JSX.Element[]>([]);

  useEffect(() => {
    setIsClient(true);
    
    // 生成粒子
    const newParticles = Array.from({ length: count }, (_, i) => {
      const colors = ['#666BCE', '#C2A8F2', '#FFD64F'];
      const color = colors[i % 3];
      const size = Math.random() * 4 + 2;
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const duration = Math.random() * 8 + 4;
      const delay = Math.random() * 2;
      
      return (
        <div
          key={i}
          className="css-particle"
          style={{
            position: 'absolute',
            width: `${size}px`,
            height: `${size}px`,
            backgroundColor: color,
            borderRadius: '50%',
            left: `${x}%`,
            top: `${y}%`,
            opacity: Math.random() * 0.8 + 0.3,
            boxShadow: `0 0 ${size * 3}px ${color}`,
            animation: `cssFloat ${duration}s ${delay}s infinite ease-in-out alternate`,
          }}
        />
      );
    });
    
    setParticles(newParticles);
  }, [count]);

  // 避免hydration问题
  if (!isClient) {
    return (
      <div 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          width: '100%',
          height: '100vh',
          background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
          overflow: 'hidden',
          zIndex: 0
        }}
      >
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          textAlign: 'center'
        }}>
          <p>🌌 正在启动粒子宇宙...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* CSS Keyframes */}
      <style jsx global>{`
        @keyframes cssFloat {
          0% { 
            transform: translateY(0px) translateX(0px) scale(1); 
            opacity: 0.3;
          }
          50% { 
            transform: translateY(-30px) translateX(20px) scale(1.2); 
            opacity: 0.8;
          }
          100% { 
            transform: translateY(-10px) translateX(-10px) scale(0.9); 
            opacity: 0.4;
          }
        }
        
        .css-particle {
          pointer-events: none;
        }
      `}</style>
      
      <div 
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          width: '100%',
          height: '100vh',
          background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
          overflow: 'hidden',
          zIndex: 0
        }}
      >
        {particles}
        
        {/* 调试信息 */}
        <div style={{
          position: 'absolute',
          top: '10px',
          left: '10px',
          zIndex: 1000,
          color: 'white',
          background: 'rgba(0,0,0,0.7)',
          padding: '12px',
          borderRadius: '8px',
          fontSize: '12px',
          backdropFilter: 'blur(10px)'
        }}>
          <div>🎨 CSS粒子背景</div>
          <div>粒子数: {count} 个</div>
          <div style={{color: '#666BCE'}}>● 紫色</div>
          <div style={{color: '#C2A8F2'}}>● 紫晶色</div>
          <div style={{color: '#FFD64F'}}>● 柠檬黄</div>
        </div>
      </div>
    </>
  );
};

export default CSSParticleUniverse;