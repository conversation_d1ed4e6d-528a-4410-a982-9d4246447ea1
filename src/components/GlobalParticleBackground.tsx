'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

// 轻量级全局粒子背景组件
const GlobalParticleBackground: React.FC = () => {
  const [isClient, setIsClient] = useState(false);
  const [particles, setParticles] = useState<JSX.Element[]>([]);
  const pathname = usePathname();

  // 在首页且非3D模式时显示全局背景，其他页面也显示
  const shouldShowGlobalBackground = true;

  useEffect(() => {
    setIsClient(true);
    
    // 如果不应该显示全局背景，直接返回
    if (!shouldShowGlobalBackground) {
      return;
    }
    
    // 生成粒子（减少数量以提高性能）
    const newParticles = Array.from({ length: 150 }, (_, i) => {
      const colors = ['#666BCE', '#C2A8F2', '#FFD64F'];
      const color = colors[i % 3];
      const size = Math.random() * 3 + 1;
      const x = Math.random() * 100;
      const y = Math.random() * 100;
      const duration = Math.random() * 8 + 6;
      const delay = Math.random() * 3;
      
      return (
        <div
          key={i}
          className="global-particle"
          style={{
            position: 'fixed',
            width: `${size}px`,
            height: `${size}px`,
            backgroundColor: color,
            borderRadius: '50%',
            left: `${x}%`,
            top: `${y}%`,
            opacity: Math.random() * 0.6 + 0.2,
            boxShadow: `0 0 ${size * 2}px ${color}`,
            animation: `globalFloat ${duration}s ${delay}s infinite ease-in-out alternate`,
            pointerEvents: 'none',
            zIndex: -1
          }}
        />
      );
    });
    
    setParticles(newParticles);
  }, [shouldShowGlobalBackground]);

  // 避免hydration问题
  if (!isClient) {
    return null;
  }

  // 在首页不显示全局背景
  if (!shouldShowGlobalBackground) {
    return null;
  }

  return (
    <>
      {/* CSS Keyframes */}
      <style jsx global>{`
        @keyframes globalFloat {
          0% { 
            transform: translateY(0px) translateX(0px) scale(1); 
            opacity: 0.2;
          }
          50% { 
            transform: translateY(-40px) translateX(30px) scale(1.1); 
            opacity: 0.7;
          }
          100% { 
            transform: translateY(-15px) translateX(-15px) scale(0.9); 
            opacity: 0.3;
          }
        }
        
        .global-particle {
          pointer-events: none;
        }
        
        /* 确保所有页面都有深色背景 */
        body, html {
          background: #0a0a1a !important;
        }
      `}</style>
      
      <div 
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          width: '100vw',
          height: '100vh',
          background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
          overflow: 'hidden',
          zIndex: -10,
          pointerEvents: 'none'
        }}
      >
        {particles}
      </div>
    </>
  );
};

export default GlobalParticleBackground;