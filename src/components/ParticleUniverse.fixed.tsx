'use client';

import React, { useRef, useMemo, useEffect, useCallback } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Vector3, Color, BufferGeometry, BufferAttribute } from 'three';
import * as THREE from 'three';

interface ParticleSystemProps {
  count: number;
}

const ParticleSystem: React.FC<ParticleSystemProps> = ({ count }) => {
  const pointsRef = useRef<THREE.Points>(null);
  
  const geometry = useMemo(() => {
    console.log('🎨 正在生成粒子系统，粒子数量：', count);
    
    const geom = new BufferGeometry();
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    const sizes = new Float32Array(count);

    // 你的CP应援色配置
    const phantomPurple = new Color('#666BCE');
    const amethyst = new Color('#C2A8F2');
    const lemonYellow = new Color('#FFD64F');
    
    for (let i = 0; i < count; i++) {
      // 粒子位置
      const x = (Math.random() - 0.5) * 3000;
      const y = (Math.random() - 0.5) * 3000;
      const z = (Math.random() - 0.5) * 3000;
      
      positions[i * 3] = x;
      positions[i * 3 + 1] = y;
      positions[i * 3 + 2] = z;
      
      // 颜色计算（使用你之前的算法）
      const noiseX = Math.sin(x * 0.001) * Math.cos(y * 0.001);
      const noiseY = Math.cos(y * 0.001) * Math.sin(z * 0.001);
      const noiseZ = Math.sin(z * 0.001) * Math.cos(x * 0.001);
      const combined = (noiseX + noiseY + noiseZ) / 3;
      
      let color: Color;
      if (combined > 0.3) {
        color = lemonYellow.clone();
        sizes[i] = Math.random() * 8 + 5;
      } else if (combined > 0.0) {
        const t = combined / 0.3;
        color = amethyst.clone().lerp(lemonYellow, t);
        sizes[i] = Math.random() * 6 + 3.5;
      } else if (combined > -0.3) {
        const t = (combined + 0.3) / 0.3;
        color = phantomPurple.clone().lerp(amethyst, t);
        sizes[i] = Math.random() * 4 + 2.5;
      } else {
        color = phantomPurple.clone();
        sizes[i] = Math.random() * 3 + 1.5;
      }
      
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
    }
    
    geom.setAttribute('position', new BufferAttribute(positions, 3));
    geom.setAttribute('color', new BufferAttribute(colors, 3));
    geom.setAttribute('size', new BufferAttribute(sizes, 1));
    
    console.log('✅ 粒子几何体生成完成');
    return geom;
  }, [count]);
  
  useFrame((state, delta) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.x += delta * 0.05;
      pointsRef.current.rotation.y += delta * 0.03;
    }
  });
  
  return (
    <points ref={pointsRef} geometry={geometry}>
      <pointsMaterial 
        size={4.6}
        vertexColors={true}
        transparent={true}
        blending={THREE.AdditiveBlending}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.95}
      />
    </points>
  );
};

// WASD 控制相机
const CameraController: React.FC = () => {
  const { camera } = useThree();
  const velocity = useRef(new Vector3());
  const keys = useRef<{ [key: string]: boolean }>({});
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => { 
    keys.current[event.code] = true; 
  }, []);
  
  const handleKeyUp = useCallback((event: KeyboardEvent) => { 
    keys.current[event.code] = false; 
  }, []);
  
  useEffect(() => { 
    window.addEventListener('keydown', handleKeyDown); 
    window.addEventListener('keyup', handleKeyUp); 
    return () => { 
      window.removeEventListener('keydown', handleKeyDown); 
      window.removeEventListener('keyup', handleKeyUp); 
    }; 
  }, [handleKeyDown, handleKeyUp]);
  
  useFrame((state, delta) => {
    const speed = 100 * delta;
    const damp = 0.85;
    
    // WASD 控制
    if (keys.current['KeyW'] || keys.current['ArrowUp']) velocity.current.z -= speed;
    if (keys.current['KeyS'] || keys.current['ArrowDown']) velocity.current.z += speed;
    if (keys.current['KeyA'] || keys.current['ArrowLeft']) velocity.current.x -= speed;
    if (keys.current['KeyD'] || keys.current['ArrowRight']) velocity.current.x += speed;
    if (keys.current['Space']) velocity.current.y += speed;
    if (keys.current['ShiftLeft'] || keys.current['ControlLeft']) velocity.current.y -= speed;
    
    // 应用移动
    const forward = new Vector3(0, 0, -1).applyQuaternion(camera.quaternion);
    const right = new Vector3(1, 0, 0).applyQuaternion(camera.quaternion);
    const up = new Vector3(0, 1, 0);
    
    camera.position.add(forward.clone().multiplyScalar(velocity.current.z));
    camera.position.add(right.clone().multiplyScalar(velocity.current.x));
    camera.position.add(up.clone().multiplyScalar(velocity.current.y));
    
    velocity.current.multiplyScalar(damp);
  });
  
  return null;
};

// 鼠标控制视角
const MouseLook: React.FC = () => {
  const { camera } = useThree();
  const isLocked = useRef(false);
  
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => { 
      if (!isLocked.current) return; 
      const sensitivity = 0.002; 
      camera.rotation.y -= event.movementX * sensitivity; 
      camera.rotation.x -= event.movementY * sensitivity; 
      camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, camera.rotation.x)); 
    };
    
    const handleClick = (event: MouseEvent) => { 
      if (event.target instanceof HTMLElement && event.target.tagName === 'CANVAS') {
        if (document.pointerLockElement) { 
          isLocked.current = true; 
        } else { 
          document.body.requestPointerLock(); 
          isLocked.current = true; 
        } 
      }
    };
    
    const handlePointerLockChange = () => { 
      isLocked.current = !!document.pointerLockElement; 
    };
    
    document.addEventListener('mousemove', handleMouseMove); 
    document.addEventListener('click', handleClick); 
    document.addEventListener('pointerlockchange', handlePointerLockChange);
    document.addEventListener('mozpointerlockchange', handlePointerLockChange);
    document.addEventListener('webkitpointerlockchange', handlePointerLockChange);
    
    return () => { 
      document.removeEventListener('mousemove', handleMouseMove); 
      document.removeEventListener('click', handleClick); 
      document.removeEventListener('pointerlockchange', handlePointerLockChange);
      document.removeEventListener('mozpointerlockchange', handlePointerLockChange);
      document.removeEventListener('webkitpointerlockchange', handlePointerLockChange);
    };
  }, [camera]);
  
  return null;
};

interface ParticleUniverseProps { 
  count?: number;
}

const FixedParticleUniverse: React.FC<ParticleUniverseProps> = ({ count = 10000 }) => {
  console.log('🚀 ParticleUniverse 组件开始渲染，粒子数量：', count);
  
  return (
    <div 
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100vh',
        background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
        overflow: 'hidden',
        zIndex: 0
      }}
    >
      {/* 调试信息 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        left: '10px',
        zIndex: 1000,
        color: 'white',
        background: 'rgba(0,0,0,0.7)',
        padding: '10px',
        fontSize: '12px',
        borderRadius: '5px'
      }}>
        <div>粒子数量: {count}</div>
        <div>WASD: 移动 | 鼠标: 点击画布锁定视角</div>
        <div>颜色: 紫色 + 紫晶色 + 柠檬黄</div>
      </div>
      
      <Canvas
        camera={{ position: [0, 0, 900], fov: 65, near: 1, far: 5000 }}
        gl={{ 
          antialias: false,
          alpha: false, 
          powerPreference: 'high-performance',
          preserveDrawingBuffer: false,
          stencil: false,
          depth: true
        }}
        onCreated={(state) => {
          console.log('📺 Canvas 创建成功, WebGL 渲染器：', state.gl.getParameter(state.gl.VERSION));
        }}
      >
        <fog attach="fog" args={['#0a0a1a', 500, 2000]} />
        <ParticleSystem count={count} />
        <CameraController />
        <MouseLook />
      </Canvas>
    </div>
  );
};

export default FixedParticleUniverse;