'use client';

import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import * as THREE from 'three';

interface BreathingParticleProps {
  position: [number, number, number];
  color: string;
  speed: number;
}

const BreathingParticle: React.FC<BreathingParticleProps> = ({ position, color, speed }) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const timeRef = useRef(0);

  useFrame((state) => {
    if (!meshRef.current) return;
    
    timeRef.current += speed;
    
    // 呼吸效果 - 大小变化
    const breathScale = 1 + Math.sin(timeRef.current * 0.8) * 0.3;
    meshRef.current.scale.setScalar(breathScale);
    
    // 柔和的透明度变化
    const opacity = 0.4 + Math.sin(timeRef.current * 0.5) * 0.2;
    if (meshRef.current.material instanceof THREE.Material) {
      (meshRef.current.material as any).opacity = opacity;
    }
    
    // 微妙的位置浮动
    meshRef.current.position.y = position[1] + Math.sin(timeRef.current * 0.3) * 0.5;
  });

  return (
    <mesh ref={meshRef} position={position}>
      <sphereGeometry args={[0.02, 8, 8]} />
      <meshBasicMaterial 
        color={color} 
        transparent 
        opacity={0.6}
        blending={THREE.AdditiveBlending}
      />
    </mesh>
  );
};

interface BreathingUniverseProps {
  particleCount?: number;
  intensity?: 'subtle' | 'medium' | 'strong';
}

const BreathingUniverse: React.FC<BreathingUniverseProps> = ({ 
  particleCount = 500,
  intensity = 'medium'
}) => {
  const cpColors = ['#666BCE', '#C2A8F2', '#FFD64F'];
  
  const particles = useMemo(() => {
    const result = [];
    const range = intensity === 'subtle' ? 15 : intensity === 'medium' ? 20 : 25;
    
    for (let i = 0; i < particleCount; i++) {
      result.push({
        position: [
          (Math.random() - 0.5) * range,
          (Math.random() - 0.5) * range,
          (Math.random() - 0.5) * range,
        ] as [number, number, number],
        color: cpColors[Math.floor(Math.random() * cpColors.length)],
        speed: 0.008 + Math.random() * 0.004, // 更缓慢的呼吸节奏
      });
    }
    return result;
  }, [particleCount, intensity]);

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        zIndex: 1,
        pointerEvents: 'none',
      }}
    >
      <Canvas
        camera={{ 
          position: [0, 0, 10], 
          fov: 75,
          near: 0.1,
          far: 1000
        }}
        style={{ background: 'transparent' }}
      >
        <ambientLight intensity={0.2} />
        {particles.map((particle, index) => (
          <BreathingParticle
            key={index}
            position={particle.position}
            color={particle.color}
            speed={particle.speed}
          />
        ))}
      </Canvas>
    </div>
  );
};

export default BreathingUniverse;