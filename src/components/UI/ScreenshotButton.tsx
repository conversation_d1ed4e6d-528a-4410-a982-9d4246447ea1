import React from 'react';
import html2canvas from 'html2canvas';

const ScreenshotButton: React.FC = () => {
  const handleShot = async () => {
    const elementsToIgnore = document.querySelectorAll('.hide-on-screenshot');
    const watermark = document.getElementById('screenshot-watermark');

    elementsToIgnore.forEach((el) => el.classList.add('ignore-in-screenshot'));
    if (watermark) watermark.classList.remove('hidden');

    try {
      const target = document.getElementById('screenshot-root') || document.body;
      const rect = target.getBoundingClientRect();
      const canvas = await html2canvas(target, {
        backgroundColor: '#000000',
        scale: 2,
        useCORS: true,
        width: Math.ceil(rect.width),
        height: Math.ceil(rect.height),
        windowWidth: Math.ceil(rect.width),
        windowHeight: Math.ceil(rect.height),
        ignoreElements: (el) => el.classList?.contains('ignore-in-screenshot')
      });
      const a = document.createElement('a');
      a.href = canvas.toDataURL('image/png');
      a.download = `nixian-screenshot-${Date.now()}.png`;
      a.click();
    } catch (e) {
      console.error('Screenshot failed', e);
    } finally {
      elementsToIgnore.forEach((el) => el.classList.remove('ignore-in-screenshot'));
      if (watermark) watermark.classList.add('hidden');
    }
  };
  return (
    <button onClick={handleShot} title="Screenshot" className="fixed bottom-6 right-6 z-40 w-12 h-12 rounded-full backdrop-blur-xl bg-black/35 border border-white/10 text-white/85 hover:text-white shadow-[0_10px_30px_rgba(0,0,0,0.35)]">
      ⌁
    </button>
  );
};

export default ScreenshotButton; 