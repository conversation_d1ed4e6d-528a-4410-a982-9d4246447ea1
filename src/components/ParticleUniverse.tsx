'use client';

import React, { useRef, useMemo, useCallback, useEffect, useState } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Vector3, Color, BufferGeometry, BufferAttribute } from 'three';
import * as THREE from 'three';

// 全局缓存策略
const CACHE_KEY = 'nixian_particle_cache';
const CACHE_VERSION = '1.0';
let globalGeometryCache: BufferGeometry | null = null;
let cacheInitialized = false;

interface ParticleSystemProps {
  count: number;
}

const ParticleSystem: React.FC<ParticleSystemProps> = ({ count }) => {
  const pointsRef = useRef<THREE.Points>(null);
  const { camera } = useThree();
  const [isLoading, setIsLoading] = useState(!cacheInitialized);
  
  const geometry = useMemo(() => {
    // 尝试从缓存加载
    if (globalGeometryCache && cacheInitialized) {
      setIsLoading(false);
      return globalGeometryCache;
    }
    
    console.log('🎨 首次渲染逆线宇宙，正在生成缓存...');
    const startTime = performance.now();
    
    const geom = new BufferGeometry();
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    const sizes = new Float32Array(count);

    const phantomPurple = new Color('#666BCE');
    const amethyst = new Color('#C2A8F2');
    const lemonYellow = new Color('#FFD64F');
    
    // 批量处理以提高性能
    const batchSize = 1000;
    for (let batch = 0; batch < count; batch += batchSize) {
      const end = Math.min(batch + batchSize, count);
      for (let i = batch; i < end; i++) {
        const x = (Math.random() - 0.5) * 3000;
        const y = (Math.random() - 0.5) * 3000;
        const z = (Math.random() - 0.5) * 3000;
        positions[i * 3] = x; positions[i * 3 + 1] = y; positions[i * 3 + 2] = z;
        const noiseX = Math.sin(x * 0.001) * Math.cos(y * 0.001);
        const noiseY = Math.cos(y * 0.001) * Math.sin(z * 0.001);
        const noiseZ = Math.sin(z * 0.001) * Math.cos(x * 0.001);
        const combined = (noiseX + noiseY + noiseZ) / 3;
        let color: Color;
        if (combined > 0.3) { color = lemonYellow.clone(); sizes[i] = Math.random() * 8 + 5; }
        else if (combined > 0.0) { const t = combined / 0.3; color = amethyst.clone().lerp(lemonYellow, t); sizes[i] = Math.random() * 6 + 3.5; }
        else if (combined > -0.3) { const t = (combined + 0.3) / 0.3; color = phantomPurple.clone().lerp(amethyst, t); sizes[i] = Math.random() * 4 + 2.5; }
        else { color = phantomPurple.clone(); sizes[i] = Math.random() * 3 + 1.5; }
        colors[i * 3] = color.r; colors[i * 3 + 1] = color.g; colors[i * 3 + 2] = color.b;
      }
    }
    
    geom.setAttribute('position', new BufferAttribute(positions, 3));
    geom.setAttribute('color', new BufferAttribute(colors, 3));
    geom.setAttribute('size', new BufferAttribute(sizes, 1));
    
    // 缓存几何体
    globalGeometryCache = geom;
    cacheInitialized = true;
    
    const endTime = performance.now();
    console.log(`✨ 逆线宇宙生成完成！耗时: ${(endTime - startTime).toFixed(2)}ms`);
    console.log('🚀 下次访问将直接使用缓存，加载更快！');
    
    setIsLoading(false);
    return geom;
  }, [count]);
  
  // 组件卸载时不清理缓存，保持全局可用
  useEffect(() => {
    return () => {
      // 不清理 globalGeometryCache，让其他用户可以复用
    };
  }, []);
  
  useFrame((state, delta) => {
    // 粒子旋转动画
    if (pointsRef.current) {
      pointsRef.current.rotation.x += delta * 0.05;
      pointsRef.current.rotation.y += delta * 0.03;
    }
  });
  
  if (isLoading) {
    return (
      <mesh>
        <sphereGeometry args={[1, 8, 8]} />
        <meshBasicMaterial color="#666BCE" transparent opacity={0.1} />
      </mesh>
    );
  }
  
  return (
    <points ref={pointsRef} geometry={geometry} frustumCulled={true}>
      <pointsMaterial 
        size={4.6}
        vertexColors={true}
        transparent={true}
        blending={THREE.AdditiveBlending}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.95}
      />
    </points>
  );
};

interface UniverseProps { 
  count?: number;
}

const CameraController: React.FC = () => {
  const { camera } = useThree();
  const velocity = useRef(new Vector3());
  const keys = useRef<{ [key: string]: boolean }>({});
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => { 
    keys.current[event.code] = true; 
  }, []);
  
  const handleKeyUp = useCallback((event: KeyboardEvent) => { 
    keys.current[event.code] = false; 
  }, []);
  
  useEffect(() => { 
    window.addEventListener('keydown', handleKeyDown); 
    window.addEventListener('keyup', handleKeyUp); 
    return () => { 
      window.removeEventListener('keydown', handleKeyDown); 
      window.removeEventListener('keyup', handleKeyUp); 
    }; 
  }, [handleKeyDown, handleKeyUp]);
  
  useFrame((state, delta) => {
    const speed = 100 * delta; 
    const damp = 0.85; // 增加阻尼效果
    
    // 检查按键状态并更新速度
    if (keys.current['KeyW'] || keys.current['ArrowUp']) velocity.current.z -= speed;
    if (keys.current['KeyS'] || keys.current['ArrowDown']) velocity.current.z += speed;
    if (keys.current['KeyA'] || keys.current['ArrowLeft']) velocity.current.x -= speed;
    if (keys.current['KeyD'] || keys.current['ArrowRight']) velocity.current.x += speed;
    if (keys.current['Space']) velocity.current.y += speed;
    if (keys.current['ShiftLeft'] || keys.current['ControlLeft']) velocity.current.y -= speed;
    
    // 应用移动
    const forward = new Vector3(0, 0, -1).applyQuaternion(camera.quaternion);
    const right = new Vector3(1, 0, 0).applyQuaternion(camera.quaternion);
    const up = new Vector3(0, 1, 0);
    
    camera.position.add(forward.clone().multiplyScalar(velocity.current.z));
    camera.position.add(right.clone().multiplyScalar(velocity.current.x));
    camera.position.add(up.clone().multiplyScalar(velocity.current.y));
    
    // 应用阻尼
    velocity.current.multiplyScalar(damp);
  });
  
  return null;
};

const MouseLook: React.FC = () => {
  const { camera } = useThree();
  const isLocked = useRef(false);
  
  useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => { 
      if (!isLocked.current) return; 
      const s = 0.002; 
      camera.rotation.y -= event.movementX * s; 
      camera.rotation.x -= event.movementY * s; 
      camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, camera.rotation.x)); 
    };
    
    const handleClick = (event: MouseEvent) => { 
      // 确保点击的是画布区域
      if (event.target instanceof HTMLElement && event.target.tagName === 'CANVAS') {
        if (document.pointerLockElement) { 
          isLocked.current = true; 
        } else { 
          document.body.requestPointerLock(); 
          isLocked.current = true; 
        } 
      }
    };
    
    const handlePtr = () => { 
      isLocked.current = !!document.pointerLockElement; 
    };
    
    // 添加多个事件监听器以提高兼容性
    document.addEventListener('mousemove', handleMouseMove); 
    document.addEventListener('click', handleClick); 
    document.addEventListener('pointerlockchange', handlePtr);
    document.addEventListener('mozpointerlockchange', handlePtr);
    document.addEventListener('webkitpointerlockchange', handlePtr);
    
    return () => { 
      document.removeEventListener('mousemove', handleMouseMove); 
      document.removeEventListener('click', handleClick); 
      document.removeEventListener('pointerlockchange', handlePtr);
      document.removeEventListener('mozpointerlockchange', handlePtr);
      document.removeEventListener('webkitpointerlockchange', handlePtr);
    };
  }, [camera]);
  
  return null;
};

const ParticleUniverse: React.FC<UniverseProps> = ({ count = 20000 }) => {
  // 简化粒子数量，减少复杂性
  const adaptiveCount = Math.min(count, 15000);
  
  return (
    <div 
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100vh',
        background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
        overflow: 'hidden',
        zIndex: 0
      }}
    >
      {/* 简化的加载指示器 */}
      <Canvas
        camera={{ position: [0, 0, 900], fov: 65, near: 1, far: 5000 }}
        gl={{ 
          antialias: false, // 关闭反锐齿提升性能
          alpha: false, 
          powerPreference: 'high-performance', 
          preserveDrawingBuffer: false, // 关闭以提升性能
          stencil: false,
          depth: true,
          logarithmicDepthBuffer: false
        }}
        dpr={typeof window !== 'undefined' ? Math.min(window.devicePixelRatio, 1.5) : 1} // 限制像素比
        performance={{ min: 0.5 }} // 性能监控
        frameloop="always" // 始终渲染以确保粒子动画
        flat // 使用平面着色提升性能
      >
        <fog attach="fog" args={['#0a0a1a', 500, 2000]} />
        <ParticleSystem count={adaptiveCount} />
        <CameraController />
        <MouseLook />
      </Canvas>
    </div>
  );
};

export default ParticleUniverse;