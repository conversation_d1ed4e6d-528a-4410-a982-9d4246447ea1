'use client';

import React from 'react';
import html2canvas from 'html2canvas';

const ScreenshotButton: React.FC = () => {
  const handleScreenshot = async () => {
    console.log('开始截图...');
    const elementsToIgnore = document.querySelectorAll('.hide-on-screenshot');
    const watermark = document.getElementById('screenshot-watermark');
    
    console.log('找到需要隐藏的元素:', elementsToIgnore.length);
    console.log('水印元素:', watermark);

    // 隐藏所有需要隐藏的元素
    elementsToIgnore.forEach((el) => {
      const element = el as HTMLElement;
      element.style.display = 'none';
    });
    
    // 显示水印
    if (watermark) {
      watermark.style.display = 'block';
      console.log('水印已显示');
    } else {
      console.error('未找到水印元素');
    }

    // 等待一下确保DOM更新
    await new Promise(resolve => setTimeout(resolve, 100));

    try {
      const target = document.getElementById('screenshot-root') || document.body;
      const rect = target.getBoundingClientRect();
      console.log('截图目标:', target, '尺寸:', rect);
      
      const canvas = await html2canvas(target, {
        backgroundColor: '#000000',
        scale: 2,
        useCORS: true,
        width: Math.ceil(rect.width),
        height: Math.ceil(rect.height),
        windowWidth: Math.ceil(rect.width),
        windowHeight: Math.ceil(rect.height),
        logging: true // 启用调试日志
      });
      
      console.log('截图完成，canvas尺寸:', canvas.width, 'x', canvas.height);
      
      const a = document.createElement('a');
      a.href = canvas.toDataURL('image/png');
      a.download = `nixian-screenshot-${Date.now()}.png`;
      a.click();
      console.log('图片下载链接已触发');
    } catch (e) {
      console.error('Screenshot failed', e);
    } finally {
      // 恢复隐藏的元素
      elementsToIgnore.forEach((el) => {
        const element = el as HTMLElement;
        element.style.display = '';
      });
      
      // 隐藏水印
      if (watermark) {
        watermark.style.display = 'none';
        console.log('水印已隐藏');
      }
      console.log('截图流程完成');
    }
  };

  return (
    <button 
      className="hide-on-screenshot"
      onClick={handleScreenshot}
      title="截图"
      style={{
        position: 'fixed',
        bottom: '140px', // 调整到创作岛上方
        right: '32px',
        zIndex: 50, // 提高z-index确保显示在最上层
        width: '48px',
        height: '48px',
        borderRadius: '50%',
        backdropFilter: 'blur(16px)',
        backgroundColor: 'rgba(0, 0, 0, 0.35)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        color: 'rgba(255, 255, 255, 0.85)',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.35)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: '28px'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.color = 'rgba(255, 255, 255, 1)';
        e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.color = 'rgba(255, 255, 255, 0.85)';
        e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.35)';
      }}
    >
      ⌁
    </button>
  );
};

export default ScreenshotButton;
