'use client';

import React, { ReactNode } from 'react';
import dynamic from 'next/dynamic';
import TopNavigation from './TopNavigation';
import { cpColors } from './ui/UnifiedComponents';

// 动态导入轻量化背景组件
const BreathingUniverse = dynamic(() => import('./BreathingUniverse'), {
  ssr: false,
  loading: () => <div style={{ 
    position: 'fixed', 
    inset: 0, 
    backgroundColor: '#000', 
    zIndex: 1 
  }} />
});

interface UnifiedLayoutProps {
  children: ReactNode;
  title?: string;
  showNavigation?: boolean;
  backgroundIntensity?: 'subtle' | 'medium' | 'strong';
  maxWidth?: string;
  centerContent?: boolean;
  className?: string;
}

const UnifiedLayout: React.FC<UnifiedLayoutProps> = ({
  children,
  title,
  showNavigation = true,
  backgroundIntensity = 'medium',
  maxWidth = '1200px',
  centerContent = true,
  className = ''
}) => {
  return (
    <div
      className={className}
      style={{
        position: 'relative',
        minHeight: '100vh',
        width: '100%',
        overflow: 'auto',
        backgroundColor: '#000',
        color: 'rgba(255, 255, 255, 0.9)',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}
    >
      {/* 轻量化呼吸动感背景 */}
      <BreathingUniverse 
        particleCount={500} 
        intensity={backgroundIntensity}
      />
      
      {/* 导航层 */}
      {showNavigation && <TopNavigation />}
      
      {/* 主要内容区域 */}
      <main
        style={{
          position: 'relative',
          zIndex: 10,
          minHeight: '100vh',
          padding: showNavigation ? '80px 20px 20px' : '20px',
          width: '100%'
        }}
      >
        {/* 页面标题 */}
        {title && (
          <div
            style={{
              textAlign: 'center',
              marginBottom: '40px',
              padding: '20px'
            }}
          >
            <h1
              style={{
                fontSize: '32px',
                fontWeight: 'bold',
                background: cpColors.gradient,
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                marginBottom: '8px',
                letterSpacing: '0.05em'
              }}
            >
              {title}
            </h1>
            <div
              style={{
                width: '60px',
                height: '3px',
                background: cpColors.gradient,
                margin: '0 auto',
                borderRadius: '2px'
              }}
            />
          </div>
        )}
        
        {/* 内容容器 */}
        <div
          style={{
            maxWidth: centerContent ? maxWidth : '100%',
            margin: centerContent ? '0 auto' : '0',
            position: 'relative',
            width: '100%'
          }}
        >
          {children}
        </div>
      </main>
    </div>
  );
};

export default UnifiedLayout;