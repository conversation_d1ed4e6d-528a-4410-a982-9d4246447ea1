'use client';

import React from 'react';

// 统一的按钮组件
interface NixianButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const NixianButton: React.FC<NixianButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className = '',
  style = {}
}) => {
  const getButtonStyles = () => {
    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '8px',
      fontWeight: '600',
      transition: 'all 0.2s ease',
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      border: 'none',
      outline: 'none',
      userSelect: 'none' as const,
      opacity: disabled || loading ? 0.6 : 1
    };

    const sizeStyles = {
      sm: { padding: '6px 12px', fontSize: '12px' },
      md: { padding: '8px 16px', fontSize: '14px' },
      lg: { padding: '12px 24px', fontSize: '16px' }
    };

    const variantStyles = {
      primary: {
        background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
        color: '#000000',
        boxShadow: '0 4px 12px rgba(102, 107, 206, 0.3)'
      },
      secondary: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: 'rgba(255, 255, 255, 0.9)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      },
      outline: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.8)',
        border: '1px solid rgba(255, 255, 255, 0.3)'
      },
      ghost: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.7)',
        border: 'none'
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant],
      ...style
    };
  };

  return (
    <button
      style={getButtonStyles()}
      onClick={onClick}
      disabled={disabled || loading}
      className={className}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          if (variant === 'primary') {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 107, 206, 0.4)';
          } else {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'translateY(0)';
          if (variant === 'primary') {
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.3)';
          } else {
            e.currentTarget.style.backgroundColor = variant === 'secondary' 
              ? 'rgba(255, 255, 255, 0.1)' 
              : 'transparent';
          }
        }
      }}
    >
      {loading ? '加载中...' : children}
    </button>
  );
};

// 统一的卡片组件
interface NixianCardProps {
  children: React.ReactNode;
  onClick?: () => void;
  hoverable?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const NixianCard: React.FC<NixianCardProps> = ({
  children,
  onClick,
  hoverable = false,
  className = '',
  style = {}
}) => {
  const cardStyles = {
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    padding: '20px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.3s ease',
    cursor: onClick ? 'pointer' : 'default',
    ...style
  };

  return (
    <div
      style={cardStyles}
      onClick={onClick}
      className={className}
      onMouseEnter={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
          e.currentTarget.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.4)';
        }
      }}
      onMouseLeave={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
          e.currentTarget.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
        }
      }}
    >
      {children}
    </div>
  );
};

// 统一的输入框组件
interface NixianInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'password' | 'email';
  disabled?: boolean;
  className?: string;
}

export const NixianInput: React.FC<NixianInputProps> = ({
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  className = ''
}) => {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// 统一的文本区域组件
interface NixianTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  disabled?: boolean;
  className?: string;
}

export const NixianTextarea: React.FC<NixianTextareaProps> = ({
  value,
  onChange,
  placeholder,
  rows = 4,
  disabled = false,
  className = ''
}) => {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)',
        resize: 'vertical' as const,
        fontFamily: 'inherit'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// Loading 组件
export const NixianLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => {
  const sizeMap = { sm: 20, md: 32, lg: 48 };
  const currentSize = sizeMap[size];
  
  return (
    <div
      style={{
        width: currentSize,
        height: currentSize,
        border: '3px solid rgba(255, 255, 255, 0.1)',
        borderTop: '3px solid #C2A8F2',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  );
};

// 添加全局CSS样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
}
'use client';

import React from 'react';

// 统一的按钮组件
interface NixianButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

export const NixianButton: React.FC<NixianButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className = ''
}) => {
  const getButtonStyles = () => {
    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '8px',
      fontWeight: '600',
      transition: 'all 0.2s ease',
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      border: 'none',
      outline: 'none',
      userSelect: 'none' as const,
      opacity: disabled || loading ? 0.6 : 1
    };

    const sizeStyles = {
      sm: { padding: '6px 12px', fontSize: '12px' },
      md: { padding: '8px 16px', fontSize: '14px' },
      lg: { padding: '12px 24px', fontSize: '16px' }
    };

    const variantStyles = {
      primary: {
        background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
        color: '#000000',
        boxShadow: '0 4px 12px rgba(102, 107, 206, 0.3)'
      },
      secondary: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: 'rgba(255, 255, 255, 0.9)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      },
      outline: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.8)',
        border: '1px solid rgba(255, 255, 255, 0.3)'
      },
      ghost: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.7)',
        border: 'none'
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant]
    };
  };

  return (
    <button
      style={getButtonStyles()}
      onClick={onClick}
      disabled={disabled || loading}
      className={className}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          if (variant === 'primary') {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 107, 206, 0.4)';
          } else {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'translateY(0)';
          if (variant === 'primary') {
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.3)';
          } else {
            e.currentTarget.style.backgroundColor = variant === 'secondary' 
              ? 'rgba(255, 255, 255, 0.1)' 
              : 'transparent';
          }
        }
      }}
    >
      {loading ? '加载中...' : children}
    </button>
  );
};

// 统一的卡片组件
interface NixianCardProps {
  children: React.ReactNode;
  onClick?: () => void;
  hoverable?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const NixianCard: React.FC<NixianCardProps> = ({
  children,
  onClick,
  hoverable = false,
  className = '',
  style = {}
}) => {
  const cardStyles = {
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    padding: '20px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.3s ease',
    cursor: onClick ? 'pointer' : 'default',
    ...style
  };

  return (
    <div
      style={cardStyles}
      onClick={onClick}
      className={className}
      onMouseEnter={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
          e.currentTarget.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.4)';
        }
      }}
      onMouseLeave={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
          e.currentTarget.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
        }
      }}
    >
      {children}
    </div>
  );
};

// 统一的输入框组件
interface NixianInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'password' | 'email';
  disabled?: boolean;
  className?: string;
}

export const NixianInput: React.FC<NixianInputProps> = ({
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  className = ''
}) => {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// 统一的文本区域组件
interface NixianTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  disabled?: boolean;
  className?: string;
}

export const NixianTextarea: React.FC<NixianTextareaProps> = ({
  value,
  onChange,
  placeholder,
  rows = 4,
  disabled = false,
  className = ''
}) => {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)',
        resize: 'vertical' as const,
        fontFamily: 'inherit'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// Loading 组件
export const NixianLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => {
  const sizeMap = { sm: 20, md: 32, lg: 48 };
  const currentSize = sizeMap[size];
  
  return (
    <div
      style={{
        width: currentSize,
        height: currentSize,
        border: '3px solid rgba(255, 255, 255, 0.1)',
        borderTop: '3px solid #C2A8F2',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  );
};

// 添加全局CSS样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `;
  document.head.appendChild(style);
}
import React from 'react';

// 统一的按钮组件
interface NixianButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

export const NixianButton: React.FC<NixianButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className = ''
}) => {
  const getButtonStyles = () => {
    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '8px',
      fontWeight: '600',
      transition: 'all 0.2s ease',
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      border: 'none',
      outline: 'none',
      userSelect: 'none' as const,
      opacity: disabled || loading ? 0.6 : 1
    };

    const sizeStyles = {
      sm: { padding: '6px 12px', fontSize: '12px' },
      md: { padding: '8px 16px', fontSize: '14px' },
      lg: { padding: '12px 24px', fontSize: '16px' }
    };

    const variantStyles = {
      primary: {
        background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
        color: '#000000',
        boxShadow: '0 4px 12px rgba(102, 107, 206, 0.3)'
      },
      secondary: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: 'rgba(255, 255, 255, 0.9)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      },
      outline: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.8)',
        border: '1px solid rgba(255, 255, 255, 0.3)'
      },
      ghost: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.7)',
        border: 'none'
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant]
    };
  };

  return (
    <button
      style={getButtonStyles()}
      onClick={onClick}
      disabled={disabled || loading}
      className={className}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          if (variant === 'primary') {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 107, 206, 0.4)';
          } else {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'translateY(0)';
          if (variant === 'primary') {
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.3)';
          } else {
            e.currentTarget.style.backgroundColor = variant === 'secondary' 
              ? 'rgba(255, 255, 255, 0.1)' 
              : 'transparent';
          }
        }
      }}
    >
      {loading ? '加载中...' : children}
    </button>
  );
};

// 统一的卡片组件
interface NixianCardProps {
  children: React.ReactNode;
  onClick?: () => void;
  hoverable?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const NixianCard: React.FC<NixianCardProps> = ({
  children,
  onClick,
  hoverable = false,
  className = '',
  style = {}
}) => {
  const cardStyles = {
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    padding: '20px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.3s ease',
    cursor: onClick ? 'pointer' : 'default',
    ...style
  };

  return (
    <div
      style={cardStyles}
      onClick={onClick}
      className={className}
      onMouseEnter={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
          e.currentTarget.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.4)';
        }
      }}
      onMouseLeave={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
          e.currentTarget.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
        }
      }}
    >
      {children}
    </div>
  );
};

// 统一的输入框组件
interface NixianInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'password' | 'email';
  disabled?: boolean;
  className?: string;
}

export const NixianInput: React.FC<NixianInputProps> = ({
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  className = ''
}) => {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// 统一的文本区域组件
interface NixianTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  disabled?: boolean;
  className?: string;
}

export const NixianTextarea: React.FC<NixianTextareaProps> = ({
  value,
  onChange,
  placeholder,
  rows = 4,
  disabled = false,
  className = ''
}) => {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)',
        resize: 'vertical' as const,
        fontFamily: 'inherit'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// Loading 组件
export const NixianLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => {
  const sizeMap = { sm: 20, md: 32, lg: 48 };
  const currentSize = sizeMap[size];
  
  return (
    <div
      style={{
        width: currentSize,
        height: currentSize,
        border: '3px solid rgba(255, 255, 255, 0.1)',
        borderTop: '3px solid #C2A8F2',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  );
};'use client';

import React from 'react';

// 统一的按钮组件
interface NixianButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

export const NixianButton: React.FC<NixianButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  className = ''
}) => {
  const getButtonStyles = () => {
    const baseStyles = {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      borderRadius: '8px',
      fontWeight: '600',
      transition: 'all 0.2s ease',
      cursor: disabled || loading ? 'not-allowed' : 'pointer',
      border: 'none',
      outline: 'none',
      userSelect: 'none' as const,
      opacity: disabled || loading ? 0.6 : 1
    };

    const sizeStyles = {
      sm: { padding: '6px 12px', fontSize: '12px' },
      md: { padding: '8px 16px', fontSize: '14px' },
      lg: { padding: '12px 24px', fontSize: '16px' }
    };

    const variantStyles = {
      primary: {
        background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
        color: '#000000',
        boxShadow: '0 4px 12px rgba(102, 107, 206, 0.3)'
      },
      secondary: {
        backgroundColor: 'rgba(255, 255, 255, 0.1)',
        color: 'rgba(255, 255, 255, 0.9)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      },
      outline: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.8)',
        border: '1px solid rgba(255, 255, 255, 0.3)'
      },
      ghost: {
        backgroundColor: 'transparent',
        color: 'rgba(255, 255, 255, 0.7)',
        border: 'none'
      }
    };

    return {
      ...baseStyles,
      ...sizeStyles[size],
      ...variantStyles[variant]
    };
  };

  return (
    <button
      style={getButtonStyles()}
      onClick={onClick}
      disabled={disabled || loading}
      className={className}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          if (variant === 'primary') {
            e.currentTarget.style.transform = 'translateY(-2px)';
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(102, 107, 206, 0.4)';
          } else {
            e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)';
            e.currentTarget.style.transform = 'translateY(-1px)';
          }
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'translateY(0)';
          if (variant === 'primary') {
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.3)';
          } else {
            e.currentTarget.style.backgroundColor = variant === 'secondary' 
              ? 'rgba(255, 255, 255, 0.1)' 
              : 'transparent';
          }
        }
      }}
    >
      {loading ? '加载中...' : children}
    </button>
  );
};

// 统一的卡片组件
interface NixianCardProps {
  children: React.ReactNode;
  onClick?: () => void;
  hoverable?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const NixianCard: React.FC<NixianCardProps> = ({
  children,
  onClick,
  hoverable = false,
  className = '',
  style = {}
}) => {
  const cardStyles = {
    borderRadius: '16px',
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    backdropFilter: 'blur(20px)',
    border: '1px solid rgba(255, 255, 255, 0.1)',
    padding: '20px',
    boxShadow: '0 10px 30px rgba(0, 0, 0, 0.3)',
    transition: 'all 0.3s ease',
    cursor: onClick ? 'pointer' : 'default',
    ...style
  };

  return (
    <div
      style={cardStyles}
      onClick={onClick}
      className={className}
      onMouseEnter={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
          e.currentTarget.style.boxShadow = '0 15px 40px rgba(0, 0, 0, 0.4)';
        }
      }}
      onMouseLeave={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
          e.currentTarget.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.3)';
        }
      }}
    >
      {children}
    </div>
  );
};

// 统一的输入框组件
interface NixianInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  type?: 'text' | 'password' | 'email';
  disabled?: boolean;
  className?: string;
}

export const NixianInput: React.FC<NixianInputProps> = ({
  value,
  onChange,
  placeholder,
  type = 'text',
  disabled = false,
  className = ''
}) => {
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// 统一的文本区域组件
interface NixianTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  disabled?: boolean;
  className?: string;
}

export const NixianTextarea: React.FC<NixianTextareaProps> = ({
  value,
  onChange,
  placeholder,
  rows = 4,
  disabled = false,
  className = ''
}) => {
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      className={className}
      style={{
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        backgroundColor: 'rgba(255, 255, 255, 0.05)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        color: 'rgba(255, 255, 255, 0.9)',
        fontSize: '14px',
        outline: 'none',
        transition: 'all 0.2s ease',
        backdropFilter: 'blur(10px)',
        resize: 'vertical' as const,
        fontFamily: 'inherit'
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = '#C2A8F2';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
        e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }}
    />
  );
};

// Loading 组件
export const NixianLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => {
  const sizeMap = { sm: 20, md: 32, lg: 48 };
  const currentSize = sizeMap[size];
  
  return (
    <div
      style={{
        width: currentSize,
        height: currentSize,
        border: '3px solid rgba(255, 255, 255, 0.1)',
        borderTop: '3px solid #C2A8F2',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  );
};