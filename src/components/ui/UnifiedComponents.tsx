'use client';

import React, { ReactNode, CSSProperties } from 'react';

// CP应援色配置
export const cpColors = {
  primary: '#666BCE',
  secondary: '#C2A8F2', 
  accent: '#FFD64F',
  gradient: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
  gradientReverse: 'linear-gradient(135deg, #FFD64F 0%, #C2A8F2 50%, #666BCE 100%)',
  darkGradient: 'linear-gradient(135deg, #2D2562 0%, #4A3B7A 50%, #6B5B3D 100%)',
  glow: 'rgba(102, 107, 206, 0.3)',
  glowAccent: 'rgba(255, 214, 79, 0.3)',
  glowStrong: 'rgba(102, 107, 206, 0.6)'
};

// 统一的磨砂玻璃样式
export const glassStyle = {
  backgroundColor: 'rgba(0, 0, 0, 0.4)',
  backdropFilter: 'blur(16px)',
  border: '1px solid rgba(255, 255, 255, 0.1)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
};

// 统一按钮组件
interface UnifiedButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'tertiary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  style?: CSSProperties;
  className?: string;
}

export const UnifiedButton: React.FC<UnifiedButtonProps> = ({
  children,
  variant = 'secondary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  style = {},
  className = ''
}) => {
  const variants = {
    primary: {
      background: cpColors.gradient,
      color: '#000',
      boxShadow: `0 0 20px ${cpColors.glow}`,
      fontWeight: 'bold'
    },
    secondary: {
      ...glassStyle,
      color: 'rgba(255, 255, 255, 0.9)',
      border: '1px solid rgba(255, 255, 255, 0.2)'
    },
    tertiary: {
      backgroundColor: 'transparent',
      color: 'rgba(255, 255, 255, 0.7)',
      border: '1px solid rgba(255, 255, 255, 0.3)'
    },
    danger: {
      background: 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)',
      color: '#fff',
      boxShadow: '0 0 20px rgba(255, 71, 87, 0.3)'
    }
  };

  const sizes = {
    small: { padding: '8px 16px', fontSize: '12px' },
    medium: { padding: '12px 24px', fontSize: '14px' },
    large: { padding: '16px 32px', fontSize: '16px' }
  };

  return (
    <button
      onClick={disabled || loading ? undefined : onClick}
      disabled={disabled || loading}
      className={className}
      style={{
        ...variants[variant],
        ...sizes[size],
        borderRadius: '12px',
        cursor: disabled || loading ? 'not-allowed' : 'pointer',
        transition: 'all 0.3s ease',
        position: 'relative',
        overflow: 'hidden',
        opacity: disabled ? 0.5 : 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '8px',
        ...style
      }}
      onMouseEnter={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'scale(1.05)';
          if (variant === 'primary') {
            e.currentTarget.style.boxShadow = `0 0 30px ${cpColors.glowStrong}`;
          }
        }
      }}
      onMouseLeave={(e) => {
        if (!disabled && !loading) {
          e.currentTarget.style.transform = 'scale(1)';
          if (variant === 'primary') {
            e.currentTarget.style.boxShadow = `0 0 20px ${cpColors.glow}`;
          }
        }
      }}
    >
      {loading && (
        <div
          style={{
            width: '14px',
            height: '14px',
            border: '2px solid currentColor',
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}
        />
      )}
      {children}
    </button>
  );
};

// 统一卡片组件
interface UnifiedCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  variant?: 'default' | 'highlight' | 'dark';
  style?: CSSProperties;
  className?: string;
  onClick?: () => void;
  hoverable?: boolean;
}

export const UnifiedCard: React.FC<UnifiedCardProps> = ({
  children,
  title,
  subtitle,
  variant = 'default',
  style = {},
  className = '',
  onClick,
  hoverable = false
}) => {
  const variants = {
    default: {
      ...glassStyle,
    },
    highlight: {
      ...glassStyle,
      border: `1px solid ${cpColors.secondary}`,
      boxShadow: `0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px ${cpColors.glow}`
    },
    dark: {
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
      backdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.05)',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.5)'
    }
  };

  return (
    <div
      onClick={onClick}
      className={className}
      style={{
        ...variants[variant],
        borderRadius: '16px',
        padding: '24px',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease',
        ...style
      }}
      onMouseEnter={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.boxShadow = `0 12px 40px rgba(0, 0, 0, 0.4), 0 0 30px ${cpColors.glow}`;
        }
      }}
      onMouseLeave={(e) => {
        if (hoverable || onClick) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = variants[variant].boxShadow || '0 8px 32px rgba(0, 0, 0, 0.3)';
        }
      }}
    >
      {(title || subtitle) && (
        <div style={{ marginBottom: '16px' }}>
          {title && (
            <h3
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                color: 'rgba(255, 255, 255, 0.95)',
                marginBottom: subtitle ? '4px' : '0',
                background: cpColors.gradient,
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              {title}
            </h3>
          )}
          {subtitle && (
            <p
              style={{
                fontSize: '14px',
                color: 'rgba(255, 255, 255, 0.6)',
                margin: '0'
              }}
            >
              {subtitle}
            </p>
          )}
        </div>
      )}
      <div style={{ color: 'rgba(255, 255, 255, 0.9)' }}>
        {children}
      </div>
    </div>
  );
};

// 统一输入框组件
interface UnifiedInputProps {
  type?: string;
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  maxLength?: number;
  style?: CSSProperties;
  className?: string;
}

export const UnifiedInput: React.FC<UnifiedInputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled = false,
  maxLength,
  style = {},
  className = ''
}) => {
  return (
    <input
      type={type}
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      maxLength={maxLength}
      className={className}
      style={{
        ...glassStyle,
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        fontSize: '14px',
        color: 'rgba(255, 255, 255, 0.9)',
        outline: 'none',
        transition: 'all 0.3s ease',
        ...style
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = cpColors.secondary;
        e.currentTarget.style.boxShadow = `0 0 20px ${cpColors.glow}`;
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
        e.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
      }}
    />
  );
};

// 统一文本域组件
interface UnifiedTextareaProps {
  placeholder?: string;
  value: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  maxLength?: number;
  rows?: number;
  style?: CSSProperties;
  className?: string;
}

export const UnifiedTextarea: React.FC<UnifiedTextareaProps> = ({
  placeholder,
  value,
  onChange,
  disabled = false,
  maxLength,
  rows = 4,
  style = {},
  className = ''
}) => {
  return (
    <textarea
      placeholder={placeholder}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      maxLength={maxLength}
      rows={rows}
      className={className}
      style={{
        ...glassStyle,
        width: '100%',
        padding: '12px 16px',
        borderRadius: '8px',
        fontSize: '14px',
        color: 'rgba(255, 255, 255, 0.9)',
        outline: 'none',
        transition: 'all 0.3s ease',
        resize: 'vertical',
        fontFamily: 'inherit',
        ...style
      }}
      onFocus={(e) => {
        e.currentTarget.style.borderColor = cpColors.secondary;
        e.currentTarget.style.boxShadow = `0 0 20px ${cpColors.glow}`;
      }}
      onBlur={(e) => {
        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
        e.currentTarget.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.3)';
      }}
    />
  );
};

// 统一加载组件
interface UnifiedLoadingProps {
  size?: number;
  color?: string;
  style?: CSSProperties;
}

export const UnifiedLoading: React.FC<UnifiedLoadingProps> = ({
  size = 40,
  color = cpColors.secondary,
  style = {}
}) => {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        ...style
      }}
    >
      <div
        style={{
          width: `${size}px`,
          height: `${size}px`,
          border: `3px solid transparent`,
          borderTop: `3px solid ${color}`,
          borderRadius: '50%',
          animation: 'spin 1s linear infinite'
        }}
      />
    </div>
  );
};

// 统一标签组件
interface UnifiedBadgeProps {
  children: ReactNode;
  variant?: 'default' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'small' | 'medium';
  style?: CSSProperties;
}

export const UnifiedBadge: React.FC<UnifiedBadgeProps> = ({
  children,
  variant = 'default',
  size = 'medium',
  style = {}
}) => {
  const variants = {
    default: { background: cpColors.gradient },
    success: { background: 'linear-gradient(135deg, #2ed573 0%, #1e90ff 100%)' },
    warning: { background: 'linear-gradient(135deg, #ffa502 0%, #ff6348 100%)' },
    danger: { background: 'linear-gradient(135deg, #ff4757 0%, #ff3838 100%)' },
    info: { background: 'linear-gradient(135deg, #3742fa 0%, #2f3542 100%)' }
  };

  const sizes = {
    small: { padding: '4px 8px', fontSize: '10px' },
    medium: { padding: '6px 12px', fontSize: '12px' }
  };

  return (
    <span
      style={{
        ...variants[variant],
        ...sizes[size],
        borderRadius: '20px',
        color: '#fff',
        fontWeight: 'bold',
        display: 'inline-block',
        textAlign: 'center',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.3)',
        ...style
      }}
    >
      {children}
    </span>
  );
};

// 添加CSS动画
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }
  `;
  document.head.appendChild(style);
}