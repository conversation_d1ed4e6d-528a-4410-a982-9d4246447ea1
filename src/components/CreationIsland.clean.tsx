'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { INSPIRATIONS, type InspirationCategoryId } from '@/lib/inspirations';

const categories: { id: InspirationCategoryId; name: string }[] = [
  { id: 'action', name: '高能场面' },
  { id: 'detail', name: '细节补完' },
  { id: 'psych', name: '心理深挖' },
  { id: 'side', name: '配角外传' },
  { id: 'au', name: '平行世界' },
  { id: 'realPerson', name: '真人宇宙' }
];

const CreationIsland: React.FC<{}> = () => {
  const [active, setActive] = useState<InspirationCategoryId | null>(null);
  const [prompt, setPrompt] = useState('');
  const [showInspiration, setShowInspiration] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const router = useRouter();

  const getCurrentCategoryCards = () => {
    if (active === null) return [];
    return INSPIRATIONS[active] || [];
  };

  const handleGenerate = async () => {
    if (!active || !prompt.trim()) {
      alert('请选择创作类别并输入提示词');
      return;
    }
    
    // 设置生成状态
    setIsGenerating(true);
    
    try {
      // 构建与generate页面兼容的参数
      const params = new URLSearchParams({
        prompt: prompt.trim(),
        intent: active as string, // generate页面使用intent参数
        work: categories.find(c => c.id === active)?.name || '',
        tier: 'deep', // 默认使用deep模式
        universe: 'novel' // 默认小说宇宙
      });
      
      // 使用Next.js路由器进行导航
      router.push(`/generate?${params.toString()}`);
    } catch (error) {
      console.error('导航错误:', error);
      // 出错时使用备用方案
      const fallbackParams = new URLSearchParams({
        prompt: prompt.trim(),
        intent: active as string,
        work: categories.find(c => c.id === active)?.name || '',
        tier: 'deep',
        universe: 'novel'
      });
      window.location.href = `/generate?${fallbackParams.toString()}`;
    } finally {
      // 重置生成状态
      setIsGenerating(false);
    }
  };

  return (
    <>
      {/* 主创作面板 */}
      <div 
        className="hide-on-screenshot" 
        style={{
          position: 'fixed',
          bottom: '32px',
          right: '32px',
          zIndex: 40,
          width: '520px',
          maxWidth: '92vw',
          userSelect: 'none'
        }}
      >
        <div 
          style={{
            backdropFilter: 'blur(32px)',
            backgroundColor: 'rgba(0, 0, 0, 0.35)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '24px',
            padding: '20px',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '12px' }}>
            <div 
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              在无数逆转的线里，重逢唯一的你！
            </div>
            <button 
              onClick={() => setCollapsed(!collapsed)}
              style={{
                color: 'rgba(255, 255, 255, 0.6)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '14px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.6)'}
            >
              {collapsed ? '展开' : '收起'}
            </button>
          </div>

          {!collapsed && (
            <>
              <div style={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: '14px', marginBottom: '8px' }}>
                选择一个创作方向
              </div>
              
              {/* 类别选择 */}
              <div 
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '8px',
                  marginBottom: '8px'
                }}
              >
                {categories.map((category) => (
                  <button 
                    key={category.id}
                    onClick={() => setActive(category.id)}
                    style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      border: active === category.id ? '1px solid #C2A8F2' : '1px solid rgba(255, 255, 255, 0.1)',
                      color: active === category.id ? 'white' : 'rgba(255, 255, 255, 0.8)',
                      backgroundColor: active === category.id ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.05)',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      if (active !== category.id) {
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (active !== category.id) {
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                      }
                    }}
                  >
                    {category.name}
                  </button>
                ))}
              </div>

              {/* 标语文字 - 调整为12px字号 */}
              <div 
                style={{
                  marginTop: '4px',
                  marginBottom: '12px',
                  fontSize: '12px',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                每一颗星辰，都是一份回响——而你，点亮了整个宇宙！
              </div>

              {/* 输入区域 */}
              <div 
                style={{
                  transition: 'all 0.3s',
                  opacity: active ? 1 : 0.4,
                  maxHeight: active ? '300px' : '0',
                  overflow: active ? 'visible' : 'hidden'
                }}
              >
                <div 
                  style={{
                    borderRadius: '16px',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    padding: '16px'
                  }}
                >
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="输入你的脑洞，或点击 灵感小助手 获取导演级提示词。描述越具体，生成越精彩。"
                    style={{
                      width: '100%',
                      height: '96px',
                      backgroundColor: 'transparent',
                      color: 'rgba(255, 255, 255, 0.9)',
                      fontSize: '14px',
                      resize: 'none',
                      outline: 'none',
                      border: 'none',
                      fontFamily: 'inherit'
                    }}
                  />
                  
                  <div style={{ marginTop: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <button 
                      onClick={() => setShowInspiration(true)} 
                      disabled={!active || isGenerating}
                      style={{
                        padding: '8px 12px',
                        borderRadius: '8px',
                        fontSize: '14px',
                        border: '1px solid rgba(255, 255, 255, 0.15)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        color: 'rgba(255, 255, 255, 0.85)',
                        cursor: (active && !isGenerating) ? 'pointer' : 'not-allowed',
                        opacity: (active && !isGenerating) ? 1 : 0.5
                      }}
                      onMouseEnter={(e) => {
                        if (active && !isGenerating) {
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (active && !isGenerating) {
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
                        }
                      }}
                    >
                      灵感小助手
                    </button>
                    
                    <div style={{ flex: 1 }} />
                    
                    <button 
                      onClick={handleGenerate} 
                      disabled={!prompt.trim() || isGenerating} 
                      style={{
                        padding: '8px 16px',
                        borderRadius: '8px',
                        fontSize: '14px',
                        fontWeight: '600',
                        border: 'none',
                        background: (prompt.trim() && !isGenerating)
                          ? 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)'
                          : 'rgba(255, 255, 255, 0.05)',
                        color: (prompt.trim() && !isGenerating) ? 'black' : 'rgba(255, 255, 255, 0.5)',
                        cursor: (prompt.trim() && !isGenerating) ? 'pointer' : 'not-allowed',
                        opacity: (prompt.trim() && !isGenerating) ? 1 : 0.5,
                        minWidth: '80px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (prompt.trim() && !isGenerating) {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.4)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (prompt.trim() && !isGenerating) {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }
                      }}
                    >
                      {isGenerating ? '生成中...' : '立即生成'}
                    </button>
                  </div>

                </div>
              </div>
            </>
          )}
          
          {/* 收起状态 */}
          {collapsed && (
            <>
              <div 
                style={{
                  fontSize: '11px',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                每一颗星辰，都是一份回响——而你，点亮了整个宇宙！
              </div>
              <div style={{ marginTop: '4px', fontSize: '12px', color: 'rgba(255, 255, 255, 0.55)' }}>
                已收起创作面板
              </div>
            </>
          )}
        </div>
      </div>

      {/* 灵感弹窗 */}
      {showInspiration && (
        <div 
          style={{
            position: 'fixed',
            inset: '0',
            zIndex: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            backdropFilter: 'blur(4px)'
          }}
        >
          <div 
            style={{
              maxWidth: '672px',
              width: '92%',
              borderRadius: '24px',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              backdropFilter: 'blur(32px)',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.55)',
              maxHeight: '80vh',
              overflowY: 'auto'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: '600', fontSize: '16px' }}>
                灵感小助手
              </div>
              <button 
                onClick={() => setShowInspiration(false)} 
                style={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '20px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
                onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)'}
              >
                ×
              </button>
            </div>
            
            <div 
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '12px'
              }}
            >
              {getCurrentCategoryCards().map((card, index) => (
                <button 
                  key={index}
                  onClick={() => {
                    setPrompt(card.directorPrompt || card.prompt);
                    setShowInspiration(false);
                  }} 
                  style={{
                    textAlign: 'left',
                    borderRadius: '16px',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    color: 'rgba(255, 255, 255, 0.85)',
                    padding: '16px',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.07)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  <div style={{ fontWeight: '600', marginBottom: '8px', fontSize: '14px' }}>
                    {card.title}
                  </div>
                  <div style={{ fontSize: '12px', lineHeight: '1.4', color: 'rgba(255, 255, 255, 0.7)' }}>
                    {card.prompt}
                  </div>
                  {card.directorPrompt && (
                    <div style={{ marginTop: '8px', fontSize: '10px', color: 'rgba(102, 107, 206, 0.8)' }}>
                      💼 含导演级提示词
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CreationIsland;'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { INSPIRATIONS, type InspirationCategoryId } from '@/lib/inspirations';

const categories: { id: InspirationCategoryId; name: string }[] = [
  { id: 'action', name: '高能场面' },
  { id: 'detail', name: '细节补完' },
  { id: 'psych', name: '心理深挖' },
  { id: 'side', name: '配角外传' },
  { id: 'au', name: '平行世界' },
  { id: 'realPerson', name: '真人宇宙' }
];

const CreationIsland: React.FC = () => {
  const [active, setActive] = useState<InspirationCategoryId | null>(null);
  const [prompt, setPrompt] = useState('');
  const [showInspiration, setShowInspiration] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const router = useRouter();

  const getCurrentCategoryCards = () => {
    if (active === null) return [];
    return INSPIRATIONS[active] || [];
  };

  const handleGenerate = async () => {
    if (!active || !prompt.trim()) {
      alert('请选择创作类别并输入提示词');
      return;
    }
    
    // 设置生成状态
    setIsGenerating(true);
    
    try {
      // 构建与generate页面兼容的参数
      const params = new URLSearchParams({
        prompt: prompt.trim(),
        intent: active as string, // generate页面使用intent参数
        work: categories.find(c => c.id === active)?.name || '',
        tier: 'deep', // 默认使用deep模式
        universe: 'novel' // 默认小说宇宙
      });
      
      // 使用Next.js路由器进行导航
      router.push(`/generate?${params.toString()}`);
    } catch (error) {
      console.error('导航错误:', error);
      // 出错时使用备用方案
      const fallbackParams = new URLSearchParams({
        prompt: prompt.trim(),
        intent: active as string,
        work: categories.find(c => c.id === active)?.name || '',
        tier: 'deep',
        universe: 'novel'
      });
      window.location.href = `/generate?${fallbackParams.toString()}`;
    } finally {
      // 重置生成状态
      setIsGenerating(false);
    }
  };

  return (
    <>
      {/* 主创作面板 */}
      <div 
        className="hide-on-screenshot" 
        style={{
          position: 'fixed',
          bottom: '32px',
          right: '32px',
          zIndex: 40,
          width: '520px',
          maxWidth: '92vw',
          userSelect: 'none'
        }}
      >
        <div 
          style={{
            backdropFilter: 'blur(32px)',
            backgroundColor: 'rgba(0, 0, 0, 0.35)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '24px',
            padding: '20px',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '12px' }}>
            <div 
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              在无数逆转的线里，重逢唯一的你！
            </div>
            <button 
              onClick={() => setCollapsed(!collapsed)}
              style={{
                color: 'rgba(255, 255, 255, 0.6)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '14px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.6)'}
            >
              {collapsed ? '展开' : '收起'}
            </button>
          </div>

          {!collapsed && (
            <>
              <div style={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: '14px', marginBottom: '8px' }}>
                选择一个创作方向
              </div>
              
              {/* 类别选择 */}
              <div 
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '8px',
                  marginBottom: '8px'
                }}
              >
                {categories.map((category) => (
                  <button 
                    key={category.id}
                    onClick={() => setActive(category.id)}
                    style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      border: active === category.id ? '1px solid #C2A8F2' : '1px solid rgba(255, 255, 255, 0.1)',
                      color: active === category.id ? 'white' : 'rgba(255, 255, 255, 0.8)',
                      backgroundColor: active === category.id ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.05)',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      if (active !== category.id) {
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (active !== category.id) {
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                      }
                    }}
                  >
                    {category.name}
                  </button>
                ))}
              </div>

              {/* 标语文字 - 调整为12px字号 */}
              <div 
                style={{
                  marginTop: '4px',
                  marginBottom: '12px',
                  fontSize: '12px',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                每一颗星辰，都是一份回响——而你，点亮了整个宇宙！
              </div>

              {/* 输入区域 */}
              <div 
                style={{
                  transition: 'all 0.3s',
                  opacity: active ? 1 : 0.4,
                  maxHeight: active ? '300px' : '0',
                  overflow: active ? 'visible' : 'hidden'
                }}
              >
                <div 
                  style={{
                    borderRadius: '16px',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    padding: '16px'
                  }}
                >
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="输入你的脑洞，或点击 灵感小助手 获取导演级提示词。描述越具体，生成越精彩。"
                    style={{
                      width: '100%',
                      height: '96px',
                      backgroundColor: 'transparent',
                      color: 'rgba(255, 255, 255, 0.9)',
                      fontSize: '14px',
                      resize: 'none',
                      outline: 'none',
                      border: 'none',
                      fontFamily: 'inherit'
                    }}
                  />
                  
                  <div style={{ marginTop: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <button 
                      onClick={() => setShowInspiration(true)} 
                      disabled={!active || isGenerating}
                      style={{
                        padding: '8px 12px',
                        borderRadius: '8px',
                        fontSize: '14px',
                        border: '1px solid rgba(255, 255, 255, 0.15)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        color: 'rgba(255, 255, 255, 0.85)',
                        cursor: (active && !isGenerating) ? 'pointer' : 'not-allowed',
                        opacity: (active && !isGenerating) ? 1 : 0.5
                      }}
                      onMouseEnter={(e) => {
                        if (active && !isGenerating) {
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (active && !isGenerating) {
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
                        }
                      }}
                    >
                      灵感小助手
                    </button>
                    
                    <div style={{ flex: 1 }} />
                    
                    <button 
                      onClick={handleGenerate} 
                      disabled={!prompt.trim() || isGenerating} 
                      style={{
                        padding: '8px 16px',
                        borderRadius: '8px',
                        fontSize: '14px',
                        fontWeight: '600',
                        border: 'none',
                        background: (prompt.trim() && !isGenerating)
                          ? 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)'
                          : 'rgba(255, 255, 255, 0.05)',
                        color: (prompt.trim() && !isGenerating) ? 'black' : 'rgba(255, 255, 255, 0.5)',
                        cursor: (prompt.trim() && !isGenerating) ? 'pointer' : 'not-allowed',
                        opacity: (prompt.trim() && !isGenerating) ? 1 : 0.5,
                        minWidth: '80px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (prompt.trim() && !isGenerating) {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.4)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (prompt.trim() && !isGenerating) {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }
                      }}
                    >
                      {isGenerating ? '生成中...' : '立即生成'}
                    </button>
                  </div>

                </div>
              </div>
            </>
          )}
          
          {/* 收起状态 */}
          {collapsed && (
            <>
              <div 
                style={{
                  fontSize: '11px',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                每一颗星辰，都是一份回响——而你，点亮了整个宇宙！
              </div>
              <div style={{ marginTop: '4px', fontSize: '12px', color: 'rgba(255, 255, 255, 0.55)' }}>
                已收起创作面板
              </div>
            </>
          )}
        </div>
      </div>

      {/* 灵感弹窗 */}
      {showInspiration && (
        <div 
          style={{
            position: 'fixed',
            inset: '0',
            zIndex: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            backdropFilter: 'blur(4px)'
          }}
        >
          <div 
            style={{
              maxWidth: '672px',
              width: '92%',
              borderRadius: '24px',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              backdropFilter: 'blur(32px)',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.55)',
              maxHeight: '80vh',
              overflowY: 'auto'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: '600', fontSize: '16px' }}>
                灵感小助手
              </div>
              <button 
                onClick={() => setShowInspiration(false)} 
                style={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '20px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
                onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)'}
              >
                ×
              </button>
            </div>
            
            <div 
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '12px'
              }}
            >
              {getCurrentCategoryCards().map((card, index) => (
                <button 
                  key={index}
                  onClick={() => {
                    setPrompt(card.directorPrompt || card.prompt);
                    setShowInspiration(false);
                  }} 
                  style={{
                    textAlign: 'left',
                    borderRadius: '16px',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    color: 'rgba(255, 255, 255, 0.85)',
                    padding: '16px',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.07)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  <div style={{ fontWeight: '600', marginBottom: '8px', fontSize: '14px' }}>
                    {card.title}
                  </div>
                  <div style={{ fontSize: '12px', lineHeight: '1.4', color: 'rgba(255, 255, 255, 0.7)' }}>
                    {card.prompt}
                  </div>
                  {card.directorPrompt && (
                    <div style={{ marginTop: '8px', fontSize: '10px', color: 'rgba(102, 107, 206, 0.8)' }}>
                      💼 含导演级提示词
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CreationIsland;'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { INSPIRATIONS, type InspirationCategoryId } from '@/lib/inspirations';

const categories: { id: InspirationCategoryId; name: string }[] = [
  { id: 'action', name: '高能场面' },
  { id: 'detail', name: '细节补完' },
  { id: 'psych', name: '心理深挖' },
  { id: 'side', name: '配角外传' },
  { id: 'au', name: '平行世界' },
  { id: 'realPerson', name: '真人宇宙' }
];

const CreationIsland: React.FC = () => {
  const [active, setActive] = useState<InspirationCategoryId | null>(null);
  const [prompt, setPrompt] = useState('');
  const [showInspiration, setShowInspiration] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const router = useRouter();

  const getCurrentCategoryCards = () => {
    if (active === null) return [];
    return INSPIRATIONS[active] || [];
  };

  const handleGenerate = async () => {
    if (!active || !prompt.trim()) {
      alert('请选择创作类别并输入提示词');
      return;
    }
    
    // 设置生成状态
    setIsGenerating(true);
    
    try {
      // 构建与generate页面兼容的参数
      const params = new URLSearchParams({
        prompt: prompt.trim(),
        intent: active as string, // generate页面使用intent参数
        work: categories.find(c => c.id === active)?.name || '',
        tier: 'deep', // 默认使用deep模式
        universe: 'novel' // 默认小说宇宙
      });
      
      // 使用Next.js路由器进行导航
      router.push(`/generate?${params.toString()}`);
    } catch (error) {
      console.error('导航错误:', error);
      // 出错时使用备用方案
      const fallbackParams = new URLSearchParams({
        prompt: prompt.trim(),
        intent: active as string,
        work: categories.find(c => c.id === active)?.name || '',
        tier: 'deep',
        universe: 'novel'
      });
      window.location.href = `/generate?${fallbackParams.toString()}`;
    } finally {
      // 重置生成状态
      setIsGenerating(false);
    }
  };

  return (
    <>
      {/* 主创作面板 */}
      <div 
        className="hide-on-screenshot" 
        style={{
          position: 'fixed',
          bottom: '32px',
          right: '32px',
          zIndex: 40,
          width: '520px',
          maxWidth: '92vw',
          userSelect: 'none'
        }}
      >
        <div 
          style={{
            backdropFilter: 'blur(32px)',
            backgroundColor: 'rgba(0, 0, 0, 0.35)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            borderRadius: '24px',
            padding: '20px',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.5)'
          }}
        >
          <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '12px' }}>
            <div 
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              在无数逆转的线里，重逢唯一的你！
            </div>
            <button 
              onClick={() => setCollapsed(!collapsed)}
              style={{
                color: 'rgba(255, 255, 255, 0.6)',
                background: 'none',
                border: 'none',
                cursor: 'pointer',
                fontSize: '14px'
              }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.6)'}
            >
              {collapsed ? '展开' : '收起'}
            </button>
          </div>

          {!collapsed && (
            <>
              <div style={{ color: 'rgba(255, 255, 255, 0.85)', fontSize: '14px', marginBottom: '8px' }}>
                选择一个创作方向
              </div>
              
              {/* 类别选择 */}
              <div 
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(3, 1fr)',
                  gap: '8px',
                  marginBottom: '8px'
                }}
              >
                {categories.map((category) => (
                  <button 
                    key={category.id}
                    onClick={() => setActive(category.id)}
                    style={{
                      padding: '6px 12px',
                      borderRadius: '20px',
                      fontSize: '12px',
                      border: active === category.id ? '1px solid #C2A8F2' : '1px solid rgba(255, 255, 255, 0.1)',
                      color: active === category.id ? 'white' : 'rgba(255, 255, 255, 0.8)',
                      backgroundColor: active === category.id ? 'rgba(255, 255, 255, 0.1)' : 'rgba(255, 255, 255, 0.05)',
                      cursor: 'pointer',
                      transition: 'all 0.2s'
                    }}
                    onMouseEnter={(e) => {
                      if (active !== category.id) {
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                      }
                    }}
                    onMouseLeave={(e) => {
                      if (active !== category.id) {
                        e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                      }
                    }}
                  >
                    {category.name}
                  </button>
                ))}
              </div>

              {/* 标语文字 - 调整为12px字号 */}
              <div 
                style={{
                  marginTop: '4px',
                  marginBottom: '12px',
                  fontSize: '12px',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                每一颗星辰，都是一份回响——而你，点亮了整个宇宙！
              </div>

              {/* 输入区域 */}
              <div 
                style={{
                  transition: 'all 0.3s',
                  opacity: active ? 1 : 0.4,
                  maxHeight: active ? '300px' : '0',
                  overflow: active ? 'visible' : 'hidden'
                }}
              >
                <div 
                  style={{
                    borderRadius: '16px',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    padding: '16px'
                  }}
                >
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="输入你的脑洞，或点击 灵感小助手 获取导演级提示词。描述越具体，生成越精彩。"
                    style={{
                      width: '100%',
                      height: '96px',
                      backgroundColor: 'transparent',
                      color: 'rgba(255, 255, 255, 0.9)',
                      fontSize: '14px',
                      resize: 'none',
                      outline: 'none',
                      border: 'none',
                      fontFamily: 'inherit'
                    }}
                  />
                  
                  <div style={{ marginTop: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <button 
                      onClick={() => setShowInspiration(true)} 
                      disabled={!active || isGenerating}
                      style={{
                        padding: '8px 12px',
                        borderRadius: '8px',
                        fontSize: '14px',
                        border: '1px solid rgba(255, 255, 255, 0.15)',
                        backgroundColor: 'rgba(255, 255, 255, 0.05)',
                        color: 'rgba(255, 255, 255, 0.85)',
                        cursor: (active && !isGenerating) ? 'pointer' : 'not-allowed',
                        opacity: (active && !isGenerating) ? 1 : 0.5
                      }}
                      onMouseEnter={(e) => {
                        if (active && !isGenerating) {
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (active && !isGenerating) {
                          e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
                        }
                      }}
                    >
                      灵感小助手
                    </button>
                    
                    <div style={{ flex: 1 }} />
                    
                    <button 
                      onClick={handleGenerate} 
                      disabled={!prompt.trim() || isGenerating} 
                      style={{
                        padding: '8px 16px',
                        borderRadius: '8px',
                        fontSize: '14px',
                        fontWeight: '600',
                        border: 'none',
                        background: (prompt.trim() && !isGenerating)
                          ? 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)'
                          : 'rgba(255, 255, 255, 0.05)',
                        color: (prompt.trim() && !isGenerating) ? 'black' : 'rgba(255, 255, 255, 0.5)',
                        cursor: (prompt.trim() && !isGenerating) ? 'pointer' : 'not-allowed',
                        opacity: (prompt.trim() && !isGenerating) ? 1 : 0.5,
                        minWidth: '80px',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        if (prompt.trim() && !isGenerating) {
                          e.currentTarget.style.transform = 'translateY(-2px)';
                          e.currentTarget.style.boxShadow = '0 4px 12px rgba(102, 107, 206, 0.4)';
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (prompt.trim() && !isGenerating) {
                          e.currentTarget.style.transform = 'translateY(0)';
                          e.currentTarget.style.boxShadow = 'none';
                        }
                      }}
                    >
                      {isGenerating ? '生成中...' : '立即生成'}
                    </button>
                  </div>

                </div>
              </div>
            </>
          )}
          
          {/* 收起状态 */}
          {collapsed && (
            <>
              <div 
                style={{
                  fontSize: '11px',
                  fontWeight: '600',
                  background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                  WebkitBackgroundClip: 'text',
                  backgroundClip: 'text',
                  WebkitTextFillColor: 'transparent'
                }}
              >
                每一颗星辰，都是一份回响——而你，点亮了整个宇宙！
              </div>
              <div style={{ marginTop: '4px', fontSize: '12px', color: 'rgba(255, 255, 255, 0.55)' }}>
                已收起创作面板
              </div>
            </>
          )}
        </div>
      </div>

      {/* 灵感弹窗 */}
      {showInspiration && (
        <div 
          style={{
            position: 'fixed',
            inset: '0',
            zIndex: 50,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.6)',
            backdropFilter: 'blur(4px)'
          }}
        >
          <div 
            style={{
              maxWidth: '672px',
              width: '92%',
              borderRadius: '24px',
              border: '1px solid rgba(255, 255, 255, 0.1)',
              backgroundColor: 'rgba(0, 0, 0, 0.35)',
              backdropFilter: 'blur(32px)',
              padding: '24px',
              boxShadow: '0 20px 60px rgba(0, 0, 0, 0.55)',
              maxHeight: '80vh',
              overflowY: 'auto'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: '16px' }}>
              <div style={{ color: 'rgba(255, 255, 255, 0.9)', fontWeight: '600', fontSize: '16px' }}>
                灵感小助手
              </div>
              <button 
                onClick={() => setShowInspiration(false)} 
                style={{
                  color: 'rgba(255, 255, 255, 0.7)',
                  background: 'none',
                  border: 'none',
                  cursor: 'pointer',
                  fontSize: '20px',
                  width: '32px',
                  height: '32px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
                onMouseEnter={(e) => e.currentTarget.style.color = 'white'}
                onMouseLeave={(e) => e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)'}
              >
                ×
              </button>
            </div>
            
            <div 
              style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '12px'
              }}
            >
              {getCurrentCategoryCards().map((card, index) => (
                <button 
                  key={index}
                  onClick={() => {
                    setPrompt(card.directorPrompt || card.prompt);
                    setShowInspiration(false);
                  }} 
                  style={{
                    textAlign: 'left',
                    borderRadius: '16px',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    backgroundColor: 'rgba(255, 255, 255, 0.05)',
                    color: 'rgba(255, 255, 255, 0.85)',
                    padding: '16px',
                    cursor: 'pointer',
                    transition: 'all 0.2s'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.07)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                  }}
                >
                  <div style={{ fontWeight: '600', marginBottom: '8px', fontSize: '14px' }}>
                    {card.title}
                  </div>
                  <div style={{ fontSize: '12px', lineHeight: '1.4', color: 'rgba(255, 255, 255, 0.7)' }}>
                    {card.prompt}
                  </div>
                  {card.directorPrompt && (
                    <div style={{ marginTop: '8px', fontSize: '10px', color: 'rgba(102, 107, 206, 0.8)' }}>
                      💼 含导演级提示词
                    </div>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CreationIsland;