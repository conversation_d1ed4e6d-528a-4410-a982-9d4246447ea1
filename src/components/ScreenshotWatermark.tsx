'use client';

import React from 'react';

const ScreenshotWatermark: React.FC = () => {
  return (
    <div 
      id="screenshot-watermark" 
      style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 50,
        userSelect: 'none',
        display: 'none' // 默认隐藏
      }}
    >
      <div 
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '12px',
          padding: '8px 12px',
          backgroundColor: 'transparent'
        }}
      >
        <img 
          src="/logo-final.svg" 
          alt="逆线" 
          style={{
            width: '24px',
            height: '24px'
          }}
        />
        <div 
          style={{
            fontSize: '14px',
            lineHeight: 1.2,
            backgroundColor: 'transparent'
          }}
        >
          {/* 使用SVG文字实现渐变效果，确保html2canvas兼容 */}
          <svg width="40" height="18" style={{ display: 'block' }}>
            <defs>
              <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#666BCE" />
                <stop offset="50%" stopColor="#C2A8F2" />
                <stop offset="100%" stopColor="#FFD64F" />
              </linearGradient>
            </defs>
            <text x="0" y="14" fill="url(#textGradient)" fontSize="14" fontWeight="bold">
              逆线
            </text>
          </svg>
          
          <svg width="120" height="14" style={{ display: 'block', marginTop: '2px' }}>
            <defs>
              <linearGradient id="urlGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stopColor="#666BCE" />
                <stop offset="50%" stopColor="#C2A8F2" />
                <stop offset="100%" stopColor="#FFD64F" />
              </linearGradient>
            </defs>
            <text x="0" y="11" fill="url(#urlGradient)" fontSize="11">
              https://nixian.top
            </text>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default ScreenshotWatermark;


