'use client';

import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { Vector3, Color, BufferGeometry, BufferAttribute } from 'three';
import * as THREE from 'three';

interface ParticleSystemProps {
  count: number;
}

const ParticleSystem: React.FC<ParticleSystemProps> = ({ count }) => {
  const pointsRef = useRef<THREE.Points>(null);
  
  const geometry = useMemo(() => {
    const geom = new BufferGeometry();
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    const sizes = new Float32Array(count);

    const phantomPurple = new Color('#666BCE');
    const amethyst = new Color('#C2A8F2'); 
    const lemonYellow = new Color('#FFD64F');
    
    for (let i = 0; i < count; i++) {
      const x = (Math.random() - 0.5) * 3000;
      const y = (Math.random() - 0.5) * 3000;
      const z = (Math.random() - 0.5) * 3000;
      
      positions[i * 3] = x;
      positions[i * 3 + 1] = y; 
      positions[i * 3 + 2] = z;
      
      const noiseX = Math.sin(x * 0.001) * Math.cos(y * 0.001);
      const noiseY = Math.cos(y * 0.001) * Math.sin(z * 0.001);
      const noiseZ = Math.sin(z * 0.001) * Math.cos(x * 0.001);
      const combined = (noiseX + noiseY + noiseZ) / 3;
      
      let color: Color;
      if (combined > 0.3) {
        color = lemonYellow.clone();
        sizes[i] = Math.random() * 8 + 5;
      } else if (combined > 0.0) {
        const t = combined / 0.3;
        color = amethyst.clone().lerp(lemonYellow, t);
        sizes[i] = Math.random() * 6 + 3.5;
      } else if (combined > -0.3) {
        const t = (combined + 0.3) / 0.3;
        color = phantomPurple.clone().lerp(amethyst, t);
        sizes[i] = Math.random() * 4 + 2.5;
      } else {
        color = phantomPurple.clone();
        sizes[i] = Math.random() * 3 + 1.5;
      }
      
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
    }
    
    geom.setAttribute('position', new BufferAttribute(positions, 3));
    geom.setAttribute('color', new BufferAttribute(colors, 3));
    geom.setAttribute('size', new BufferAttribute(sizes, 1));
    
    return geom;
  }, [count]);
  
  useFrame((state, delta) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.x += delta * 0.05;
      pointsRef.current.rotation.y += delta * 0.03;
    }
  });
  
  return (
    <points ref={pointsRef} geometry={geometry}>
      <pointsMaterial 
        size={4.6}
        vertexColors={true}
        transparent={true}
        blending={THREE.AdditiveBlending}
        sizeAttenuation={true}
        depthWrite={false}
        opacity={0.95}
      />
    </points>
  );
};

const CameraController: React.FC = () => {
  const { camera } = useThree();
  const velocity = useRef(new Vector3());
  const keys = useRef<{ [key: string]: boolean }>({});
  
  const handleKeyDown = (event: KeyboardEvent) => { 
    keys.current[event.code] = true; 
  };
  
  const handleKeyUp = (event: KeyboardEvent) => { 
    keys.current[event.code] = false; 
  };
  
  React.useEffect(() => { 
    window.addEventListener('keydown', handleKeyDown); 
    window.addEventListener('keyup', handleKeyUp); 
    return () => { 
      window.removeEventListener('keydown', handleKeyDown); 
      window.removeEventListener('keyup', handleKeyUp); 
    }; 
  }, []);
  
  useFrame((state, delta) => {
    const speed = 100 * delta; 
    const damp = 0.85;
    
    if (keys.current['KeyW'] || keys.current['ArrowUp']) velocity.current.z -= speed;
    if (keys.current['KeyS'] || keys.current['ArrowDown']) velocity.current.z += speed;
    if (keys.current['KeyA'] || keys.current['ArrowLeft']) velocity.current.x -= speed;
    if (keys.current['KeyD'] || keys.current['ArrowRight']) velocity.current.x += speed;
    if (keys.current['Space']) velocity.current.y += speed;
    if (keys.current['ShiftLeft'] || keys.current['ControlLeft']) velocity.current.y -= speed;
    
    const forward = new Vector3(0, 0, -1).applyQuaternion(camera.quaternion);
    const right = new Vector3(1, 0, 0).applyQuaternion(camera.quaternion);
    const up = new Vector3(0, 1, 0);
    
    camera.position.add(forward.clone().multiplyScalar(velocity.current.z));
    camera.position.add(right.clone().multiplyScalar(velocity.current.x));
    camera.position.add(up.clone().multiplyScalar(velocity.current.y));
    
    velocity.current.multiplyScalar(damp);
  });
  
  return null;
};

const MouseLook: React.FC = () => {
  const { camera } = useThree();
  const isLocked = useRef(false);
  
  React.useEffect(() => {
    const handleMouseMove = (event: MouseEvent) => { 
      if (!isLocked.current) return; 
      const s = 0.002; 
      camera.rotation.y -= event.movementX * s; 
      camera.rotation.x -= event.movementY * s; 
      camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, camera.rotation.x)); 
    };
    
    const handleClick = (event: MouseEvent) => { 
      if (event.target instanceof HTMLElement && event.target.tagName === 'CANVAS') {
        if (document.pointerLockElement) { 
          isLocked.current = true; 
        } else { 
          document.body.requestPointerLock(); 
          isLocked.current = true; 
        } 
      }
    };
    
    const handlePtr = () => { 
      isLocked.current = !!document.pointerLockElement; 
    };
    
    document.addEventListener('mousemove', handleMouseMove); 
    document.addEventListener('click', handleClick); 
    document.addEventListener('pointerlockchange', handlePtr);
    document.addEventListener('mozpointerlockchange', handlePtr);
    document.addEventListener('webkitpointerlockchange', handlePtr);
    
    return () => { 
      document.removeEventListener('mousemove', handleMouseMove); 
      document.removeEventListener('click', handleClick); 
      document.removeEventListener('pointerlockchange', handlePtr);
      document.removeEventListener('mozpointerlockchange', handlePtr);
      document.removeEventListener('webkitpointerlockchange', handlePtr);
    };
  }, [camera]);
  
  return null;
};

interface UniverseProps { 
  count?: number;
}

const SimpleParticleUniverse: React.FC<UniverseProps> = ({ count = 10000 }) => {
  return (
    <div 
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100vh',
        background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
        overflow: 'hidden',
        zIndex: 0
      }}
    >
      <Canvas
        camera={{ position: [0, 0, 900], fov: 65, near: 1, far: 5000 }}
        gl={{ 
          antialias: false,
          alpha: false, 
          powerPreference: 'high-performance'
        }}
      >
        <fog attach="fog" args={['#0a0a1a', 500, 2000]} />
        <ParticleSystem count={count} />
        <CameraController />
        <MouseLook />
      </Canvas>
    </div>
  );
};

export default SimpleParticleUniverse;