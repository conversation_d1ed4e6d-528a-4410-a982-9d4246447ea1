'use client';

import React, { useRef, useMemo, useEffect, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { BufferGeometry, BufferAttribute, Color } from 'three';
import * as THREE from 'three';

// 超轻量级粒子系统
function FastParticles({ count = 5000 }: { count?: number }) {
  const pointsRef = useRef<THREE.Points>(null);
  
  const geometry = useMemo(() => {
    console.log('🚀 生成高性能粒子系统');
    
    const geom = new BufferGeometry();
    const positions = new Float32Array(count * 3);
    const colors = new Float32Array(count * 3);
    
    // 你的CP应援色
    const color1 = new Color('#666BCE'); // 紫色
    const color2 = new Color('#C2A8F2'); // 紫晶色  
    const color3 = new Color('#FFD64F'); // 柠檬黄
    
    for (let i = 0; i < count; i++) {
      // 随机位置
      positions[i * 3] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 2000;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 2000;
      
      // 简单的颜色分配
      let color: Color;
      const rand = Math.random();
      if (rand < 0.33) {
        color = color1;
      } else if (rand < 0.66) {
        color = color2;
      } else {
        color = color3;
      }
      
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
    }
    
    geom.setAttribute('position', new BufferAttribute(positions, 3));
    geom.setAttribute('color', new BufferAttribute(colors, 3));
    
    return geom;
  }, [count]);
  
  useFrame((state, delta) => {
    if (pointsRef.current) {
      pointsRef.current.rotation.x += delta * 0.02;
      pointsRef.current.rotation.y += delta * 0.01;
    }
  });
  
  return (
    <points ref={pointsRef} geometry={geometry}>
      <pointsMaterial 
        size={3}
        vertexColors={true}
        transparent={true}
        opacity={0.8}
      />
    </points>
  );
}

// 高性能3D粒子宇宙
export default function FastParticleUniverse({ count = 5000 }: { count?: number }) {
  const [isClient, setIsClient] = useState(false);
  
  useEffect(() => {
    setIsClient(true);
  }, []);
  
  if (!isClient) {
    return (
      <div style={{
        position: 'absolute',
        top: 0, left: 0, right: 0, bottom: 0,
        background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: 'white'
      }}>
        <p>正在启动逆线宇宙...</p>
      </div>
    );
  }
  
  return (
    <div style={{
      position: 'absolute',
      top: 0, left: 0, right: 0, bottom: 0,
      width: '100%', height: '100vh',
      background: 'linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)',
      overflow: 'hidden', zIndex: 0
    }}>
      {/* 调试信息 */}
      <div style={{
        position: 'absolute', top: '10px', left: '10px', zIndex: 1000,
        color: 'white', background: 'rgba(0,0,0,0.7)',
        padding: '10px', borderRadius: '5px', fontSize: '12px'
      }}>
        <div>🌌 高性能Three.js粒子宇宙</div>
        <div>粒子数量: {count}</div>
        <div>颜色: 紫色 + 紫晶色 + 柠檬黄</div>
      </div>
      
      <Canvas
        camera={{ position: [0, 0, 500], fov: 60 }}
        gl={{ 
          antialias: false,
          alpha: false,
          powerPreference: 'high-performance',
        }}
        dpr={1} // 固定像素比
        performance={{ min: 0.1 }} // 降低性能要求
      >
        <FastParticles count={count} />
      </Canvas>
    </div>
  );
}