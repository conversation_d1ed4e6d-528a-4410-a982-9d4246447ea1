'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';

const TopNavigation: React.FC = () => {
  const [isVisible, setIsVisible] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => setIsVisible(e.clientY < 80);
    document.addEventListener('mousemove', handleMouseMove);
    
    // 检查登录状态
    checkLoginStatus();
    
    // 同步用户信息
    if (localStorage.getItem('user')) {
      syncUserInfo();
    }
    
    return () => document.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const checkLoginStatus = () => {
    // 这里可以检查localStorage或cookie中的登录状态
    const userData = localStorage.getItem('user');
    if (userData) {
      setIsLoggedIn(true);
      setUser(JSON.parse(userData));
    } else {
      setIsLoggedIn(false);
      setUser(null);
    }
  };

  // 同步用户信息
  const syncUserInfo = async () => {
    try {
      const current = JSON.parse(localStorage.getItem('user') || 'null');
      if (current?.id) {
        const response = await fetch('/api/me', { 
          headers: { 'x-user-id': current.id } 
        });
        const data = await response.json();
        if (data?.ok) {
          localStorage.setItem('user', JSON.stringify(data.user));
          setUser(data.user);
        }
      }
    } catch (error) {
      console.error('同步用户信息失败:', error);
    }
  };

  const handleLogout = async () => {
    setIsLoading(true);
    try {
      // 清除本地存储的用户数据
      localStorage.removeItem('user');
      localStorage.removeItem('token');
      setIsLoggedIn(false);
      setUser(null);
      
      // 可以调用登出API
      // await fetch('/api/auth/logout', { method: 'POST' });
      
      alert('已退出登录');
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = () => {
    // 导航到登录页面或打开登录弹窗
    router.push('/auth');
  };

  const handleDailySignin = async () => {
    if (!user?.id) return;
    
    try {
      const response = await fetch('/api/auth/daily-signin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId: user.id }),
      });
      
      const data = await response.json();
      if (data.ok) {
        alert(data.message);
        // 更新用户信息
        const updatedUser = { ...user, ...data };
        localStorage.setItem('user', JSON.stringify(updatedUser));
        setUser(updatedUser);
      } else {
        alert(data.error || '签到失败');
      }
    } catch (error) {
      console.error('签到失败:', error);
      alert('签到失败，请稍后重试');
    }
  };

  const primaryNav = [
    { 
      label: '公告', 
      action: () => router.push('/notice'),
      description: '查看最新公告和系统通知'
    },
    { 
      label: '光影回廊', 
      action: () => router.push('/gallery'),
      description: '浏览精美作品画廊'
    },
    { 
      label: '我的头衔', 
      action: () => router.push('/titles'),
      description: '查看和管理个人头衔（回响档案馆）'
    },
    { 
      label: '实时弹幕', 
      action: () => router.push('/forum'),
      description: '参与实时讨论和弹幕互动'
    },
    { 
      label: '创作社区', 
      action: () => router.push('/creative-space'),
      description: '加入创作社区（原交错时空）'
    }, 
  ];

  const secondaryNav = [
    { 
      label: '为逆线充电', 
      action: () => router.push('/recharge'),
      description: '灵感充值和守护者套餐'
    },
    { 
      label: isLoggedIn ? '退出' : '登录', 
      action: isLoggedIn ? handleLogout : handleLogin,
      description: isLoggedIn ? '退出当前账户' : '登录或注册账户'
    },
  ];

  return (
    <div 
      className="hide-on-screenshot"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 40,
        transition: 'all 0.5s ease',
        opacity: isVisible ? 1 : 0,
        transform: isVisible ? 'translateY(0)' : 'translateY(-16px)'
      }}
    >
      <div 
        style={{
          margin: '12px 16px 0',
          borderRadius: '16px',
          backdropFilter: 'blur(16px)',
          backgroundColor: 'rgba(0, 0, 0, 0.35)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          boxShadow: '0 10px 40px rgba(0, 0, 0, 0.35)'
        }}
      >
        <div 
          style={{
            padding: '12px 24px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          {/* 左侧品牌区域 */}
          <div 
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              cursor: 'pointer'
            }}
            onClick={() => router.push('/')}
          >
            <img 
              src="/logo-final.svg" 
              alt="逆线" 
              style={{
                width: '32px',
                height: '32px'
              }}
            />
            <div 
              style={{
                fontSize: '18px',
                fontWeight: 'bold',
                letterSpacing: '0.05em',
                background: 'linear-gradient(135deg, #666BCE 0%, #C2A8F2 50%, #FFD64F 100%)',
                WebkitBackgroundClip: 'text',
                backgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              逆线
            </div>
          </div>

          {/* 中间主导航 */}
          <nav style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {primaryNav.map((item) => (
              <button 
                key={item.label} 
                onClick={item.action}
                title={item.description}
                disabled={isLoading}
                style={{
                  padding: '8px 12px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.85)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  opacity: isLoading ? 0.6 : 1
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.color = 'rgba(255, 255, 255, 1)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.color = 'rgba(255, 255, 255, 0.85)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                  }
                }}
              >
                {item.label}
              </button>
            ))}
          </nav>

          {/* 右侧用户操作 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {/* 登录状态指示 */}
            {isLoggedIn && user && (
              <div 
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px'
                }}
              >
                <span 
                  style={{
                    fontSize: '12px',
                    color: 'rgba(255, 255, 255, 0.7)',
                    padding: '4px 8px',
                    borderRadius: '6px',
                    backgroundColor: 'rgba(102, 107, 206, 0.2)',
                    border: '1px solid rgba(102, 107, 206, 0.3)'
                  }}
                >
                  <span style={{ color: '#4ade80', marginRight: '4px' }}>⚡</span>
                  可用灵感: {user.credits || 0}
                </span>
                
                <button
                  onClick={handleDailySignin}
                  style={{
                    padding: '4px 8px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    color: 'rgba(255, 255, 255, 0.85)',
                    border: '1px solid rgba(255, 214, 79, 0.3)',
                    backgroundColor: 'rgba(255, 214, 79, 0.1)',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 214, 79, 0.2)';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(255, 214, 79, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  📅 签到
                </button>
              </div>
            )}
            
            {secondaryNav.map((item) => (
              <button 
                key={item.label} 
                onClick={item.action}
                title={item.description}
                disabled={isLoading}
                style={{
                  padding: '8px 12px',
                  borderRadius: '8px',
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.85)',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  backgroundColor: 'rgba(255, 255, 255, 0.05)',
                  cursor: isLoading ? 'not-allowed' : 'pointer',
                  transition: 'all 0.2s ease',
                  opacity: isLoading ? 0.6 : 1,
                  fontWeight: 'normal'
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.color = 'rgba(255, 255, 255, 1)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(-1px)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.color = 'rgba(255, 255, 255, 0.85)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }
                }}
              >
                {isLoading && item.label === '退出' ? '退出中...' : item.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopNavigation;