// 独立的3D星空组件 - 完全不依赖React版本
export class Starfield {
  constructor(container, options = {}) {
    this.container = container;
    this.count = options.count || 50000;
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.particles = null;
    this.animationId = null;
    this.keys = {};
    this.velocity = { x: 0, y: 0, z: 0 };
    this.isPointerLocked = false;

    this.init();
  }

  async init() {
    try {
      // 动态导入Three.js避免版本冲突
      const THREE = await import('three');

      // 创建场景
      this.scene = new THREE.Scene();
      this.scene.fog = new THREE.Fog('#0a0a1a', 500, 2000);

      // 创建相机
      this.camera = new THREE.PerspectiveCamera(
        65,
        window.innerWidth / window.innerHeight,
        1,
        5000
      );
      this.camera.position.set(0, 0, 900);

      // 创建渲染器
      this.renderer = new THREE.WebGLRenderer({
        antialias: true,
        alpha: false
      });
      this.renderer.setSize(window.innerWidth, window.innerHeight);
      this.renderer.setClearColor('#0a0a1a');
      this.renderer.setPixelRatio(window.devicePixelRatio);
      this.renderer.powerPreference = 'high-performance';
      this.container.appendChild(this.renderer.domElement);

      // 创建粒子系统
      this.createParticles(THREE);

      // 初始化控制
      this.initControls();

      // 开始动画
      this.animate();

    } catch (error) {
      console.error('Failed to initialize Starfield:', error);
      this.showFallback();
    }
  }

  createParticles(THREE) {
    const geometry = new THREE.BufferGeometry();
    const positions = new Float32Array(this.count * 3);
    const colors = new Float32Array(this.count * 3);
    const sizes = new Float32Array(this.count);

    const phantomPurple = new THREE.Color('#666BCE');
    const amethyst = new THREE.Color('#C2A8F2');
    const lemonYellow = new THREE.Color('#FFD64F');

    for (let i = 0; i < this.count; i++) {
      const x = (Math.random() - 0.5) * 3000;
      const y = (Math.random() - 0.5) * 3000;
      const z = (Math.random() - 0.5) * 3000;

      positions[i * 3] = x;
      positions[i * 3 + 1] = y;
      positions[i * 3 + 2] = z;

      const noiseX = Math.sin(x * 0.001) * Math.cos(y * 0.001);
      const noiseY = Math.cos(y * 0.001) * Math.sin(z * 0.001);
      const noiseZ = Math.sin(z * 0.001) * Math.cos(x * 0.001);
      const combined = (noiseX + noiseY + noiseZ) / 3;

      let color;
      if (combined > 0.3) {
        color = lemonYellow.clone();
        sizes[i] = Math.random() * 8 + 5;
      } else if (combined > 0.0) {
        const t = combined / 0.3;
        color = amethyst.clone().lerp(lemonYellow, t);
        sizes[i] = Math.random() * 6 + 3.5;
      } else if (combined > -0.3) {
        const t = (combined + 0.3) / 0.3;
        color = phantomPurple.clone().lerp(amethyst, t);
        sizes[i] = Math.random() * 4 + 2.5;
      } else {
        color = phantomPurple.clone();
        sizes[i] = Math.random() * 3 + 1.5;
      }

      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
    }

    geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    geometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));

    const material = new THREE.PointsMaterial({
      size: 4.6,
      vertexColors: true,
      transparent: true,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true,
      depthWrite: false,
      opacity: 0.95
    });

    this.particles = new THREE.Points(geometry, material);
    this.scene.add(this.particles);
  }

  initControls() {
    // 键盘控制
    document.addEventListener('keydown', (e) => {
      this.keys[e.code] = true;
    });

    document.addEventListener('keyup', (e) => {
      this.keys[e.code] = false;
    });

    // 鼠标控制
    document.addEventListener('click', () => {
      if (document.pointerLockElement) {
        this.isPointerLocked = true;
      } else {
        document.body.requestPointerLock();
        this.isPointerLocked = true;
      }
    });

    document.addEventListener('pointerlockchange', () => {
      this.isPointerLocked = !!document.pointerLockElement;
    });

    document.addEventListener('mousemove', (e) => {
      if (!this.isPointerLocked || !this.camera) return;

      const sensitivity = 0.002;
      this.camera.rotation.y -= e.movementX * sensitivity;
      this.camera.rotation.x -= e.movementY * sensitivity;
      this.camera.rotation.x = Math.max(-Math.PI / 2, Math.min(Math.PI / 2, this.camera.rotation.x));
    });
  }

  animate() {
    this.animationId = requestAnimationFrame(() => this.animate());

    if (!this.camera || !this.renderer || !this.scene) return;

    // 更新相机位置
    const delta = 1/60;
    const speed = 100 * delta;
    const damp = 0.8;

    if (this.keys['KeyW']) this.velocity.z -= speed;
    if (this.keys['KeyS']) this.velocity.z += speed;
    if (this.keys['KeyA']) this.velocity.x -= speed;
    if (this.keys['KeyD']) this.velocity.x += speed;
    if (this.keys['Space']) this.velocity.y += speed;
    if (this.keys['ShiftLeft']) this.velocity.y -= speed;

    // 应用移动
    import('three').then(({ Vector3 }) => {
      const forward = new Vector3(0, 0, -1).applyQuaternion(this.camera.quaternion);
      const right = new Vector3(1, 0, 0).applyQuaternion(this.camera.quaternion);
      const up = new Vector3(0, 1, 0);

      this.camera.position.add(forward.multiplyScalar(this.velocity.z));
      this.camera.position.add(right.multiplyScalar(this.velocity.x));
      this.camera.position.add(up.multiplyScalar(this.velocity.y));

      this.velocity.x *= damp;
      this.velocity.y *= damp;
      this.velocity.z *= damp;

      this.renderer.render(this.scene, this.camera);
    });
  }

  showFallback() {
    // 显示CSS动画的fallback
    this.container.innerHTML = `
      <div class="absolute inset-0" style="background: linear-gradient(135deg, #0a0a1a 0%, #1a0a2a 50%, #0a0a1a 100%)">
        <div class="absolute inset-0">
          ${Array.from({ length: 100 }).map(() => `
            <div
              class="absolute rounded-full opacity-70 animate-pulse"
              style="
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                width: ${Math.random() * 4 + 2}px;
                height: ${Math.random() * 4 + 2}px;
                background-color: ${['#666BCE', '#C2A8F2', '#FFD64F'][Math.floor(Math.random() * 3)]};
                animation-delay: ${Math.random() * 3}s;
                animation-duration: ${Math.random() * 2 + 1}s;
              "
            ></div>
          `).join('')}
        </div>
        <div class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="text-white/80 text-lg mb-2">3D逆线宇宙背景</div>
            <div class="text-white/60 text-sm">正在加载3D系统...</div>
          </div>
        </div>
      </div>
    `;
  }

  resize() {
    if (this.camera && this.renderer) {
      this.camera.aspect = window.innerWidth / window.innerHeight;
      this.camera.updateProjectionMatrix();
      this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
  }

  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    // 清理事件监听器
    document.removeEventListener('keydown', this.keys);
    document.removeEventListener('keyup', this.keys);
    document.removeEventListener('click', () => {});
    document.removeEventListener('pointerlockchange', () => {});
    document.removeEventListener('mousemove', () => {});

    if (this.renderer && this.container) {
      this.container.removeChild(this.renderer.domElement);
      this.renderer.dispose();
    }
  }
}

// 默认导出
export default Starfield;
