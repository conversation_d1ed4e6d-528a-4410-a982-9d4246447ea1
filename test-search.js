const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://bhbzfbryaehofeafplcu.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoYnpmYnJ5YWVob2ZlYWZwbGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTA4MTM0NSwiZXhwIjoyMDcwNjU3MzQ1fQ.tnwhVuvkxRIBM2epa8X815smk14ySjceK1BOL1jM4V8';

console.log('Testing search functionality...');
console.log('Supabase URL:', SUPABASE_URL);

try {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  console.log('Supabase client created successfully');
  
  // 测试全文搜索功能（使用默认配置）
  const query = '池骋';
  console.log(`Searching for: ${query}`);
  
  supabase
    .from('book_chunks')
    .select('id, work_title, chapter_no, chapter_title, chunk_text')
    .eq('work_title', '逆袭')
    .textSearch('fts', query, {
      type: 'websearch'
      // 移除config参数，使用默认配置
    })
    .limit(5)
    .then(({ data, error }) => {
      if (error) {
        console.error('Error searching:', error);
      } else {
        console.log(`Found ${data.length} results:`);
        data.forEach((row, index) => {
          console.log(`Result ${index + 1}:`, {
            id: row.id,
            chapter_no: row.chapter_no,
            chapter_title: row.chapter_title,
            chunk_text_preview: row.chunk_text.substring(0, 150) + '...'
          });
        });
      }
    });
} catch (err) {
  console.error('Failed to create Supabase client:', err);
}