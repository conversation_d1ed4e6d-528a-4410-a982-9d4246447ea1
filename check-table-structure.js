const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://bhbzfbryaehofeafplcu.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoYnpmYnJ5YWVob2ZlYWZwbGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTA4MTM0NSwiZXhwIjoyMDcwNjU3MzQ1fQ.tnwhVuvkxRIBM2epa8X815smk14ySjceK1BOL1jM4V8';

console.log('Checking book_chunks table structure...');
console.log('URL:', SUPABASE_URL);

try {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  console.log('Supabase client created successfully');
  
  // 查询book_chunks表的列信息
  supabase
    .from('book_chunks')
    .select('*')
    .limit(1)
    .then(({ data, error }) => {
      if (error) {
        console.error('Error querying book_chunks table:', error);
      } else {
        console.log('book_chunks table structure:');
        if (data && data.length > 0) {
          const columns = Object.keys(data[0]);
          columns.forEach(column => {
            console.log('- ' + column);
          });
        } else {
          console.log('No data found in book_chunks table');
        }
      }
    });
} catch (err) {
  console.error('Failed to create Supabase client:', err);
}