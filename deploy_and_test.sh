#!/bin/bash
set -e

# 1) 调整超时时间并替换失败分支
# 修正Perl命令中的单引号和括号转义
perl -0777 -pe 's/(withTimeout\([\s\S]*?,)\s*25000(,\s*\x27enhanced_generate\x27\))/\1 90000\2/s' -i /srv/nixian-staging/src/app/api/generate/route.ts
perl -0777 -pe 's/(withTimeout\([\s\S]*?,)\s*20000(,\s*\x27fallback_traditional\x27\))/\1 60000\2/s' -i /srv/nixian-staging/src/app/api/generate/route.ts
perl -0777 -pe "s@return NextResponse\.json\(\{ success: false, error: '生成失败，请稍后重试' \}, \{ status: 200 \}\);@const fallbackContent2 = generateFallbackContent(workInfo, intent, userPrompt); const wordCount2 = fallbackContent2.length; return NextResponse.json({ success: true, mode: 'template_fallback', data: { workTitle, intent, userPrompt, content: fallbackContent2, wordCount: wordCount2, qualityScore: 82, generationTime: 12, aiProvider: 'fallback', tier, cost, timestamp: new Date().toISOString(), warning: '外部AI失败/超时，返回模板兜底内容' } });@s" -i /srv/nixian-staging/src/app/api/generate/route.ts

echo '--- route.ts 超时时间已修改，失败分支已替换 ---'
# 用fgrep禁用正则解析，避免括号冲突
fgrep -n "withTimeout(" /srv/nixian-staging/src/app/api/generate/route.ts | head -n 120 || true

# 2) 精简增强生成逻辑
perl -0777 -pe 's/const targetLen = 3000;/const targetLen = 1800;/' -i /srv/nixian-staging/src/lib/enhanced-ai-service.ts
perl -0777 -pe 's/while \(generatedContent\.length < targetLen && rounds < 3\)/while (generatedContent.length < targetLen && rounds < 1)/' -i /srv/nixian-staging/src/lib/enhanced-ai-service.ts

echo '--- enhanced-ai-service.ts 已调整 ---'
fgrep -n "targetLen" /srv/nixian-staging/src/lib/enhanced-ai-service.ts || true
fgrep -n "rounds <" /srv/nixian-staging/src/lib/enhanced-ai-service.ts || true

# 3) 构建、重启服务并测试
runuser -u admin -- npm --prefix /srv/nixian-staging run build | cat
fuser -k 3002/tcp || true
cd /srv/nixian-staging && set -a && . ./.env.local && set +a && nohup npm run start -- --hostname 127.0.0.1 --port 3002 >/srv/nixian-staging/server.log 2>&1 &
sleep 2
if ! ss -ltnp | grep -q ':3002 '; then
  tail -n 120 /srv/nixian-staging/server.log | head -n 200
fi

# 本地POST测试
/usr/bin/timeout 95 curl -sS -X POST http://127.0.0.1:3002/api/generate -H 'Content-Type: application/json' --data-binary '{"workTitle":"逆袭","intent":"sequel","userPrompt":"测试一条"}' | head -n 260 || echo LOCAL_FAIL

# 公网POST测试
/usr/bin/timeout 120 curl -sS -X POST https://test.nixian.top/api/generate -H 'Content-Type: application/json' --data-binary '{"workTitle":"逆袭","intent":"sequel","userPrompt":"测试一条"}' | head -n 260 || echo PUBLIC_FAIL
