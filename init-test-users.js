const fs = require('fs');
const path = require('path');

// 初始化测试用户数据
const initTestUsers = () => {
  const dataDir = path.join(__dirname, 'data');
  
  // 确保数据目录存在
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // 3个测试用户
  const testUsers = [
    {
      id: 'test_001',
      phone: '13800000001',
      name: '月下守护者',
      credits: 30,
      fragmentBalance: 15,
      equippedTitles: ['月下守护者', '心动体验官', 'AI首席调教师'],
      unlockedTitles: ['月下守护者', '心动体验官', 'AI首席调教师', '逆线爆肝王', '意难平终结者'],
      hashedPassword: 'test123456', // 简化的密码
      createdAt: new Date().toISOString(),
      inviteCode: 'INV001',
      lastActiveAt: new Date().toISOString(),
      totalGenerations: 25,
      totalInvites: 5
    },
    {
      id: 'test_002', 
      phone: '13800000002',
      name: '荣耀守护者',
      credits: 50,
      fragmentBalance: 25,
      equippedTitles: ['荣耀守护者', '小醋包饲养员', '逆袭首席军师'],
      unlockedTitles: ['荣耀守护者', '小醋包饲养员', '逆袭首席军师', '雷朋显微镜学家', '导演级Prompt工程师'],
      hashedPassword: 'test123456',
      createdAt: new Date().toISOString(),
      inviteCode: 'INV002',
      lastActiveAt: new Date().toISOString(),
      totalGenerations: 45,
      totalInvites: 12
    },
    {
      id: 'test_003',
      phone: '13800000003', 
      name: '永恒共建者',
      credits: 100,
      fragmentBalance: 50,
      equippedTitles: ['永恒共建者', '池畏宿命连接者', '逆爱金牌制片人'],
      unlockedTitles: ['永恒共建者', '池畏宿命连接者', '逆爱金牌制片人', '服务器续命官', '站长的鸡腿投喂员'],
      hashedPassword: 'test123456',
      createdAt: new Date().toISOString(),
      inviteCode: 'INV003',
      lastActiveAt: new Date().toISOString(),
      totalGenerations: 88,
      totalInvites: 25
    }
  ];

  // 写入用户数据
  const usersFile = path.join(dataDir, 'users.json');
  let existingUsers = {};
  if (fs.existsSync(usersFile)) {
    try {
      existingUsers = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
    } catch (e) {
      existingUsers = {};
    }
  }

  // 添加测试用户
  testUsers.forEach(user => {
    existingUsers[user.id] = user;
  });

  fs.writeFileSync(usersFile, JSON.stringify(existingUsers, null, 2));

  // 创建认证数据
  const authFile = path.join(dataDir, 'auth.json');
  let existingAuth = {};
  if (fs.existsSync(authFile)) {
    try {
      existingAuth = JSON.parse(fs.readFileSync(authFile, 'utf8'));
    } catch (e) {
      existingAuth = {};
    }
  }

  testUsers.forEach(user => {
    existingAuth[`phone:${user.phone}`] = {
      userId: user.id,
      hashedPassword: user.hashedPassword
    };
  });

  fs.writeFileSync(authFile, JSON.stringify(existingAuth, null, 2));

  // 创建OTP数据（模拟验证码已验证）
  const otpFile = path.join(dataDir, 'otp.json');
  const otpData = {};
  testUsers.forEach(user => {
    otpData[user.phone] = {
      code: '123456',
      expiresAt: Date.now() + 10 * 60 * 1000 // 10分钟后过期
    };
  });
  fs.writeFileSync(otpFile, JSON.stringify(otpData, null, 2));

  console.log('✅ 测试用户创建成功！');
  console.log('📱 测试账号信息：');
  testUsers.forEach((user, index) => {
    console.log(`${index + 1}. 手机号：${user.phone} | 密码：test123456 | 验证码：123456`);
    console.log(`   用户名：${user.name} | 可用章节：${user.credits} | 灵感碎片：${user.fragmentBalance}`);
    console.log(`   已装备头衔：${user.equippedTitles.join(', ')}`);
    console.log('');
  });
};

// 运行初始化
initTestUsers(); 