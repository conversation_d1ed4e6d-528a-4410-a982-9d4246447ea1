const { createClient } = require('@supabase/supabase-js');

// 设置环境变量以便测试
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://bhbzfbryaehofeafplcu.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoYnpmYnJ5YWVob2ZlYWZwbGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTA4MTM0NSwiZXhwIjoyMDcwNjU3MzQ1fQ.tnwhVuvkxRIBM2epa8X815smk14ySjceK1BOL1jM4V8';

console.log('Testing Supabase search function directly...');

async function testSupabaseSearch() {
  console.log('Testing Supabase search function...');
  
  try {
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
    
    const workTitle = '逆袭';
    const userQuery = '池骋';
    const topK = 5;
    
    // 使用Supabase全文搜索功能
    const { data, error } = await supabase
      .from('book_chunks')
      .select('id, work_title, chapter_no, chapter_title, chunk_text')
      .eq('work_title', workTitle)
      .textSearch('fts', userQuery, {
        type: 'websearch'
      })
      .limit(topK * 2); // 获取更多结果以便后续处理

    if (error) {
      console.error('Error searching evidence from Supabase:', error);
      return;
    }

    console.log(`Found ${data.length} items:`);
    data.slice(0, 5).forEach((item, index) => {
      console.log(`Item ${index + 1}:`, {
        id: item.id,
        chapter_no: item.chapter_no,
        chapter_title: item.chapter_title,
        chunk_text_preview: item.chunk_text.substring(0, 100) + '...'
      });
    });
  } catch (error) {
    console.error('Error testing Supabase search:', error);
  }
}

testSupabaseSearch();