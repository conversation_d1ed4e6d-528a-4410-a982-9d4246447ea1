const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://bhbzfbryaehofeafplcu.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoYnpmYnJ5YWVob2ZlYWZwbGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTA4MTM0NSwiZXhwIjoyMDcwNjU3MzQ1fQ.tnwhVuvkxRIBM2epa8X815smk14ySjceK1BOL1jM4V8';

console.log('Testing Supabase connection...');
console.log('URL:', SUPABASE_URL);

try {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  console.log('Supabase client created successfully');
  
  // 测试连接
  supabase.from('works').select('id', { count: 'exact' })
    .then(({ data, error, count }) => {
      if (error) {
        console.error('Supabase query error:', error);
      } else {
        console.log('Supabase connection test successful, count:', count);
      }
    })
    .catch(err => {
      console.error('Supabase connection test failed:', err);
    });
} catch (err) {
  console.error('Failed to create Supabase client:', err);
}