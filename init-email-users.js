const fs = require('fs');
const path = require('path');

// 初始化邮箱测试用户数据
const initEmailUsers = () => {
  const dataDir = path.join(__dirname, 'data');
  
  // 确保数据目录存在
  if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
  }

  // 3个邮箱测试用户 - 全新用户，没有头衔
  const testUsers = [
    {
      id: 'email_test_001',
      contactType: 'email',
      contact: '<EMAIL>',
      name: '测试用户001',
      credits: 100, // 新用户赠送10章节
      fragmentBalance: 0,
      equippedTitles: [], // 新用户没有头衔
      unlockedTitles: [], // 新用户没有头衔
      passwordHash: '$2b$10$example_hash_for_test123', // 密码: test123
      createdAt: new Date().toISOString(),
      inviteCode: 'ETEST001',
      lastLoginAt: new Date().toISOString(),
      fragmentCount: 0,
      inviteCount: 0,
      generateCount: 0,
      checkinStreak: 0,
      totalRecharge: 0,
      donationCount: 0,
      gachaTickets: 0,
      gachaHistory: []
    },
    {
      id: 'email_test_002', 
      contactType: 'email',
      contact: '<EMAIL>',
      name: '测试用户002',
      credits: 100,
      fragmentBalance: 0,
      equippedTitles: [],
      unlockedTitles: [],
      passwordHash: '$2b$10$example_hash_for_test123',
      createdAt: new Date().toISOString(),
      inviteCode: 'ETEST002',
      lastLoginAt: new Date().toISOString(),
      fragmentCount: 0,
      inviteCount: 0,
      generateCount: 0,
      checkinStreak: 0,
      totalRecharge: 0,
      donationCount: 0,
      gachaTickets: 0,
      gachaHistory: []
    },
    {
      id: 'email_test_003',
      contactType: 'email',
      contact: '<EMAIL>',
      name: '测试用户003',
      credits: 100,
      fragmentBalance: 0,
      equippedTitles: [],
      unlockedTitles: [],
      passwordHash: '$2b$10$example_hash_for_test123',
      createdAt: new Date().toISOString(),
      inviteCode: 'ETEST003',
      lastLoginAt: new Date().toISOString(),
      fragmentCount: 0,
      inviteCount: 0,
      generateCount: 0,
      checkinStreak: 0,
      totalRecharge: 0,
      donationCount: 0,
      gachaTickets: 0,
      gachaHistory: []
    }
  ];

  // 写入用户数据
  const usersFile = path.join(dataDir, 'users.json');
  let existingUsers = {};
  if (fs.existsSync(usersFile)) {
    try {
      existingUsers = JSON.parse(fs.readFileSync(usersFile, 'utf8'));
    } catch (e) {
      existingUsers = {};
    }
  }

  // 清除之前的测试用户，添加新的测试用户
  Object.keys(existingUsers).forEach(key => {
    if (key.startsWith('email_test_') || key.startsWith('test_')) {
      delete existingUsers[key];
    }
  });

  testUsers.forEach(user => {
    existingUsers[user.id] = user;
  });

  fs.writeFileSync(usersFile, JSON.stringify(existingUsers, null, 2));

  // 创建认证数据
  const authFile = path.join(dataDir, 'auth.json');
  let existingAuth = {};
  if (fs.existsSync(authFile)) {
    try {
      existingAuth = JSON.parse(fs.readFileSync(authFile, 'utf8'));
    } catch (e) {
      existingAuth = {};
    }
  }

  // 清除之前的测试认证数据
  Object.keys(existingAuth).forEach(key => {
    if (key.includes('test')) {
      delete existingAuth[key];
    }
  });

  testUsers.forEach(user => {
    existingAuth[`email:${user.email}`] = {
      userId: user.id,
      hashedPassword: user.hashedPassword
    };
  });

  fs.writeFileSync(authFile, JSON.stringify(existingAuth, null, 2));

  // 创建OTP数据（模拟验证码：123456，有效期很长）
  const otpFile = path.join(dataDir, 'email_otp.json');
  const otpData = {};
  testUsers.forEach(user => {
    otpData[user.contact] = {
      code: '123456',
      expiresAt: Date.now() + 60 * 60 * 1000 // 1小时后过期
    };
  });
  fs.writeFileSync(otpFile, JSON.stringify(otpData, null, 2));

  console.log('✅ 邮箱测试用户创建成功！');
  console.log('📧 测试账号信息：');
  testUsers.forEach((user, index) => {
    console.log(`${index + 1}. 邮箱：${user.contact} | 密码：test123 | 验证码：123456`);
    console.log(`   用户名：${user.name} | 可用章节：${user.credits} | 灵感碎片：${user.fragmentBalance}`);
    console.log(`   头衔状态：全新用户，无任何头衔`);
    console.log('');
  });
  
  console.log('💡 使用说明：');
  console.log('1. 可以使用邮箱 + 验证码登录，验证码固定为 123456');
  console.log('2. 也可以使用邮箱 + 密码登录，密码为 test123');
  console.log('3. 这些用户没有任何头衔，可以通过管理员后台添加');
};

// 运行初始化
initEmailUsers(); 