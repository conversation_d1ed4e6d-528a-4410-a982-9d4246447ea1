#!/usr/bin/env bash
set -euo pipefail
cd /srv/nixian-staging
chown -R admin:admin /srv/nixian-staging
if [ -f package-lock.json ]; then
  runuser -u admin -- bash -lc 'cd /srv/nixian-staging && npm ci'
else
  runuser -u admin -- bash -lc 'cd /srv/nixian-staging && npm install'
fi
runuser -u admin -- bash -lc 'cd /srv/nixian-staging && NODE_ENV=production npm run build | tee -a /srv/nixian-staging/server.log'
systemctl restart nixian-staging.service
sleep 2
ss -ltnp | grep ':3002 ' || true
printf n--
