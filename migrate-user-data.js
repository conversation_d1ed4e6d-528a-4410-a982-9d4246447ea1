const fs = require('fs');
const path = require('path');

// 读取用户数据
const usersPath = path.join(__dirname, 'data', 'users.json');
const users = JSON.parse(fs.readFileSync(usersPath, 'utf8'));

console.log(`开始迁移 ${users.length} 个用户的数据...`);

// 迁移用户数据
const migratedUsers = users.map(user => {
  const migratedUser = { ...user };
  
  // 添加灵感碎片余额字段
  if (typeof migratedUser.fragmentBalance !== 'number') {
    migratedUser.fragmentBalance = 0;
  }
  
  // 添加邀请码字段（如果没有）
  if (!migratedUser.inviteCode) {
    migratedUser.inviteCode = Date.now().toString(36) + Math.random().toString(36).slice(2, 12);
  }
  
  // 添加头衔相关字段
  if (!migratedUser.unlockedTitles) {
    migratedUser.unlockedTitles = [];
  }
  
  if (!migratedUser.equippedTitles) {
    migratedUser.equippedTitles = [];
  }
  
  // 添加统计字段
  if (typeof migratedUser.inviteCount !== 'number') {
    migratedUser.inviteCount = 0;
  }
  
  if (typeof migratedUser.generateCount !== 'number') {
    migratedUser.generateCount = 0;
  }
  
  if (typeof migratedUser.checkinStreak !== 'number') {
    migratedUser.checkinStreak = 0;
  }
  
  if (!migratedUser.lastCheckinDate) {
    migratedUser.lastCheckinDate = null;
  }
  
  return migratedUser;
});

// 备份原文件
const backupPath = path.join(__dirname, 'data', `users_backup_${Date.now()}.json`);
fs.writeFileSync(backupPath, JSON.stringify(users, null, 2));
console.log(`原数据已备份到: ${backupPath}`);

// 写入迁移后的数据
fs.writeFileSync(usersPath, JSON.stringify(migratedUsers, null, 2));
console.log('用户数据迁移完成！');

// 创建空的内容和互动数据文件
const contentPath = path.join(__dirname, 'data', 'content_cards.json');
if (!fs.existsSync(contentPath)) {
  fs.writeFileSync(contentPath, '[]');
  console.log('创建了空的 content_cards.json 文件');
}

const interactionsPath = path.join(__dirname, 'data', 'user_interactions.json');
if (!fs.existsSync(interactionsPath)) {
  fs.writeFileSync(interactionsPath, '[]');
  console.log('创建了空的 user_interactions.json 文件');
}

// 显示迁移结果统计
console.log('\n迁移统计:');
console.log(`- 总用户数: ${migratedUsers.length}`);
console.log(`- 添加了fragmentBalance字段的用户: ${migratedUsers.filter(u => users.find(orig => orig.id === u.id && typeof orig.fragmentBalance !== 'number')).length}`);
console.log(`- 添加了inviteCode字段的用户: ${migratedUsers.filter(u => users.find(orig => orig.id === u.id && !orig.inviteCode)).length}`);
console.log(`- 添加了头衔字段的用户: ${migratedUsers.filter(u => users.find(orig => orig.id === u.id && !orig.unlockedTitles)).length}`);
console.log('\n迁移完成！可以删除此脚本文件。'); 