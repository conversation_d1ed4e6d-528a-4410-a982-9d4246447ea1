#!/bin/bash

# 清理3004端口上的所有进程
echo "正在检查3004端口占用情况..."
PIDS=$(ss -tlnp | grep :3004 | grep -o 'pid=[0-9]*' | cut -d'=' -f2)

if [ ! -z "$PIDS" ]; then
    echo "发现占用3004端口的进程: $PIDS"
    for pid in $PIDS; do
        echo "终止进程 $pid"
        kill -9 $pid 2>/dev/null
    done
    sleep 2
else
    echo "3004端口未被占用"
fi

# 再次检查确保端口已释放
REMAINING=$(ss -tlnp | grep :3004)
if [ ! -z "$REMAINING" ]; then
    echo "警告: 仍有进程占用3004端口:"
    echo "$REMAINING"
    exit 1
fi

echo "3004端口已清理完毕，启动服务器..."

# 使用开发模式在后台启动网站
echo "正在使用开发模式在后台启动网站..."
cd /srv/nixian-v2.2/api
nohup npm run dev -- -p 3004 > nohup.out 2>&1 &

# 输出进程ID以便后续管理
NEXTJS_PID=$!
echo "网站已在后台启动，进程ID: $NEXTJS_PID"
echo "可以通过 tail -f nohup.out 查看日志"
echo "通过 kill -9 $NEXTJS_PID 停止服务"
echo "或使用 pkill -f \"next dev\" 停止所有Next.js开发服务器"

# 保存进程ID到文件以便后续管理
echo $NEXTJS_PID > .pid
echo "进程ID已保存到 .pid 文件"