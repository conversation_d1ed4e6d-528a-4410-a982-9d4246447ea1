<svg width="300" height="200" viewBox="0 0 300 200" xmlns="http://www.w3.org/2000/svg">
  <!-- 莫比乌斯环形式的走之底 -->
  <path d="M80,100 C80,60 140,60 160,90 C180,120 160,150 120,150 C80,150 60,120 60,100 C60,80 70,60 100,60 C130,60 160,90 160,120 C160,150 120,180 80,180 C40,180 20,140 40,100 C60,60 100,40 140,60" 
        fill="none" stroke="url(#mobiusGradient)" stroke-width="6" stroke-linecap="round" />
  
  <!-- 逆字的简化部分 -->
  <rect x="180" y="80" width="40" height="15" fill="#666BCE" />
  <rect x="180" y="105" width="25" height="15" fill="#666BCE" />
  <rect x="205" y="130" width="15" height="30" fill="#FFD64F" />
  
  <!-- 文字 "线" -->
  <text x="240" y="140" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#18B7F6">线</text>
  
  <!-- 渐变定义 -->
  <defs>
    <linearGradient id="mobiusGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#666BCE" />
      <stop offset="50%" stop-color="#18B7F6" />
      <stop offset="100%" stop-color="#FFD64F" />
    </linearGradient>
  </defs>
</svg>
    