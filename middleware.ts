import { NextResponse, type NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const url = request.nextUrl;
  if (url.pathname === '/recharge' && !url.searchParams.has('v')) {
    const redirectUrl = new URL(url.toString());
    redirectUrl.searchParams.set('v', String(Date.now()));
    return NextResponse.redirect(redirectUrl);
  }
  if (url.pathname === '/auth' && !url.searchParams.has('v')) {
    const redirectUrl = new URL(url.toString());
    redirectUrl.searchParams.set('v', String(Date.now()));
    return NextResponse.redirect(redirectUrl);
  }
  return NextResponse.next();
}

export const config = { matcher: ['/recharge', '/auth'] }; 