const { createClient } = require('@supabase/supabase-js');

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://bhbzfbryaehofeafplcu.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJoYnpmYnJ5YWVob2ZlYWZwbGN1Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NTA4MTM0NSwiZXhwIjoyMDcwNjU3MzQ1fQ.tnwhVuvkxRIBM2epa8X815smk14ySjceK1BOL1jM4V8';

console.log('Verifying data import...');
console.log('Supabase URL:', SUPABASE_URL);

try {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);
  console.log('Supabase client created successfully');
  
  // 查询book_chunks表的行数
  supabase
    .from('book_chunks')
    .select('*', { count: 'exact' })
    .then(({ data, error, count }) => {
      if (error) {
        console.error('Error querying book_chunks table:', error);
      } else {
        console.log('book_chunks table row count:', count);
        console.log('Sample data:');
        if (data && data.length > 0) {
          data.slice(0, 3).forEach((row, index) => {
            console.log(`Row ${index + 1}:`, {
              id: row.id,
              work_title: row.work_title,
              chapter_no: row.chapter_no,
              chapter_title: row.chapter_title,
              chunk_text_preview: row.chunk_text.substring(0, 100) + '...'
            });
          });
        }
      }
    });
} catch (err) {
  console.error('Failed to create Supabase client:', err);
}