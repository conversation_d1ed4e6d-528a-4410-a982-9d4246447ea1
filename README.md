# 逆线 (Nixian) 网站使用指南

## 项目概述
逆线是一个AI驱动的同人创作平台，旨在通过AI技术帮助用户续写个性化的故事内容，首页为3D沉浸式粒子宇宙。

## 技术栈
- **前端**: Next.js 14.2.5 + React 18 + TypeScript + Tailwind CSS
- **3D效果**: Three.js + React Three Fiber
- **UI组件**: Lucide React 图标
- **数据库**: 模拟数据库（JSON文件，生产环境可替换为 Supabase）
- **认证**: 简化版认证系统

## 项目结构
```
nixian-v2.2/api/
├── src/
│   ├── app/
│   │   ├── page.tsx                  # 首页
│   │   ├── intent/page.tsx           # 意图选择页
│   │   ├── generate/page.tsx         # 生成结果页
│   │   ├── echo/page.tsx             # 实时弹幕页
│   │   ├── forum/page.tsx            # 创作社区页
│   │   ├── gallery/page.tsx          # 光影回廊页
│   │   ├── auth/page.tsx             # 登录注册页
│   │   ├── recharge/page.tsx         # 积分充值页
│   │   ├── titles/page.tsx           # 用户头衔页
│   │   ├── notice/page.tsx           # 公告通知页
│   │   └── api/                      # API路由
│   │       ├── generate/route.ts     # 生成API
│   │       ├── auth/                 # 认证API
│   │       ├── echo/                 # 弹幕API
│   │       ├── posts/                # 社区帖子API
│   │       └── gallery/              # 图库API
│   ├── components/
│   │   ├── EnhancedStarfield.tsx     # 增强星空背景
│   │   ├── ParticleUniverse.tsx      # 粒子宇宙
│   │   ├── BreathingUniverse.tsx     # 呼吸宇宙
│   │   ├── ThreeScene.tsx            # Three.js场景
│   │   ├── UnifiedLayout.tsx         # 统一布局组件
│   │   └── TopNavigation.tsx         # 顶部导航栏
│   ├── lib/
│   │   ├── database.ts               # 数据库模拟
│   │   ├── ai-service.ts             # AI服务
│   │   ├── persistence/              # 数据持久化
│   │   ├── auth/                     # 认证逻辑
│   │   └── retrieval/                # 检索服务
│   └── styles/globals.css            # 全局样式
├── data/                             # JSON数据文件
└── public/                           # 静态资源
```

## 功能特性

### 🏠 首页 (/)
- **核心输入框**: 用户可以输入书名、CP名或作品URL
- **热门作品展示**: 预设热门作品一键体验
- **用户导航**: 登录/注册、积分显示、用户菜单

### 🎯 意图选择页 (/intent)
- **四种创作模式**:
  - 续写番外: 为原作续写新章节
  - 平行宇宙AU: 将角色放到新世界设定
  - 风格模仿: 用原作风格写新故事
  - 补完细节: 填补原文空白
- **智能示例**: 每种模式提供具体示例

### 📝 生成结果页 (/generate)
- **实时生成动画**: 模拟AI创作过程
- **内容预览**: 免费用户可预览部分内容
- **质量评估**: 显示生成质量、字数、耗时
- **操作功能**: 复制、分享、收藏、重新生成

### 👤 用户系统 (/auth)
- **登录注册**: 简化版认证系统
- **积分管理**: 新用户赠送10积分
- **演示账号**: 统一密码123456

### 💰 积分充值 (/pricing)
- **三种套餐**: 体验包/创作包/月度会员
- **灵活定价**: 从9.9元到39.9元
- **功能对比**: 清晰的功能差异展示

## 如何启动项目

### 1. 确保依赖已安装
```bash
cd /srv/nixian-v2.2/api
npm install
```

### 2. 启动开发服务器
```bash
cd /srv/nixian-v2.2/api
npm run dev -- -p 3004
```

### 3. 访问网站
打开浏览器访问: http://localhost:3004

### 4. 后台持续运行
如果需要在后台持续运行网站，可以使用：
```bash
cd /srv/nixian-v2.2/api
nohup npm run dev -- -p 3004 > nohup.out 2>&1 &
```

### 5. 检查运行状态
```bash
ps aux | grep "node" | grep "next"
```

### 6. 停止运行中的网站
```bash
pkill -f "next dev"
```

## 演示流程

### 完整用户体验流程:
1. **访问首页** → 查看热门作品和功能介绍
2. **注册账号** → 获得10积分的新手奖励
3. **选择作品** → 点击"魔道祖师"或输入其他作品
4. **选择创作意图** → 如"续写番外"
5. **描述场景** → 如"魏无羡第一次带蓝忘机回云梦"
6. **查看结果** → AI生成的高质量续文
7. **积分充值** → 购买更多创作机会

### 快速测试步骤:
1. 首页 → 点击"魔道祖师"卡片
2. 意图页 → 选择"续写番外"
3. 输入: "蓝忘机喝醉后的故事"
4. 点击"立即生成" → 查看AI生成内容

## API端点

- `POST /api/generate` - AI内容生成
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册

## 部署说明

### 开发环境部署
目前推荐使用开发模式部署，避免SWC编译问题：
```bash
npm run dev -- -p 3004
```

### 代码推送与管理
由于GitHub仓库有2GB的存储限制，推送代码时需要注意：

1. **只推送必要的代码文件**
   ```bash
   # 确保.gitignore正确配置，忽略大文件和不必要的目录
   # 典型的需要忽略的内容：node_modules/, .next/, *.pdf, *.tgz
   ```

2. **使用Git LFS管理大文件**
   对于必要的大型二进制文件，可以考虑使用Git LFS
   
3. **定期清理不必要的分支和标签**
   ```bash
   # 查看大文件
   git rev-list --objects --all | grep "$(git verify-pack -v .git/objects/pack/*.idx | sort -k 3 -n | tail -10 | awk '{print $1}')"
   
   # 清理Git历史中的大文件（谨慎使用）
   git filter-branch --force --tree-filter 'rm -f path/to/large/file' HEAD
   ```

4. **推送前检查变更大小**
   ```bash
   git diff --stat
   ```

## 注意事项

1. **SWC编译问题**: 当前在使用`npm run build`时可能遇到SWC二进制加载问题，建议使用开发模式运行
2. **演示用途**: 当前版本为演示版本，密码统一为123456
3. **数据持久化**: 使用JSON文件和localStorage，刷新页面会保留登录状态
4. **AI生成**: 目前使用模拟内容，生产环境需接入真实AI服务
5. **支付系统**: 充值功能为演示，实际需接入支付网关
6. **3D性能**: 3D效果在低配置设备上可能造成性能问题，建议添加性能检测和降级方案

## 核心特色

✨ **极简入口**: 一个输入框解决所有需求
🌌 **3D宇宙**: 沉浸式粒子宇宙和星空背景
🎨 **精美UI**: 渐变色彩、流畅动画、响应式设计  
⚡ **快速响应**: 30秒内完成内容生成
🎯 **精准理解**: 深度分析原作风格，确保不OOC
💰 **灵活付费**: 多种套餐满足不同需求

## 发展计划

1. **性能优化**: 优化3D效果在移动设备上的表现
2. **接入真实AI**: 替换模拟数据，接入实际的AI生成服务
3. **完善社区功能**: 强化用户互动和内容分享
4. **添加分析系统**: 跟踪用户行为和内容生成效果
5. **升级UI/UX**: 基于用户反馈持续改进界面体验
