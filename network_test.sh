#!/bin/bash

echo "=== 逆线网站性能诊断工具 ==="
echo ""

# 检查服务器状态
echo "🔍 检查服务器状态..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 服务器运行正常"
    
    # 测试首页加载时间
    echo "⏱️  测试首页加载时间..."
    time_start=$(date +%s.%N)
    curl -s http://localhost:3000 > /dev/null
    time_end=$(date +%s.%N)
    load_time=$(echo "$time_end - $time_start" | bc -l)
    echo "首页加载时间: ${load_time}秒"
    
    # 测试图片API
    echo "🖼️  测试图片API..."
    time_start=$(date +%s.%N)
    curl -s "http://localhost:3000/api/gallery?category=cp_main" > /dev/null
    time_end=$(date +%s.%N)
    api_time=$(echo "$time_end - $time_start" | bc -l)
    echo "图片API响应时间: ${api_time}秒"
    
else
    echo "❌ 服务器未运行，尝试启动..."
    cd /srv/nixian-staging
    npm run start &
    echo "等待服务器启动..."
    sleep 5
fi

echo ""
echo "📊 系统资源使用情况:"
echo "内存使用:"
free -h | head -2
echo ""
echo "磁盘使用:"
df -h / | tail -1
echo ""
echo "CPU负载:"
uptime

echo ""
echo "🌐 网络建议:"
echo "1. 如果加载慢，可能是VPN连接不稳定"
echo "2. 建议使用稳定的VPN节点(香港/日本)"
echo "3. 可以尝试刷新页面或清除浏览器缓存"
echo "4. 图片较多时首次加载会较慢，后续会有缓存"

echo ""
echo "⚡ 快速测试命令:"
echo "curl -w '@-' -o /dev/null -s http://localhost:3000 <<< 'time_total: %{time_total}'" 