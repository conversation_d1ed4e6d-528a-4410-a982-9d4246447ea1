const express = require("express");
const path = require("path");
const app = express();

// 创建代理中间件函数
const createProxyMiddleware = (options) => {
  const { target } = options;
  
  return (req, res) => {
    // 简单重定向
    const targetUrl = `${target}${req.url}`;
    res.writeHead(302, { 'Location': targetUrl });
    res.end();
    console.log(`[代理] ${req.url} -> ${targetUrl}`);
  };
};

// 当前使用代理路由到Next.js开发服务器
app.use('/', (req, res) => {
  // 将所有请求重定向到3004端口
  const target = 'http://localhost:3004';
  const targetUrl = `${target}${req.url}`;
  res.writeHead(302, { 'Location': targetUrl });
  res.end();
  console.log(`[代理] ${req.url} -> ${targetUrl}`);
});

const port = process.env.PORT || 3000;
app.listen(port, () => {
  console.log(`🚀 代理服务器运行在端口 ${port}`);
  console.log(`📎 转发请求到: http://localhost:3004/`);
  console.log(`🎯 请访问: http://localhost:${port}/`);
});