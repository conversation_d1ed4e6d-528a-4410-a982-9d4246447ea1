/** @type {import('next').NextConfig} */
const nextConfig = {
  // 开发环境性能优化
  swcMinify: true,
  reactStrictMode: false, // 暂时禁用严格模式以提升性能
  
  // 性能优化配置
  poweredByHeader: false,
  compress: true,
  
  // 图片优化
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  
  experimental: {
    esmExternals: false,
    optimizeCss: false, // 开发环境禁用CSS优化
    scrollRestoration: false, // 禁用以减少复杂性
  },
  
  transpilePackages: [
    'three',
    '@react-three/fiber',
    '@react-three/drei'
  ],
  
  webpack: (config, { dev, isServer }) => {
    // 开发环境优化
    if (dev) {
      config.cache = false; // 禁用缓存可能导致的问题
      config.optimization.minimize = false;
      config.optimization.splitChunks = false; // 开发环境不分块
    }
    
    // 生产环境优化
    if (!dev && !isServer) {
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
          three: {
            test: /[\\/]node_modules[\\/](three|@react-three)[\\/]/,
            name: 'three',
            chunks: 'all',
            priority: 10,
          },
        },
      };
    }
    
    config.externals.push({
      canvas: 'commonjs canvas',
    });
    
    return config;
  },
};

export default nextConfig;