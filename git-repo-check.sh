#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== 逆线(Nixian)仓库管理工具 ===${NC}"
echo

# 显示当前Git仓库的大小
echo -e "${BLUE}当前Git仓库大小:${NC}"
du -sh .git

# 显示远程仓库信息
echo -e "\n${BLUE}远程仓库信息:${NC}"
git remote -v

# 检查未提交的更改
echo -e "\n${BLUE}未提交的更改:${NC}"
git status -s

# 查找最大的文件
echo -e "\n${BLUE}仓库中最大的10个文件:${NC}"
find . -type f -not -path "./.git/*" -exec du -h {} \; | sort -rh | head -n 10

# 查找Git历史中的大文件
echo -e "\n${BLUE}Git历史中的大文件:${NC}"
git rev-list --objects --all | grep "$(git verify-pack -v .git/objects/pack/*.idx | sort -k 3 -n | tail -10 | awk '{print $1}')"

# 显示文件类型统计
echo -e "\n${BLUE}文件类型统计:${NC}"
find . -type f -not -path "./.git/*" | grep -v "node_modules" | grep -o "\.[^\.]*$" | sort | uniq -c | sort -nr | head -n 10

# 显示近期提交
echo -e "\n${BLUE}最近5次提交:${NC}"
git log -5 --pretty=format:"%h - %an, %ar : %s"

# 建议
echo -e "\n${YELLOW}=== 管理建议 ===${NC}"
echo -e "1. ${GREEN}忽略大文件${NC}: 确保.gitignore正确配置，忽略node_modules/, .next/, build/"
echo -e "2. ${GREEN}使用Git LFS${NC}: 对于必要的大文件，考虑使用Git Large File Storage"
echo -e "3. ${GREEN}清理大文件${NC}: 使用 git filter-branch 或 BFG Repo-Cleaner 清理历史中的大文件"
echo -e "4. ${GREEN}拆分仓库${NC}: 考虑将代码和资源分成不同的仓库"
echo -e "5. ${GREEN}减少二进制文件${NC}: 将图片、PDF等文件存储在外部服务上"

echo
echo -e "${BLUE}=== 完成 ===${NC}"