import type { NextConfig } from "next";

const securityHeaders = [
  { key: 'X-Content-Type-Options', value: 'nosniff' },
  { key: 'Referrer-Policy', value: 'strict-origin-when-cross-origin' },
  { key: 'X-Frame-Options', value: 'SAMEORIGIN' },
  { key: 'Permissions-Policy', value: 'geolocation=(), microphone=(), camera=()' },
];

const nextConfig: NextConfig = {
  poweredByHeader: false,
  productionBrowserSourceMaps: false,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  // 开发环境性能优化
  ...(process.env.NODE_ENV === 'development' && {
    onDemandEntries: {
      // 页面在内存中保持的时间 (默认60秒)
      maxInactiveAge: 60 * 1000,
      // 同时保持的页面数量 (默认25)
      pagesBufferLength: 10,
    },
  }),
  async headers() {
    return [
      { source: '/(.*)', headers: securityHeaders },
      { source: '/recharge', headers: [ { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, max-age=0' } ] },
      { source: '/auth', headers: [ { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, max-age=0' } ] },
      { source: '/admin', headers: [ { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, max-age=0' } ] },
      // 开发环境下允许静态资源缓存以提升性能
      ...(process.env.NODE_ENV === 'production' ? [
        { source: '/_next/static/(.*)', headers: [ { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, max-age=0' } ] },
        { source: '/_next/(.*)', headers: [ { key: 'Cache-Control', value: 'no-store, no-cache, must-revalidate, max-age=0' } ] },
      ] : []),
    ];
  },
};

export default nextConfig;
